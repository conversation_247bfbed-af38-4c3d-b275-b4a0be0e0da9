package main

import (
	"os"

	"github.com/mubeng/mubeng/internal/runner"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/common/errors"
)

func main() {
	opt := runner.Options()

	if err := runner.New(opt); err != nil {
		// 使用主模块logger记录致命错误
		mainLogger := logger.GetMainLogger()

		// 根据错误类型提供不同的退出码
		exitCode := getExitCodeFromError(err)
		mainLogger.GetRawLogger().Errorf("程序退出，退出码: %d", exitCode)

		// 使用os.Exit确保程序以指定退出码退出
		os.Exit(exitCode)
	}
}

// getExitCodeFromError 根据错误类型返回相应的退出码
func getExitCodeFromError(err error) int {
	// 导入错误包以进行类型检查
	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		switch flexErr.Type {
		case errors.ErrTypeConfig, errors.ErrTypeValidation:
			return 2 // 配置错误
		case errors.ErrTypeFile:
			return 3 // 文件错误
		case errors.ErrTypeNetwork:
			return 4 // 网络错误
		case errors.ErrTypeProxy:
			return 5 // 代理错误
		default:
			return 1 // 通用错误
		}
	}
	return 1 // 未知错误
}
