# FlexProxy 旧接口清理和重构计划

## 📋 清理目标

### 1. 标记为"旧定义"的代码
- `internal/trigger/trigger.go` 第1215行：`OldRequestBodyTrigger` 结构体
- 注释中包含"旧的定义"的代码

### 2. 废弃的初始化代码
- `internal/server/init.go` 第9行：使用了废弃的 `rand.Seed()` 方法

### 3. 不一致的命名规范
- 各种日志调用格式不统一
- 错误处理方式不一致

### 4. 重复或冗余的代码
- 多处相似的清理逻辑（cleanExpiredBans、cleanupExpiredBans等）
- 重复的IP提取逻辑

## 🎯 清理步骤

### 步骤1：移除OldRequestBodyTrigger
- 删除 `OldRequestBodyTrigger` 结构体及其方法
- 确保没有其他代码引用此结构体

### 步骤2：修复废弃的rand.Seed
- 更新为Go 1.20+推荐的随机数初始化方式

### 步骤3：统一命名规范
- 统一日志调用格式
- 统一错误处理模式
- 统一方法命名规范

### 步骤4：清理重复代码
- 提取通用的清理逻辑
- 统一IP提取方法
- 合并相似的功能函数

## 🔍 清理验证
- 编译测试通过
- 所有单元测试通过
- 功能验证正常
- 代码质量检查通过
