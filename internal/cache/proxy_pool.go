// proxy_pool.go - 优化的代理连接池管理
package cache

import (
	"fmt"
	"net"
	"net/http"
	"net/url"
	"sync"
	"sync/atomic"
	"time"

	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/errors"
)

// ProxyConnection 代理连接
type ProxyConnection struct {
	URL         *url.URL      // 代理URL
	Transport   *http.Transport // HTTP传输
	CreatedAt   time.Time     // 创建时间
	LastUsed    time.Time     // 最后使用时间
	UseCount    int64         // 使用次数
	ErrorCount  int64         // 错误次数
	Latency     time.Duration // 平均延迟
	IsHealthy   bool          // 健康状态
	MaxIdleTime time.Duration // 最大空闲时间
}

// IsExpired 检查连接是否过期
func (pc *ProxyConnection) IsExpired() bool {
	return time.Since(pc.LastUsed) > pc.MaxIdleTime
}

// UpdateLatency 更新延迟统计
func (pc *ProxyConnection) UpdateLatency(latency time.Duration) {
	if pc.Latency == 0 {
		pc.Latency = latency
	} else {
		// 使用指数移动平均计算延迟
		pc.Latency = time.Duration(float64(pc.Latency)*(1-constants.LatencySmoothingFactor) + float64(latency)*constants.LatencySmoothingFactor)
	}
}

// ProxyPool 代理连接池
type ProxyPool struct {
	mu              sync.RWMutex                    // 读写锁
	connections     map[string]*ProxyConnection     // 连接映射 (proxy_url -> connection)
	maxConnections  int                             // 最大连接数
	maxIdleTime     time.Duration                   // 最大空闲时间
	healthCheckTick time.Duration                   // 健康检查间隔
	stopHealthCheck chan struct{}                   // 停止健康检查信号
	stats           ProxyPoolStats                  // 连接池统计
	loadBalancer    LoadBalancer                    // 负载均衡器
}

// ProxyPoolStats 连接池统计信息
type ProxyPoolStats struct {
	TotalConnections   int64 // 总连接数
	ActiveConnections  int64 // 活跃连接数
	IdleConnections    int64 // 空闲连接数
	ConnectionHits     int64 // 连接命中次数
	ConnectionMisses   int64 // 连接未命中次数
	ConnectionErrors   int64 // 连接错误次数
	HealthCheckCount   int64 // 健康检查次数
	UnhealthyProxies   int64 // 不健康代理数
}

// LoadBalancer 负载均衡器接口
type LoadBalancer interface {
	SelectProxy(proxies []*ProxyConnection) *ProxyConnection
}

// RoundRobinBalancer 轮询负载均衡器
type RoundRobinBalancer struct {
	counter int64
}

// SelectProxy 轮询选择代理
func (rb *RoundRobinBalancer) SelectProxy(proxies []*ProxyConnection) *ProxyConnection {
	if len(proxies) == 0 {
		return nil
	}
	index := atomic.AddInt64(&rb.counter, 1) % int64(len(proxies))
	return proxies[index]
}

// WeightedBalancer 加权负载均衡器（基于延迟和错误率）
type WeightedBalancer struct{}

// SelectProxy 基于权重选择代理
func (wb *WeightedBalancer) SelectProxy(proxies []*ProxyConnection) *ProxyConnection {
	if len(proxies) == 0 {
		return nil
	}

	// 计算每个代理的权重分数（延迟越低、错误率越低，分数越高）
	var bestProxy *ProxyConnection
	var bestScore float64

	for _, proxy := range proxies {
		if !proxy.IsHealthy {
			continue
		}

		// 计算错误率
		errorRate := float64(proxy.ErrorCount) / float64(proxy.UseCount+1)
		
		// 计算延迟分数（延迟越低分数越高）
		latencyScore := 1.0 / (float64(proxy.Latency.Milliseconds()) + 1)
		
		// 综合分数（错误率权重0.6，延迟权重0.4）
		score := (1.0-errorRate)*constants.ErrorRateWeight + latencyScore*constants.LatencyWeight

		if bestProxy == nil || score > bestScore {
			bestProxy = proxy
			bestScore = score
		}
	}

	// 如果没有健康的代理，返回第一个
	if bestProxy == nil && len(proxies) > 0 {
		return proxies[0]
	}

	return bestProxy
}

// NewProxyPool 创建新的代理连接池
func NewProxyPool(maxConnections int, maxIdleTime time.Duration, balancerType string) *ProxyPool {
	if maxConnections <= 0 {
		maxConnections = 100 // 默认最大100个连接
	}
	if maxIdleTime <= 0 {
		maxIdleTime = 5 * time.Minute // 默认5分钟空闲时间
	}

	var balancer LoadBalancer
	switch balancerType {
	case "weighted":
		balancer = &WeightedBalancer{}
	default:
		balancer = &RoundRobinBalancer{}
	}

	pool := &ProxyPool{
		connections:     make(map[string]*ProxyConnection),
		maxConnections:  maxConnections,
		maxIdleTime:     maxIdleTime,
		healthCheckTick: 30 * time.Second, // 30秒健康检查间隔
		stopHealthCheck: make(chan struct{}),
		loadBalancer:    balancer,
	}

	// 启动后台健康检查
	go pool.startHealthCheck()

	return pool
}

// GetConnection 获取代理连接
func (pp *ProxyPool) GetConnection(proxyURL string) (*ProxyConnection, error) {
	pp.mu.RLock()
	conn, exists := pp.connections[proxyURL]
	pp.mu.RUnlock()

	if exists && !conn.IsExpired() && conn.IsHealthy {
		// 更新使用时间和计数
		conn.LastUsed = time.Now()
		atomic.AddInt64(&conn.UseCount, 1)
		atomic.AddInt64(&pp.stats.ConnectionHits, 1)
		return conn, nil
	}

	atomic.AddInt64(&pp.stats.ConnectionMisses, 1)

	// 连接不存在或已过期，创建新连接
	return pp.createConnection(proxyURL)
}

// createConnection 创建新的代理连接
func (pp *ProxyPool) createConnection(proxyURL string) (*ProxyConnection, error) {
	pp.mu.Lock()
	defer pp.mu.Unlock()

	// 双重检查，防止并发创建
	if conn, exists := pp.connections[proxyURL]; exists && !conn.IsExpired() && conn.IsHealthy {
		conn.LastUsed = time.Now()
		atomic.AddInt64(&conn.UseCount, 1)
		return conn, nil
	}

	// 检查连接池大小限制
	if len(pp.connections) >= pp.maxConnections {
		pp.evictLRU()
	}

	// 解析代理URL
	parsedURL, err := url.Parse(proxyURL)
	if err != nil {
		return nil, errors.NewErrorWithDetails(errors.ErrTypeCache, errors.ErrCodeCacheOperationFailed, "无效的代理URL", fmt.Sprintf("代理地址: %s, 错误: %v", proxyURL, err))
	}

	// 创建HTTP传输
	transport := &http.Transport{
		Proxy: http.ProxyURL(parsedURL),
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   10,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	now := time.Now()
	conn := &ProxyConnection{
		URL:         parsedURL,
		Transport:   transport,
		CreatedAt:   now,
		LastUsed:    now,
		UseCount:    1,
		ErrorCount:  0,
		Latency:     0,
		IsHealthy:   true,
		MaxIdleTime: pp.maxIdleTime,
	}

	pp.connections[proxyURL] = conn
	atomic.AddInt64(&pp.stats.TotalConnections, 1)

	return conn, nil
}

// SelectProxy 使用负载均衡器选择代理
func (pp *ProxyPool) SelectProxy(proxyURLs []string) (*ProxyConnection, error) {
	if len(proxyURLs) == 0 {
		return nil, errors.NewErrorWithDetails(errors.ErrTypeCache, errors.ErrCodeCacheKeyNotFound, "未提供代理URL", "代理URL列表为空")
	}

	pp.mu.RLock()
	var availableProxies []*ProxyConnection
	for _, proxyURL := range proxyURLs {
		if conn, exists := pp.connections[proxyURL]; exists && conn.IsHealthy && !conn.IsExpired() {
			availableProxies = append(availableProxies, conn)
		}
	}
	pp.mu.RUnlock()

	// 如果没有可用的连接，尝试创建第一个
	if len(availableProxies) == 0 {
		return pp.GetConnection(proxyURLs[0])
	}

	// 使用负载均衡器选择代理
	selected := pp.loadBalancer.SelectProxy(availableProxies)
	if selected != nil {
		selected.LastUsed = time.Now()
		atomic.AddInt64(&selected.UseCount, 1)
		atomic.AddInt64(&pp.stats.ConnectionHits, 1)
	}

	return selected, nil
}

// RecordError 记录代理连接错误
func (pp *ProxyPool) RecordError(proxyURL string, err error) {
	pp.mu.RLock()
	conn, exists := pp.connections[proxyURL]
	pp.mu.RUnlock()

	if exists {
		atomic.AddInt64(&conn.ErrorCount, 1)
		atomic.AddInt64(&pp.stats.ConnectionErrors, 1)

		// 如果错误率过高，标记为不健康
		errorRate := float64(conn.ErrorCount) / float64(conn.UseCount+1)
		if errorRate > constants.HighErrorRateThreshold { // 错误率超过50%
			conn.IsHealthy = false
			atomic.AddInt64(&pp.stats.UnhealthyProxies, 1)
		}
	}
}

// RecordLatency 记录代理连接延迟
func (pp *ProxyPool) RecordLatency(proxyURL string, latency time.Duration) {
	pp.mu.RLock()
	conn, exists := pp.connections[proxyURL]
	pp.mu.RUnlock()

	if exists {
		conn.UpdateLatency(latency)
	}
}

// evictLRU 驱逐最少使用的连接
func (pp *ProxyPool) evictLRU() {
	var oldestKey string
	var oldestTime time.Time
	var lowestUseCount int64 = -1

	for key, conn := range pp.connections {
		if lowestUseCount == -1 || conn.UseCount < lowestUseCount ||
			(conn.UseCount == lowestUseCount && (oldestTime.IsZero() || conn.LastUsed.Before(oldestTime))) {
			oldestKey = key
			oldestTime = conn.LastUsed
			lowestUseCount = conn.UseCount
		}
	}

	if oldestKey != "" {
		if conn := pp.connections[oldestKey]; conn != nil {
			conn.Transport.CloseIdleConnections()
		}
		delete(pp.connections, oldestKey)
	}
}

// startHealthCheck 启动后台健康检查
func (pp *ProxyPool) startHealthCheck() {
	ticker := time.NewTicker(pp.healthCheckTick)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pp.healthCheck()
		case <-pp.stopHealthCheck:
			return
		}
	}
}

// healthCheck 执行健康检查
func (pp *ProxyPool) healthCheck() {
	pp.mu.Lock()
	defer pp.mu.Unlock()

	for key, conn := range pp.connections {
		atomic.AddInt64(&pp.stats.HealthCheckCount, 1)

		// 检查连接是否过期
		if conn.IsExpired() {
			conn.Transport.CloseIdleConnections()
			delete(pp.connections, key)
			continue
		}

		// 简单的健康检查：如果错误率过高且最近没有使用，标记为不健康
		errorRate := float64(conn.ErrorCount) / float64(conn.UseCount+1)
		if errorRate > constants.MediumErrorRateThreshold && time.Since(conn.LastUsed) > time.Minute {
			conn.IsHealthy = false
		} else if errorRate < constants.LowErrorRateThreshold {
			// 错误率低时恢复健康状态
			if !conn.IsHealthy {
				atomic.AddInt64(&pp.stats.UnhealthyProxies, -1)
			}
			conn.IsHealthy = true
		}
	}
}

// GetStats 获取连接池统计信息
func (pp *ProxyPool) GetStats() ProxyPoolStats {
	pp.mu.RLock()
	defer pp.mu.RUnlock()

	stats := pp.stats
	stats.ActiveConnections = 0
	stats.IdleConnections = 0

	for _, conn := range pp.connections {
		if time.Since(conn.LastUsed) < time.Minute {
			stats.ActiveConnections++
		} else {
			stats.IdleConnections++
		}
	}

	return stats
}

// GetHitRate 获取连接池命中率
func (pp *ProxyPool) GetHitRate() float64 {
	total := pp.stats.ConnectionHits + pp.stats.ConnectionMisses
	if total == 0 {
		return 0
	}
	return float64(pp.stats.ConnectionHits) / float64(total)
}

// Size 获取当前连接池大小
func (pp *ProxyPool) Size() int {
	pp.mu.RLock()
	defer pp.mu.RUnlock()
	return len(pp.connections)
}

// Clear 清空连接池
func (pp *ProxyPool) Clear() {
	pp.mu.Lock()
	defer pp.mu.Unlock()

	// 关闭所有连接
	for _, conn := range pp.connections {
		conn.Transport.CloseIdleConnections()
	}

	pp.connections = make(map[string]*ProxyConnection)
}

// Close 关闭连接池
func (pp *ProxyPool) Close() {
	close(pp.stopHealthCheck)
	pp.Clear()
}

// GetConnectionInfo 获取特定代理的连接信息
func (pp *ProxyPool) GetConnectionInfo(proxyURL string) (*ProxyConnection, bool) {
	pp.mu.RLock()
	defer pp.mu.RUnlock()
	conn, exists := pp.connections[proxyURL]
	return conn, exists
}

// ListHealthyProxies 列出所有健康的代理
func (pp *ProxyPool) ListHealthyProxies() []string {
	pp.mu.RLock()
	defer pp.mu.RUnlock()

	var healthyProxies []string
	for url, conn := range pp.connections {
		if conn.IsHealthy && !conn.IsExpired() {
			healthyProxies = append(healthyProxies, url)
		}
	}

	return healthyProxies
}