package action

import (
	"context"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/mubeng/mubeng/internal/interfaces"
)

// 测试用的简单日志服务
type testLogger struct{}

func (t *testLogger) Info(msg string, args ...interface{})                                    {}
func (t *testLogger) Warn(msg string, args ...interface{})                                    {}
func (t *testLogger) Error(msg string, args ...interface{})                                   {}
func (t *testLogger) Debug(msg string, args ...interface{})                                   {}
func (t *testLogger) Fatal(msg string, args ...interface{})                                   {}
func (t *testLogger) WithTraceID(traceID string) interfaces.LogService                        { return t }
func (t *testLogger) WithFields(fields map[string]interface{}) interfaces.LogService          { return t }
func (t *testLogger) LogError(err error, msg string, args ...interface{})                     {}
func (t *testLogger) GetLogger() interface{}                                                   { return t }

func TestAdvancedBodyModification_JSON(t *testing.T) {
	executor := &ModifyRequestExecutor{Logger: &testLogger{}}
	
	req, _ := http.NewRequest("POST", "http://example.com/api", nil)
	
	parameters := map[string]interface{}{
		"body_config": map[string]interface{}{
			"content":      `{"test": true, "format": "json"}`,
			"format":       "json",
			"content_type": "application/json; charset=utf-8",
		},
	}
	
	ctx := context.Background()
	modifiedReq, _, err := executor.ExecuteHTTP(ctx, parameters, req, nil)
	if err != nil {
		t.Fatalf("JSON修改失败: %v", err)
	}
	
	// 验证Content-Type
	expectedContentType := "application/json; charset=utf-8"
	if modifiedReq.Header.Get("Content-Type") != expectedContentType {
		t.Errorf("Content-Type错误，期望: %s, 实际: %s", expectedContentType, modifiedReq.Header.Get("Content-Type"))
	}
	
	// 验证body内容
	body, _ := io.ReadAll(modifiedReq.Body)
	expectedBody := `{"test": true, "format": "json"}`
	if string(body) != expectedBody {
		t.Errorf("Body内容错误，期望: %s, 实际: %s", expectedBody, string(body))
	}
}

func TestAdvancedBodyModification_XML(t *testing.T) {
	executor := &ModifyResponseExecutor{Logger: &testLogger{}}
	
	resp := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
	}
	
	xmlContent := `<?xml version="1.0"?><root><test>true</test></root>`
	parameters := map[string]interface{}{
		"body_config": map[string]interface{}{
			"content": xmlContent,
			"format":  "xml",
		},
	}
	
	ctx := context.Background()
	_, modifiedResp, err := executor.ExecuteHTTP(ctx, parameters, nil, resp)
	if err != nil {
		t.Fatalf("XML修改失败: %v", err)
	}
	
	// 验证Content-Type
	expectedContentType := "application/xml"
	if modifiedResp.Header.Get("Content-Type") != expectedContentType {
		t.Errorf("Content-Type错误，期望: %s, 实际: %s", expectedContentType, modifiedResp.Header.Get("Content-Type"))
	}
	
	// 验证body内容
	body, _ := io.ReadAll(modifiedResp.Body)
	if string(body) != xmlContent {
		t.Errorf("Body内容错误，期望: %s, 实际: %s", xmlContent, string(body))
	}
}

func TestAdvancedBodyModification_HTML(t *testing.T) {
	executor := &ModifyResponseExecutor{Logger: &testLogger{}}
	
	resp := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
	}
	
	htmlContent := `<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Hello</h1></body></html>`
	parameters := map[string]interface{}{
		"body_config": map[string]interface{}{
			"content": htmlContent,
			"format":  "html",
		},
	}
	
	ctx := context.Background()
	_, modifiedResp, err := executor.ExecuteHTTP(ctx, parameters, nil, resp)
	if err != nil {
		t.Fatalf("HTML修改失败: %v", err)
	}
	
	// 验证Content-Type
	expectedContentType := "text/html"
	if modifiedResp.Header.Get("Content-Type") != expectedContentType {
		t.Errorf("Content-Type错误，期望: %s, 实际: %s", expectedContentType, modifiedResp.Header.Get("Content-Type"))
	}
	
	// 验证body内容
	body, _ := io.ReadAll(modifiedResp.Body)
	if string(body) != htmlContent {
		t.Errorf("Body内容错误，期望: %s, 实际: %s", htmlContent, string(body))
	}
}

func TestAdvancedBodyModification_Form(t *testing.T) {
	executor := &ModifyRequestExecutor{Logger: &testLogger{}}
	
	req, _ := http.NewRequest("POST", "http://example.com/form", nil)
	
	formContent := "username=test&password=secret&action=login"
	parameters := map[string]interface{}{
		"body_config": map[string]interface{}{
			"content": formContent,
			"format":  "form",
		},
	}
	
	ctx := context.Background()
	modifiedReq, _, err := executor.ExecuteHTTP(ctx, parameters, req, nil)
	if err != nil {
		t.Fatalf("Form修改失败: %v", err)
	}
	
	// 验证Content-Type
	expectedContentType := "application/x-www-form-urlencoded"
	if modifiedReq.Header.Get("Content-Type") != expectedContentType {
		t.Errorf("Content-Type错误，期望: %s, 实际: %s", expectedContentType, modifiedReq.Header.Get("Content-Type"))
	}
	
	// 验证body内容
	body, _ := io.ReadAll(modifiedReq.Body)
	if string(body) != formContent {
		t.Errorf("Body内容错误，期望: %s, 实际: %s", formContent, string(body))
	}
}

func TestAdvancedBodyModification_Binary(t *testing.T) {
	executor := &ModifyResponseExecutor{Logger: &testLogger{}}
	
	resp := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
	}
	
	// "Hello World!" 的 base64 编码
	base64Content := "SGVsbG8gV29ybGQh"
	parameters := map[string]interface{}{
		"body_config": map[string]interface{}{
			"content":      base64Content,
			"encoding":     "base64",
			"content_type": "application/octet-stream",
		},
	}
	
	ctx := context.Background()
	_, modifiedResp, err := executor.ExecuteHTTP(ctx, parameters, nil, resp)
	if err != nil {
		t.Fatalf("Binary修改失败: %v", err)
	}
	
	// 验证Content-Type
	expectedContentType := "application/octet-stream"
	if modifiedResp.Header.Get("Content-Type") != expectedContentType {
		t.Errorf("Content-Type错误，期望: %s, 实际: %s", expectedContentType, modifiedResp.Header.Get("Content-Type"))
	}
	
	// 验证解码后的内容
	body, _ := io.ReadAll(modifiedResp.Body)
	expectedDecoded := "Hello World!"
	if string(body) != expectedDecoded {
		t.Errorf("解码后内容错误，期望: %s, 实际: %s", expectedDecoded, string(body))
	}
}

func TestAdvancedBodyModification_AutoDetect(t *testing.T) {
	executor := &ModifyResponseExecutor{Logger: &testLogger{}}
	
	resp := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
	}
	
	// JSON内容，不指定format，让系统自动检测
	jsonContent := `{"auto_detected": true, "type": "json"}`
	parameters := map[string]interface{}{
		"body_config": map[string]interface{}{
			"content": jsonContent,
			// 不指定format，让系统自动检测
		},
	}
	
	ctx := context.Background()
	_, modifiedResp, err := executor.ExecuteHTTP(ctx, parameters, nil, resp)
	if err != nil {
		t.Fatalf("自动检测修改失败: %v", err)
	}
	
	// 验证自动检测的Content-Type
	expectedContentType := "application/json"
	if modifiedResp.Header.Get("Content-Type") != expectedContentType {
		t.Errorf("自动检测Content-Type错误，期望: %s, 实际: %s", expectedContentType, modifiedResp.Header.Get("Content-Type"))
	}
	
	// 验证body内容
	body, _ := io.ReadAll(modifiedResp.Body)
	if string(body) != jsonContent {
		t.Errorf("Body内容错误，期望: %s, 实际: %s", jsonContent, string(body))
	}
}

func TestAdvancedBodyModification_BackwardCompatibility(t *testing.T) {
	executor := &ModifyRequestExecutor{Logger: &testLogger{}}
	
	req, _ := http.NewRequest("POST", "http://example.com/api", nil)
	
	// 使用旧的body参数（向后兼容）
	parameters := map[string]interface{}{
		"body": `{"backward_compatible": true}`,
	}
	
	ctx := context.Background()
	modifiedReq, _, err := executor.ExecuteHTTP(ctx, parameters, req, nil)
	if err != nil {
		t.Fatalf("向后兼容测试失败: %v", err)
	}
	
	// 验证body内容
	body, _ := io.ReadAll(modifiedReq.Body)
	expectedBody := `{"backward_compatible": true}`
	if string(body) != expectedBody {
		t.Errorf("向后兼容Body内容错误，期望: %s, 实际: %s", expectedBody, string(body))
	}
}

func TestAdvancedBodyModification_InvalidJSON(t *testing.T) {
	executor := &ModifyRequestExecutor{Logger: &testLogger{}}
	
	req, _ := http.NewRequest("POST", "http://example.com/api", nil)
	
	// 无效的JSON内容
	parameters := map[string]interface{}{
		"body_config": map[string]interface{}{
			"content": `{"invalid": json}`, // 缺少引号
			"format":  "json",
		},
	}
	
	ctx := context.Background()
	_, _, err := executor.ExecuteHTTP(ctx, parameters, req, nil)
	if err == nil {
		t.Fatalf("应该返回JSON验证错误")
	}
	
	if !strings.Contains(err.Error(), "JSON格式") && !strings.Contains(err.Error(), "高级请求体修改失败") {
		t.Errorf("错误信息不正确，期望包含JSON格式相关错误，实际: %s", err.Error())
	}
}
