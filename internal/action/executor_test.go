package action

import (
	"context"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/internal/interfaces"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockLogService 模拟日志服务
type MockLogService struct {
	mock.Mock
}

func (m *MockLogService) Debug(format string, args ...interface{}) {
	callArgs := make([]interface{}, len(args)+1)
	callArgs[0] = format
	for i, arg := range args {
		callArgs[i+1] = arg
	}
	m.Called(callArgs...)
}

func (m *MockLogService) Info(format string, args ...interface{}) {
	callArgs := make([]interface{}, len(args)+1)
	callArgs[0] = format
	for i, arg := range args {
		callArgs[i+1] = arg
	}
	m.Called(callArgs...)
}

func (m *MockLogService) Warn(format string, args ...interface{}) {
	callArgs := make([]interface{}, len(args)+1)
	callArgs[0] = format
	for i, arg := range args {
		callArgs[i+1] = arg
	}
	m.Called(callArgs...)
}

func (m *MockLogService) Error(format string, args ...interface{}) {
	callArgs := make([]interface{}, len(args)+1)
	callArgs[0] = format
	for i, arg := range args {
		callArgs[i+1] = arg
	}
	m.Called(callArgs...)
}

func (m *MockLogService) Fatal(format string, args ...interface{}) {
	callArgs := make([]interface{}, len(args)+1)
	callArgs[0] = format
	for i, arg := range args {
		callArgs[i+1] = arg
	}
	m.Called(callArgs...)
}

func (m *MockLogService) WithTraceID(traceID string) interfaces.LogService {
	return m
}

func (m *MockLogService) WithFields(fields map[string]interface{}) interfaces.LogService {
	return m
}

func (m *MockLogService) LogError(err error, msg string, args ...interface{}) {
	m.Called(err, msg, args)
}

func (m *MockLogService) GetLogger() interface{} {
	return m
}

// MockProxyService 模拟代理服务
type MockProxyService struct {
	mock.Mock
}

func (m *MockProxyService) BanIP(ip string, duration int) error {
	args := m.Called(ip, duration)
	return args.Error(0)
}

func (m *MockProxyService) BanDomain(domain string, duration int) error {
	args := m.Called(domain, duration)
	return args.Error(0)
}

func (m *MockProxyService) GetProxy() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

func (m *MockProxyService) RemoveProxy(proxy string) error {
	args := m.Called(proxy)
	return args.Error(0)
}

func (m *MockProxyService) GetNextProxy() (string, error) {
	args := m.Called()
	return args.String(0), args.Error(1)
}

func (m *MockProxyService) GetProxyCount() int {
	args := m.Called()
	return args.Int(0)
}



func (m *MockProxyService) AddProxy(proxy string) error {
	args := m.Called(proxy)
	return args.Error(0)
}



func (m *MockProxyService) MarkProxyFailed(proxy string) {
	m.Called(proxy)
}

func (m *MockProxyService) MarkProxySuccess(proxy string) {
	m.Called(proxy)
}

func (m *MockProxyService) InitBanSystem(config interface{}) {
	m.Called(config)
}

func (m *MockProxyService) StartBanCleaner(ctx context.Context) {
	m.Called(ctx)
}

func (m *MockProxyService) IsIPBanned(ip string) bool {
	args := m.Called(ip)
	return args.Bool(0)
}

func (m *MockProxyService) IsDomainBanned(domain string) bool {
	args := m.Called(domain)
	return args.Bool(0)
}

func (m *MockProxyService) UnbanIP(ip string) error {
	args := m.Called(ip)
	return args.Error(0)
}

func (m *MockProxyService) UnbanDomain(domain string) error {
	args := m.Called(domain)
	return args.Error(0)
}

func (m *MockProxyService) UpdateProxyList(proxies []string) {
	m.Called(proxies)
}

func (m *MockProxyService) GetBanStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

func (m *MockProxyService) GetProxyStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

func (m *MockProxyService) ResetFailedProxies() {
	m.Called()
}

func (m *MockProxyService) Stop() {
	m.Called()
}

// MockCacheService 模拟缓存服务
type MockCacheService struct {
	mock.Mock
}

func (m *MockCacheService) Set(key string, value interface{}, ttl time.Duration) error {
	args := m.Called(key, value, ttl)
	return args.Error(0)
}

func (m *MockCacheService) Get(key string) (interface{}, bool) {
	args := m.Called(key)
	return args.Get(0), args.Bool(1)
}

func (m *MockCacheService) Delete(key string) error {
	args := m.Called(key)
	return args.Error(0)
}

func (m *MockCacheService) Clear() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockCacheService) GetStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

// 实现CacheService接口的其他方法
func (m *MockCacheService) GetDNSCache(key string) ([]string, bool) {
	args := m.Called(key)
	return args.Get(0).([]string), args.Bool(1)
}

func (m *MockCacheService) SetDNSCache(key string, value []string, ttl int) {
	m.Called(key, value, ttl)
}

func (m *MockCacheService) GetRegexCache(pattern string) (interface{}, bool) {
	args := m.Called(pattern)
	return args.Get(0), args.Bool(1)
}

func (m *MockCacheService) SetRegexCache(pattern string, value interface{}) {
	m.Called(pattern, value)
}

func (m *MockCacheService) UpdateProxyPool(proxies []string) {
	m.Called(proxies)
}

func (m *MockCacheService) StartCleanupRoutine() {
	m.Called()
}

func (m *MockCacheService) GetCacheStats() map[string]interface{} {
	args := m.Called()
	return args.Get(0).(map[string]interface{})
}

func (m *MockCacheService) ClearAllCache() {
	m.Called()
}

// TestLogExecutor 测试日志执行器
func TestLogExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &LogExecutor{Logger: mockLogger}

	t.Run("Execute with valid message", func(t *testing.T) {
		mockLogger.On("Info", "Test message").Return()
		
		params := map[string]interface{}{
			"message": "Test message",
			"level":   "info",
		}
		
		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with missing message", func(t *testing.T) {
		params := map[string]interface{}{
			"level": "info",
		}
		
		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缺少message参数")
	})

	t.Run("Validate with valid parameters", func(t *testing.T) {
		params := map[string]interface{}{
			"message": "Test message",
		}
		
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate with missing message", func(t *testing.T) {
		params := map[string]interface{}{}
		
		err := executor.Validate(params)
		assert.Error(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "log", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "记录日志信息", executor.GetDescription())
	})
}

// TestBanIPExecutor 测试IP封禁执行器
func TestBanIPExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockProxy := new(MockProxyService)
	executor := &BanIPExecutor{
		Logger:       mockLogger,
		ProxyService: mockProxy,
	}

	t.Run("Execute with IP parameter", func(t *testing.T) {
		mockProxy.On("BanIP", "***********00", 3600).Return(nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"ip": "***********00",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with custom duration", func(t *testing.T) {
		mockProxy.On("BanIP", "********", 7200).Return(nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"ip":       "********",
			"duration": 7200,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute without IP parameter", func(t *testing.T) {
		mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无法获取要封禁的IP地址")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate", func(t *testing.T) {
		params := map[string]interface{}{}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "banip", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "封禁IP地址", executor.GetDescription())
	})
}

// TestBanDomainExecutor 测试域名封禁执行器
func TestBanDomainExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockProxy := new(MockProxyService)
	executor := &BanDomainExecutor{
		Logger:       mockLogger,
		ProxyService: mockProxy,
	}

	t.Run("Execute with domain parameter", func(t *testing.T) {
		mockProxy.On("BanDomain", "example.com", 3600).Return(nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"domain": "example.com",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with permanent ban", func(t *testing.T) {
		mockProxy.On("BanDomain", "malicious.com", 31536000).Return(nil) // 1年
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"domain":    "malicious.com",
			"permanent": true,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute without domain parameter", func(t *testing.T) {
		mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "无法获取要封禁的域名")
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "ban_domain", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "封禁域名", executor.GetDescription())
	})
}

// TestBlockRequestExecutor 测试阻止请求执行器
func TestBlockRequestExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &BlockRequestExecutor{Logger: mockLogger}

	t.Run("ExecuteHTTP with default parameters", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.RemoteAddr = "***********:12345"

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}

		modifiedReq, resp, err := executor.ExecuteHTTP(context.Background(), params, req, nil)
		assert.NoError(t, err)
		assert.Equal(t, req, modifiedReq)
		assert.NotNil(t, resp)
		assert.Equal(t, 403, resp.StatusCode)
		assert.Equal(t, "1", resp.Header.Get("X-FlexProxy-Blocked"))
		mockLogger.AssertExpectations(t)
	})

	t.Run("ExecuteHTTP with custom parameters", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "http://api.example.com", nil)
		req.RemoteAddr = "********:54321"

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"reason":      "安全策略阻止",
			"status_code": 429,
			"body":        `{"error": "Rate limited"}`,
		}

		modifiedReq, resp, err := executor.ExecuteHTTP(context.Background(), params, req, nil)
		assert.NoError(t, err)
		assert.Equal(t, req, modifiedReq)
		assert.NotNil(t, resp)
		assert.Equal(t, 429, resp.StatusCode)
		assert.Equal(t, "安全策略阻止", resp.Header.Get("X-FlexProxy-Reason"))
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "block_request", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "阻止请求", executor.GetDescription())
	})
}

// TestRetrySameExecutor 测试相同IP重试执行器
func TestRetrySameExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockProxy := new(MockProxyService)
	executor := &RetrySameExecutor{
		Logger:       mockLogger,
		ProxyService: mockProxy,
	}

	t.Run("Execute with default retry count", func(t *testing.T) {
		mockProxy.On("GetNextProxy").Return("http://proxy1:8080", nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 3, // 提供有效的重试次数
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with custom retry count", func(t *testing.T) {
		mockProxy.On("GetNextProxy").Return("http://proxy2:8080", nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 2, // 使用不超过限制的值
			"delay":       "2s",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with retry count exceeding limit", func(t *testing.T) {
		mockProxy.On("GetNextProxy").Return("http://proxy3:8080", nil)
		mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 10, // 超过限制的值
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with attempts parameter", func(t *testing.T) {
		mockProxy.On("GetNextProxy").Return("http://proxy3:8080", nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"attempts": 2,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with invalid retry count", func(t *testing.T) {
		mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 0,
		}

		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "重试次数必须大于0")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate", func(t *testing.T) {
		params := map[string]interface{}{
			"retry_count": 3,
			"delay":       "1s",
		}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "retry_same", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, constants.DescRetrySameExecutor, executor.GetDescription())
	})
}

// TestRetryExecutor 测试新IP重试执行器
func TestRetryExecutor(t *testing.T) {
	t.Run("Execute with default retry count", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockProxy := new(MockProxyService)
		executor := &RetryExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		mockProxy.On("GetProxyCount").Return(5)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 3, // 提供有效的重试次数
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with custom retry count", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockProxy := new(MockProxyService)
		executor := &RetryExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		mockProxy.On("GetProxyCount").Return(10)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count":   2, // 使用不超过限制的值
			"delay":         "3s",
			"rotation_mode": "random",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with retry count exceeding limit", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockProxy := new(MockProxyService)
		executor := &RetryExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		mockProxy.On("GetProxyCount").Return(10)
		mockLogger.On("Warn", mock.AnythingOfType("string"), mock.Anything).Return()
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 10, // 超过限制的值
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Execute with empty proxy pool", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockProxy := new(MockProxyService)
		executor := &RetryExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		mockProxy.On("GetProxyCount").Return(0)
		mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 3,
		}

		err := executor.Execute(context.Background(), params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "代理池为空")
		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		executor := &RetryExecutor{}
		assert.Equal(t, "retry", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		executor := &RetryExecutor{}
		assert.Equal(t, constants.DescRetryExecutor, executor.GetDescription())
	})
}



// TestCacheExecutor 测试缓存执行器
func TestCacheExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	mockCache := new(MockCacheService)
	executor := &CacheExecutor{
		Logger:       mockLogger,
		CacheService: mockCache,
	}

	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"duration":      600000,
			"max_use_count": 5,
			"cache_scope":   "domain",
			"ignore_params": true,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "cache", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, constants.DescCacheExecutor, executor.GetDescription())
	})
}

// TestNullResponseExecutor 测试空响应执行器
func TestNullResponseExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &NullResponseExecutor{Logger: mockLogger}

	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"status_code":  404,
			"content_type": "application/json",
			"body":         `{"error": "Not found"}`,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "null_response", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, "返回空响应或自定义响应", executor.GetDescription())
	})
}

// TestBypassProxyExecutor 测试绕过代理执行器
func TestBypassProxyExecutor(t *testing.T) {
	mockLogger := new(MockLogService)
	executor := &BypassProxyExecutor{Logger: mockLogger}

	t.Run("Execute with default timeout", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom timeout", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"timeout_ms": 10000,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with timeout parameter", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"timeout": 15000,
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with all parameters", func(t *testing.T) {
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"timeout":    20000,
			"keep_alive": true,
			"dns_mode":   "remote",
		}

		err := executor.Execute(context.Background(), params)
		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate valid parameters", func(t *testing.T) {
		params := map[string]interface{}{
			"timeout":    30000,
			"keep_alive": false,
			"dns_mode":   "local",
		}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, "bypass_proxy", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		assert.Equal(t, constants.DescBypassProxyExecutor, executor.GetDescription())
	})
}

// =============================================================================
// 新增执行器测试
// =============================================================================

func TestCacheResponseExecutor(t *testing.T) {
	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockCache := new(MockCacheService)
		executor := &CacheResponseExecutor{
			Logger:       mockLogger,
			CacheService: mockCache,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockCache := new(MockCacheService)
		executor := &CacheResponseExecutor{
			Logger:       mockLogger,
			CacheService: mockCache,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"duration":    600,
			"key":         "custom-key",
			"cache_scope": "domain",
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute without CacheService", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &CacheResponseExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Error", constants.ErrMsgCacheServiceNotInit).Return()

		params := map[string]interface{}{
			"duration": 300,
		}
		err := executor.Execute(context.Background(), params)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "CacheService未初始化")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with invalid duration", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockCache := new(MockCacheService)
		executor := &CacheResponseExecutor{
			Logger:       mockLogger,
			CacheService: mockCache,
		}

		mockLogger.On("Warn", "缓存时间无效或超过限制，已调整为默认值: %d秒", constants.CacheResponseDefaultDuration).Return()
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"duration": 999999, // 超过最大限制
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate valid parameters", func(t *testing.T) {
		executor := &CacheResponseExecutor{}
		params := map[string]interface{}{
			"duration": 300,
			"key":      "test-key",
		}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate invalid duration", func(t *testing.T) {
		executor := &CacheResponseExecutor{}
		params := map[string]interface{}{
			"duration": -100,
		}
		err := executor.Validate(params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缓存时间格式无效")
	})

	t.Run("GetType", func(t *testing.T) {
		executor := &CacheResponseExecutor{}
		assert.Equal(t, "cache_response", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		executor := &CacheResponseExecutor{}
		assert.Equal(t, constants.DescCacheResponseExecutor, executor.GetDescription())
	})
}

func TestSaveToPoolExecutor(t *testing.T) {
	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockProxy := new(MockProxyService)
		executor := &SaveToPoolExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		mockLogger.On("Warn", "未找到当前代理信息，无法保存到代理池").Return()
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockProxy := new(MockProxyService)
		executor := &SaveToPoolExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		mockLogger.On("Warn", "未找到当前代理信息，无法保存到代理池").Return()
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"quality_tier":    "high",
			"min_score":       85.5,
			"pool_name":       "premium",
			"domain_specific": true,
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute without ProxyService", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &SaveToPoolExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Error", constants.ErrMsgProxyServiceNotInit).Return()

		params := map[string]interface{}{
			"quality_tier": "high",
		}
		err := executor.Execute(context.Background(), params)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "ProxyService未初始化")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with no current proxy", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockProxy := new(MockProxyService)
		executor := &SaveToPoolExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		mockLogger.On("Warn", "未找到当前代理信息，无法保存到代理池").Return()
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"quality_tier": "medium",
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err) // 不应该返回错误，只是警告
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate valid parameters", func(t *testing.T) {
		executor := &SaveToPoolExecutor{}
		params := map[string]interface{}{
			"quality_tier": "high",
			"min_score":    90.0,
			"pool_name":    "test-pool",
		}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate invalid quality tier", func(t *testing.T) {
		executor := &SaveToPoolExecutor{}
		params := map[string]interface{}{
			"quality_tier": "invalid",
		}
		err := executor.Validate(params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "质量等级格式无效")
	})

	t.Run("Validate invalid min score", func(t *testing.T) {
		executor := &SaveToPoolExecutor{}
		params := map[string]interface{}{
			"min_score": 150.0, // 超过100
		}
		err := executor.Validate(params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "最小分数格式无效")
	})

	t.Run("GetType", func(t *testing.T) {
		executor := &SaveToPoolExecutor{}
		assert.Equal(t, "save_to_pool", executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		executor := &SaveToPoolExecutor{}
		assert.Equal(t, constants.DescSaveToPoolExecutor, executor.GetDescription())
	})
}

func TestScriptExecutor(t *testing.T) {
	t.Run("Execute with inline script", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &ScriptExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"script": "console.log('Hello World');",
			"engine": "javascript",
			"timeout": 5000,
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with script file", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &ScriptExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"script_file": "/path/to/script.js",
			"engine": "javascript",
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with oversized script", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &ScriptExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Error", constants.ErrMsgScriptSizeExceeded).Return()

		// 创建超过1MB的脚本
		largeScript := strings.Repeat("console.log('test');", 100000)
		params := map[string]interface{}{
			"script": largeScript,
		}
		err := executor.Execute(context.Background(), params)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "脚本大小超过限制")
		mockLogger.AssertExpectations(t)
	})

	t.Run("GetType", func(t *testing.T) {
		executor := &ScriptExecutor{}
		assert.Equal(t, constants.ActionTypeScript, executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		executor := &ScriptExecutor{}
		assert.Equal(t, constants.DescScriptExecutor, executor.GetDescription())
	})
}

func TestRequestURLExecutor(t *testing.T) {
	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &RequestURLExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"url": "https://example.com",
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &RequestURLExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"url":     "https://api.example.com/data",
			"method":  "POST",
			"timeout": 10000,
			"retries": 2,
			"headers": "Content-Type:application/json,Authorization:Bearer token",
			"body":    `{"key":"value"}`,
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with invalid URL", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &RequestURLExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Error", constants.ErrMsgURLInvalid).Return()

		params := map[string]interface{}{
			"url": "invalid-url",
		}
		err := executor.Execute(context.Background(), params)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "URL格式无效")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with invalid timeout", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &RequestURLExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Warn", "请求超时时间无效或超过限制，已调整为默认值: %dms", constants.RequestURLDefaultTimeout).Return()
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"url":     "https://example.com",
			"timeout": 999999, // 超过最大限制
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate valid parameters", func(t *testing.T) {
		executor := &RequestURLExecutor{}
		params := map[string]interface{}{
			"url":    "https://example.com",
			"method": "GET",
			"timeout": 5000,
		}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate invalid URL", func(t *testing.T) {
		executor := &RequestURLExecutor{}
		params := map[string]interface{}{
			"url": "invalid-url",
		}
		err := executor.Validate(params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "URL格式无效")
	})

	t.Run("GetType", func(t *testing.T) {
		executor := &RequestURLExecutor{}
		assert.Equal(t, constants.ActionTypeRequestURL, executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		executor := &RequestURLExecutor{}
		assert.Equal(t, constants.DescRequestURLExecutor, executor.GetDescription())
	})
}

func TestCacheExecutorNew(t *testing.T) {
	t.Run("Execute with default parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockCache := new(MockCacheService)
		executor := &CacheExecutor{
			Logger:       mockLogger,
			CacheService: mockCache,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with custom parameters", func(t *testing.T) {
		mockLogger := new(MockLogService)
		mockCache := new(MockCacheService)
		executor := &CacheExecutor{
			Logger:       mockLogger,
			CacheService: mockCache,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"duration":      600000,
			"max_use_count": 10,
			"cache_scope":   "domain",
			"custom_key":    "my-cache-key",
			"ignore_params": true,
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute without CacheService", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &CacheExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Error", constants.ErrMsgCacheServiceNotInit).Return()

		params := map[string]interface{}{
			"duration": 300000,
		}
		err := executor.Execute(context.Background(), params)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "CacheService未初始化")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate valid parameters", func(t *testing.T) {
		executor := &CacheExecutor{}
		params := map[string]interface{}{
			"duration":      300000,
			"max_use_count": 5,
			"cache_scope":   "url",
		}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate invalid duration", func(t *testing.T) {
		executor := &CacheExecutor{}
		params := map[string]interface{}{
			"duration": -100,
		}
		err := executor.Validate(params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "缓存时间格式无效")
	})

	t.Run("GetType", func(t *testing.T) {
		executor := &CacheExecutor{}
		assert.Equal(t, constants.ActionTypeCache, executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		executor := &CacheExecutor{}
		assert.Equal(t, constants.DescCacheExecutor, executor.GetDescription())
	})
}

func TestBanIPDomainExecutor(t *testing.T) {
	t.Run("Execute with IP address", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &BanIPDomainExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"ip":       "***********00",
			"duration": 3600,
			"reason":   "恶意访问",
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with domain", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &BanIPDomainExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"domain":   "malicious.example.com",
			"duration": 7200,
			"reason":   "恶意域名",
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with CIDR", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &BanIPDomainExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"cidr":     "***********/24",
			"duration": 1800,
			"reason":   "可疑网段",
		}
		err := executor.Execute(context.Background(), params)

		assert.NoError(t, err)
		mockLogger.AssertExpectations(t)
	})

	t.Run("Execute with invalid target", func(t *testing.T) {
		mockLogger := new(MockLogService)
		executor := &BanIPDomainExecutor{
			Logger: mockLogger,
		}

		mockLogger.On("Error", constants.ErrMsgBanTargetInvalid).Return()

		params := map[string]interface{}{
			"duration": 3600,
		}
		err := executor.Execute(context.Background(), params)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "封禁目标格式无效")
		mockLogger.AssertExpectations(t)
	})

	t.Run("Validate valid parameters", func(t *testing.T) {
		executor := &BanIPDomainExecutor{}
		params := map[string]interface{}{
			"ip":       "***********00",
			"duration": 3600,
			"reason":   "测试封禁",
		}
		err := executor.Validate(params)
		assert.NoError(t, err)
	})

	t.Run("Validate invalid IP", func(t *testing.T) {
		executor := &BanIPDomainExecutor{}
		params := map[string]interface{}{
			"ip": "invalid.ip.address",
		}
		err := executor.Validate(params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "封禁目标格式无效")
	})

	t.Run("GetType", func(t *testing.T) {
		executor := &BanIPDomainExecutor{}
		assert.Equal(t, constants.ActionTypeBanIPDomain, executor.GetType())
	})

	t.Run("GetDescription", func(t *testing.T) {
		executor := &BanIPDomainExecutor{}
		assert.Equal(t, constants.DescBanIPDomainExecutor, executor.GetDescription())
	})
}
