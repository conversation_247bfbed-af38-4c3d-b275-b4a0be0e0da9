package action

import (
	"context"
	"testing"

	"github.com/flexp/flexp/common/constants"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)





// TestExecutorIntegration 测试执行器集成
func TestExecutorIntegration(t *testing.T) {
	// 创建模拟服务
	mockLogger := new(MockLogService)
	mockProxy := new(MockProxyService)

	// 创建ActionManager
	am := NewActionManager()

	t.Run("Register and retrieve RetrySameExecutor", func(t *testing.T) {
		executor := &RetrySameExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeRetrySame)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeRetrySame, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescRetrySameExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve RetryExecutor", func(t *testing.T) {
		executor := &RetryExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeRetry)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeRetry, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescRetryExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve BypassProxyExecutor", func(t *testing.T) {
		executor := &BypassProxyExecutor{
			Logger: mockLogger,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeBypassProxy)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeBypassProxy, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescBypassProxyExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve CacheResponseExecutor", func(t *testing.T) {
		executor := &CacheResponseExecutor{
			Logger: mockLogger,
			// CacheService: 在集成测试中可以为nil
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeCacheResponse)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeCacheResponse, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescCacheResponseExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve SaveToPoolExecutor", func(t *testing.T) {
		executor := &SaveToPoolExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeSaveToPool)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeSaveToPool, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescSaveToPoolExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve NullResponseExecutor", func(t *testing.T) {
		executor := &NullResponseExecutor{
			Logger: mockLogger,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeNullResponse)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeNullResponse, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescNullResponseExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve ScriptExecutor", func(t *testing.T) {
		executor := &ScriptExecutor{
			Logger: mockLogger,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeScript)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeScript, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescScriptExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve RequestURLExecutor", func(t *testing.T) {
		executor := &RequestURLExecutor{
			Logger: mockLogger,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeRequestURL)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeRequestURL, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescRequestURLExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve CacheExecutor", func(t *testing.T) {
		mockCache := new(MockCacheService)
		executor := &CacheExecutor{
			Logger:       mockLogger,
			CacheService: mockCache,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeCache)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeCache, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescCacheExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Register and retrieve BanIPDomainExecutor", func(t *testing.T) {
		executor := &BanIPDomainExecutor{
			Logger: mockLogger,
		}

		am.RegisterExecutor(executor)

		retrievedExecutor, exists := am.GetExecutor(constants.ActionTypeBanIPDomain)
		assert.True(t, exists)
		assert.NotNil(t, retrievedExecutor)
		assert.Equal(t, constants.ActionTypeBanIPDomain, retrievedExecutor.GetType())
		assert.Equal(t, constants.DescBanIPDomainExecutor, retrievedExecutor.GetDescription())
	})

	t.Run("Execute actions through ActionManager", func(t *testing.T) {
		// 注册执行器
		retrySameExecutor := &RetrySameExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}
		retryExecutor := &RetryExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}
		bypassExecutor := &BypassProxyExecutor{
			Logger: mockLogger,
		}

		am.RegisterExecutor(retrySameExecutor)
		am.RegisterExecutor(retryExecutor)
		am.RegisterExecutor(bypassExecutor)

		// 测试RetrySameExecutor执行
		mockProxy.On("GetNextProxy").Return("http://proxy1:8080", nil)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params := map[string]interface{}{
			"retry_count": 2,
			"delay":       "1s",
		}

		err := am.ExecuteAction(context.Background(), constants.ActionTypeRetrySame, params)
		assert.NoError(t, err)

		// 测试RetryExecutor执行
		mockProxy.On("GetProxyCount").Return(5)
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return()

		params = map[string]interface{}{
			"retry_count":   2,
			"rotation_mode": "random",
		}

		err = am.ExecuteAction(context.Background(), constants.ActionTypeRetry, params)
		assert.NoError(t, err)

		// 测试BypassProxyExecutor执行
		mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything, mock.Anything, mock.Anything).Return()

		params = map[string]interface{}{
			"timeout":  30000,
			"dns_mode": "local",
		}

		err = am.ExecuteAction(context.Background(), constants.ActionTypeBypassProxy, params)
		assert.NoError(t, err)

		mockLogger.AssertExpectations(t)
		mockProxy.AssertExpectations(t)
	})

	t.Run("Validate parameters through ActionManager", func(t *testing.T) {
		// 注册执行器
		retrySameExecutor := &RetrySameExecutor{
			Logger:       mockLogger,
			ProxyService: mockProxy,
		}

		am.RegisterExecutor(retrySameExecutor)

		// 测试有效参数
		validParams := map[string]interface{}{
			"retry_count": 3,
			"delay":       "2s",
		}

		executor, exists := am.GetExecutor(constants.ActionTypeRetrySame)
		assert.True(t, exists)
		err := executor.Validate(validParams)
		assert.NoError(t, err)

		// 测试无效参数
		invalidParams := map[string]interface{}{
			"retry_count": -1,
		}

		err = executor.Validate(invalidParams)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "重试次数格式无效")
	})
}

// TestExecutorConstants 测试执行器常量
func TestExecutorConstants(t *testing.T) {
	t.Run("Action type constants", func(t *testing.T) {
		assert.Equal(t, "retry_same", constants.ActionTypeRetrySame)
		assert.Equal(t, "retry", constants.ActionTypeRetry)
		assert.Equal(t, "bypass_proxy", constants.ActionTypeBypassProxy)
	})
}
