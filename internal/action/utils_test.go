package action

import (
	"context"
	"net/http"
	"testing"

	"github.com/flexp/flexp/common/constants"
	"github.com/stretchr/testify/assert"
)

func TestExtractRealIP(t *testing.T) {
	t.Run("Extract from X-Real-IP header", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.Header.Set(constants.HeaderXRealIP, "*************")
		req.RemoteAddr = "********:12345"

		ip := extractRealIP(req)
		assert.Equal(t, "*************", ip)
	})

	t.Run("Extract from X-Forwarded-For header", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.Header.Set(constants.HeaderXForwardedFor, "*************, ********")
		req.RemoteAddr = "**********:12345"

		ip := extractRealIP(req)
		assert.Equal(t, "*************", ip)
	})

	t.Run("Extract from CF-Connecting-IP header", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.Header.Set(constants.HeaderCFConnectingIP, "*************")
		req.RemoteAddr = "**********:12345"

		ip := extractRealIP(req)
		assert.Equal(t, "*************", ip)
	})

	t.Run("Extract from RemoteAddr", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.RemoteAddr = "*************:12345"

		ip := extractRealIP(req)
		assert.Equal(t, "*************", ip)
	})

	t.Run("Extract from RemoteAddr without port", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.RemoteAddr = "*************"

		ip := extractRealIP(req)
		assert.Equal(t, "*************", ip)
	})

	t.Run("No IP found", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)

		ip := extractRealIP(req)
		assert.Equal(t, "", ip)
	})
}

func TestExtractIPFromContext(t *testing.T) {
	t.Run("Extract from parameters", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{
			"ip": "*************",
		}

		ip := extractIPFromContext(ctx, params)
		assert.Equal(t, "*************", ip)
	})

	t.Run("Extract from HTTP request context", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com", nil)
		req.Header.Set(constants.HeaderXRealIP, "*************")
		
		ctx := context.WithValue(context.Background(), constants.HTTPRequestContextKey, req)
		params := map[string]interface{}{}

		ip := extractIPFromContext(ctx, params)
		assert.Equal(t, "*************", ip)
	})

	t.Run("No IP found", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{}

		ip := extractIPFromContext(ctx, params)
		assert.Equal(t, "", ip)
	})
}

func TestExtractDomainFromContext(t *testing.T) {
	t.Run("Extract from parameters", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{
			"domain": "example.com",
		}

		domain := extractDomainFromContext(ctx, params)
		assert.Equal(t, "example.com", domain)
	})

	t.Run("Extract from HTTP request URL", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "http://example.com/path", nil)
		
		ctx := context.WithValue(context.Background(), constants.HTTPRequestContextKey, req)
		params := map[string]interface{}{}

		domain := extractDomainFromContext(ctx, params)
		assert.Equal(t, "example.com", domain)
	})

	t.Run("No domain found", func(t *testing.T) {
		ctx := context.Background()
		params := map[string]interface{}{}

		domain := extractDomainFromContext(ctx, params)
		assert.Equal(t, "", domain)
	})
}

func TestParseDuration(t *testing.T) {
	t.Run("Parse int duration", func(t *testing.T) {
		duration := parseDuration(7200)
		assert.Equal(t, 7200, duration)
	})

	t.Run("Parse float64 duration", func(t *testing.T) {
		duration := parseDuration(7200.0)
		assert.Equal(t, 7200, duration)
	})

	t.Run("Parse string duration", func(t *testing.T) {
		duration := parseDuration("7200")
		assert.Equal(t, 7200, duration)
	})

	t.Run("Parse reboot duration", func(t *testing.T) {
		duration := parseDuration(constants.BanDurationReboot)
		assert.Equal(t, constants.RebootBanDurationSeconds, duration)
	})

	t.Run("Parse nil duration", func(t *testing.T) {
		duration := parseDuration(nil)
		assert.Equal(t, constants.DefaultBanDurationSeconds, duration)
	})

	t.Run("Parse invalid duration", func(t *testing.T) {
		duration := parseDuration("invalid")
		assert.Equal(t, constants.DefaultBanDurationSeconds, duration)
	})

	t.Run("Parse negative duration", func(t *testing.T) {
		duration := parseDuration(-100)
		assert.Equal(t, constants.DefaultBanDurationSeconds, duration)
	})
}

func TestValidateHTTPStatusCode(t *testing.T) {
	t.Run("Valid status code", func(t *testing.T) {
		statusCode := validateHTTPStatusCode(200)
		assert.Equal(t, 200, statusCode)
	})

	t.Run("Valid status code at boundary", func(t *testing.T) {
		statusCode := validateHTTPStatusCode(100)
		assert.Equal(t, 100, statusCode)
		
		statusCode = validateHTTPStatusCode(599)
		assert.Equal(t, 599, statusCode)
	})

	t.Run("Invalid status code too low", func(t *testing.T) {
		statusCode := validateHTTPStatusCode(99)
		assert.Equal(t, constants.DefaultBlockStatusCode, statusCode)
	})

	t.Run("Invalid status code too high", func(t *testing.T) {
		statusCode := validateHTTPStatusCode(600)
		assert.Equal(t, constants.DefaultBlockStatusCode, statusCode)
	})
}

// Mock logger for testing
type mockLogger struct {
	lastMessage string
	lastArgs    []interface{}
}

func (m *mockLogger) Info(msg string, args ...interface{}) {
	m.lastMessage = msg
	m.lastArgs = args
}

func (m *mockLogger) Error(msg string, args ...interface{}) {
	m.lastMessage = msg
	m.lastArgs = args
}

func TestLogFunctions(t *testing.T) {
	logger := &mockLogger{}

	t.Run("logIPBanSuccess", func(t *testing.T) {
		logIPBanSuccess(logger, "*************", 3600)
		assert.Contains(t, logger.lastMessage, "IP封禁成功")
		assert.Equal(t, "*************", logger.lastArgs[0])
		assert.Equal(t, 3600, logger.lastArgs[1])
	})

	t.Run("logDomainBanSuccess", func(t *testing.T) {
		logDomainBanSuccess(logger, "example.com", 3600, "domain", false)
		assert.Contains(t, logger.lastMessage, "域名封禁成功")
		assert.Equal(t, "example.com", logger.lastArgs[0])
	})

	t.Run("logParameterMissingError", func(t *testing.T) {
		logParameterMissingError(logger, "IP地址")
		assert.Contains(t, logger.lastMessage, "无法获取要封禁的")
		assert.Equal(t, "IP地址", logger.lastArgs[0])
	})
}
