package action

import (
	"context"
	"io"
	"net/http"
	"strings"
	"testing"
)



func TestModifyRequestExecutor_ExecuteHTTP(t *testing.T) {
	executor := &ModifyRequestExecutor{
		Logger: &testLogger{},
	}

	// 创建测试请求
	req, err := http.NewRequest("POST", "http://example.com/api", strings.NewReader("original body"))
	if err != nil {
		t.Fatalf("创建测试请求失败: %v", err)
	}
	req.Header.Set("User-Agent", "Original-Agent/1.0")
	req.Header.Set("X-Real-IP", "***********")
	req.Header.Set("Accept-Encoding", "gzip")

	// 定义修改参数
	parameters := map[string]interface{}{
		"headers": map[string]interface{}{
			"User-Agent":      "FlexProxy-Test/1.0",
			"X-Custom-Header": "test-value",
			"Authorization":   "Bearer token-123",
		},
		"remove_headers": []string{"X-Real-IP", "Accept-Encoding"},
		"body":           `{"modified": true, "test": "request modification"}`,
	}

	// 执行修改
	ctx := context.Background()
	modifiedReq, _, err := executor.ExecuteHTTP(ctx, parameters, req, nil)
	if err != nil {
		t.Fatalf("ExecuteHTTP 失败: %v", err)
	}

	// 验证修改结果
	if modifiedReq.Header.Get("User-Agent") != "FlexProxy-Test/1.0" {
		t.Errorf("User-Agent 修改失败，期望: FlexProxy-Test/1.0, 实际: %s", modifiedReq.Header.Get("User-Agent"))
	}

	if modifiedReq.Header.Get("X-Custom-Header") != "test-value" {
		t.Errorf("X-Custom-Header 添加失败，期望: test-value, 实际: %s", modifiedReq.Header.Get("X-Custom-Header"))
	}

	if modifiedReq.Header.Get("Authorization") != "Bearer token-123" {
		t.Errorf("Authorization 添加失败，期望: Bearer token-123, 实际: %s", modifiedReq.Header.Get("Authorization"))
	}

	if modifiedReq.Header.Get("X-Real-IP") != "" {
		t.Errorf("X-Real-IP 删除失败，应该为空，实际: %s", modifiedReq.Header.Get("X-Real-IP"))
	}

	if modifiedReq.Header.Get("Accept-Encoding") != "" {
		t.Errorf("Accept-Encoding 删除失败，应该为空，实际: %s", modifiedReq.Header.Get("Accept-Encoding"))
	}

	// 验证请求体修改
	bodyBytes, err := io.ReadAll(modifiedReq.Body)
	if err != nil {
		t.Fatalf("读取修改后的请求体失败: %v", err)
	}
	expectedBody := `{"modified": true, "test": "request modification"}`
	if string(bodyBytes) != expectedBody {
		t.Errorf("请求体修改失败，期望: %s, 实际: %s", expectedBody, string(bodyBytes))
	}

	// 验证Content-Length更新
	if modifiedReq.ContentLength != int64(len(expectedBody)) {
		t.Errorf("Content-Length 更新失败，期望: %d, 实际: %d", len(expectedBody), modifiedReq.ContentLength)
	}
}

func TestModifyResponseExecutor_ExecuteHTTP(t *testing.T) {
	executor := &ModifyResponseExecutor{
		Logger: &testLogger{},
	}

	// 创建测试响应
	resp := &http.Response{
		StatusCode: 404,
		Status:     "404 Not Found",
		Header:     make(http.Header),
	}
	resp.Header.Set("Server", "nginx/1.18.0")
	resp.Header.Set("X-Powered-By", "PHP/7.4")
	resp.Header.Set("Content-Type", "text/html")

	// 定义修改参数
	parameters := map[string]interface{}{
		"headers": map[string]interface{}{
			"X-Proxy-Modified": "true",
			"Content-Type":     "application/json",
			"Cache-Control":    "no-cache, no-store",
		},
		"remove_headers": []string{"Server", "X-Powered-By"},
		"status_code":    200,
		"body":           `{"success": true, "message": "Response modified by FlexProxy"}`,
	}

	// 执行修改
	ctx := context.Background()
	_, modifiedResp, err := executor.ExecuteHTTP(ctx, parameters, nil, resp)
	if err != nil {
		t.Fatalf("ExecuteHTTP 失败: %v", err)
	}

	// 验证状态码修改
	if modifiedResp.StatusCode != 200 {
		t.Errorf("状态码修改失败，期望: 200, 实际: %d", modifiedResp.StatusCode)
	}

	if modifiedResp.Status != "OK" {
		t.Errorf("状态文本修改失败，期望: OK, 实际: %s", modifiedResp.Status)
	}

	// 验证响应头修改
	if modifiedResp.Header.Get("X-Proxy-Modified") != "true" {
		t.Errorf("X-Proxy-Modified 添加失败，期望: true, 实际: %s", modifiedResp.Header.Get("X-Proxy-Modified"))
	}

	if modifiedResp.Header.Get("Content-Type") != "application/json" {
		t.Errorf("Content-Type 修改失败，期望: application/json, 实际: %s", modifiedResp.Header.Get("Content-Type"))
	}

	if modifiedResp.Header.Get("Cache-Control") != "no-cache, no-store" {
		t.Errorf("Cache-Control 添加失败，期望: no-cache, no-store, 实际: %s", modifiedResp.Header.Get("Cache-Control"))
	}

	if modifiedResp.Header.Get("Server") != "" {
		t.Errorf("Server 删除失败，应该为空，实际: %s", modifiedResp.Header.Get("Server"))
	}

	if modifiedResp.Header.Get("X-Powered-By") != "" {
		t.Errorf("X-Powered-By 删除失败，应该为空，实际: %s", modifiedResp.Header.Get("X-Powered-By"))
	}

	// 验证响应体修改
	bodyBytes, err := io.ReadAll(modifiedResp.Body)
	if err != nil {
		t.Fatalf("读取修改后的响应体失败: %v", err)
	}
	expectedBody := `{"success": true, "message": "Response modified by FlexProxy"}`
	if string(bodyBytes) != expectedBody {
		t.Errorf("响应体修改失败，期望: %s, 实际: %s", expectedBody, string(bodyBytes))
	}

	// 验证Content-Length更新
	if modifiedResp.ContentLength != int64(len(expectedBody)) {
		t.Errorf("Content-Length 更新失败，期望: %d, 实际: %d", len(expectedBody), modifiedResp.ContentLength)
	}
}

func TestModifyRequestExecutor_EmptyParameters(t *testing.T) {
	executor := &ModifyRequestExecutor{
		Logger: &testLogger{},
	}

	// 创建测试请求
	req, _ := http.NewRequest("GET", "http://example.com", nil)
	originalUserAgent := "Original-Agent/1.0"
	req.Header.Set("User-Agent", originalUserAgent)

	// 空参数测试
	parameters := map[string]interface{}{}

	// 执行修改
	ctx := context.Background()
	modifiedReq, _, err := executor.ExecuteHTTP(ctx, parameters, req, nil)
	if err != nil {
		t.Fatalf("ExecuteHTTP 失败: %v", err)
	}

	// 验证没有修改
	if modifiedReq.Header.Get("User-Agent") != originalUserAgent {
		t.Errorf("空参数时不应该修改请求头，期望: %s, 实际: %s", originalUserAgent, modifiedReq.Header.Get("User-Agent"))
	}
}

func TestModifyResponseExecutor_EmptyParameters(t *testing.T) {
	executor := &ModifyResponseExecutor{
		Logger: &testLogger{},
	}

	// 创建测试响应
	resp := &http.Response{
		StatusCode: 200,
		Status:     "200 OK",
		Header:     make(http.Header),
	}
	originalContentType := "text/html"
	resp.Header.Set("Content-Type", originalContentType)

	// 空参数测试
	parameters := map[string]interface{}{}

	// 执行修改
	ctx := context.Background()
	_, modifiedResp, err := executor.ExecuteHTTP(ctx, parameters, nil, resp)
	if err != nil {
		t.Fatalf("ExecuteHTTP 失败: %v", err)
	}

	// 验证没有修改
	if modifiedResp.StatusCode != 200 {
		t.Errorf("空参数时不应该修改状态码，期望: 200, 实际: %d", modifiedResp.StatusCode)
	}

	if modifiedResp.Header.Get("Content-Type") != originalContentType {
		t.Errorf("空参数时不应该修改响应头，期望: %s, 实际: %s", originalContentType, modifiedResp.Header.Get("Content-Type"))
	}
}
