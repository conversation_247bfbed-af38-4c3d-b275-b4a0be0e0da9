package certificate

import (
	"bufio"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/mubeng/mubeng/common/logger"
)

// StartupChecker 启动时证书检查器
type StartupChecker struct {
	certManager *CertificateManager
	logger      logger.Logger
	interactive bool // 是否允许交互式操作
}

// NewStartupChecker 创建启动检查器
func NewStartupChecker(certManager *CertificateManager, logger logger.Logger, interactive bool) *StartupChecker {
	return &StartupChecker{
		certManager: certManager,
		logger:      logger,
		interactive: interactive,
	}
}

// CheckAndSetup 检查并设置证书
func (sc *StartupChecker) CheckAndSetup() error {
	sc.logger.Info("🔒 检查HTTPS代理证书状态...")

	// 检查证书是否已安装
	installed, err := sc.certManager.CheckCertificateInstallation()
	if err != nil {
		sc.logger.Warn(fmt.Sprintf("检查证书安装状态失败: %v", err))
		installed = false
	}

	if installed {
		sc.logger.Info("✅ CA证书已安装到系统信任存储")
		return nil
	}

	// 证书未安装，显示警告和解决方案
	sc.showCertificateWarning()

	if sc.interactive {
		return sc.handleInteractiveSetup()
	} else {
		return sc.handleNonInteractiveSetup()
	}
}

// showCertificateWarning 显示证书警告信息
func (sc *StartupChecker) showCertificateWarning() {
	sc.logger.Warn("⚠️  HTTPS代理证书未安装")
	sc.logger.Info("")
	sc.logger.Info("🔍 问题说明：")
	sc.logger.Info("   FlexProxy作为HTTPS代理时需要拦截和解密HTTPS流量")
	sc.logger.Info("   浏览器会显示证书警告，因为使用的是FlexProxy自签名证书")
	sc.logger.Info("")
	sc.logger.Info("💡 解决方案：")
	sc.logger.Info("   安装FlexProxy的CA证书到系统信任存储")
	sc.logger.Info("   这样浏览器就会信任FlexProxy生成的证书")
	sc.logger.Info("")
}

// handleInteractiveSetup 处理交互式设置
func (sc *StartupChecker) handleInteractiveSetup() error {
	sc.logger.Info("🤖 自动安装选项：")
	sc.logger.Info("   1. 自动安装证书（推荐）")
	sc.logger.Info("   2. 生成安装脚本")
	sc.logger.Info("   3. 显示手动安装说明")
	sc.logger.Info("   4. 跳过（继续启动，但HTTPS会有警告）")
	sc.logger.Info("")

	for {
		fmt.Print("请选择操作 (1-4): ")
		reader := bufio.NewReader(os.Stdin)
		input, err := reader.ReadString('\n')
		if err != nil {
			sc.logger.Error(fmt.Sprintf("读取输入失败: %v", err))
			continue
		}

		choice := strings.TrimSpace(input)
		switch choice {
		case "1":
			return sc.autoInstallCertificate()
		case "2":
			return sc.generateInstallScript()
		case "3":
			return sc.showManualInstructions()
		case "4":
			sc.logger.Info("⏭️  跳过证书安装，继续启动...")
			return nil
		default:
			sc.logger.Warn("无效选择，请输入1-4")
		}
	}
}

// handleNonInteractiveSetup 处理非交互式设置
func (sc *StartupChecker) handleNonInteractiveSetup() error {
	sc.logger.Info("🔧 非交互模式 - 生成安装资源...")

	// 生成安装脚本
	if err := sc.generateInstallScript(); err != nil {
		return err
	}

	// 显示手动安装说明
	return sc.showManualInstructions()
}

// autoInstallCertificate 自动安装证书
func (sc *StartupChecker) autoInstallCertificate() error {
	sc.logger.Info("🚀 正在自动安装证书...")

	err := sc.certManager.InstallCertificate()
	if err != nil {
		sc.logger.Error(fmt.Sprintf("自动安装失败: %v", err))
		sc.logger.Info("")
		sc.logger.Info("📋 请尝试手动安装：")
		return sc.showManualInstructions()
	}

	sc.logger.Info("✅ 证书安装成功！")
	sc.logger.Info("")
	sc.logger.Info("🔄 请重启浏览器以使证书生效")

	// 等待用户确认
	if sc.interactive {
		fmt.Print("按回车键继续启动FlexProxy...")
		bufio.NewReader(os.Stdin).ReadString('\n')
	} else {
		time.Sleep(2 * time.Second)
	}

	return nil
}

// generateInstallScript 生成安装脚本
func (sc *StartupChecker) generateInstallScript() error {
	sc.logger.Info("📝 正在生成安装脚本...")

	scriptPath, err := sc.certManager.GenerateInstallScript()
	if err != nil {
		return fmt.Errorf("生成安装脚本失败: %v", err)
	}

	sc.logger.Info(fmt.Sprintf("✅ 安装脚本已生成: %s", scriptPath))
	sc.logger.Info("")
	sc.logger.Info("🏃 运行脚本安装证书：")

	switch runtime.GOOS {
	case "windows":
		sc.logger.Info(fmt.Sprintf("   右键点击脚本 -> 以管理员身份运行"))
		sc.logger.Info(fmt.Sprintf("   或在管理员PowerShell中运行: %s", scriptPath))
	case "darwin", "linux":
		sc.logger.Info(fmt.Sprintf("   bash %s", scriptPath))
	}

	return nil
}

// showManualInstructions 显示手动安装说明
func (sc *StartupChecker) showManualInstructions() error {
	sc.logger.Info("📖 手动安装说明：")
	sc.logger.Info("")

	instructions := sc.certManager.GetInstallInstructions()
	lines := strings.Split(instructions, "\n")
	for _, line := range lines {
		sc.logger.Info(line)
	}

	sc.logger.Info("")
	sc.logger.Info("📍 证书文件位置：")
	sc.logger.Info(fmt.Sprintf("   %s", sc.certManager.GetCACertPath()))
	sc.logger.Info("")

	return nil
}

// ShowBrowserSpecificInstructions 显示浏览器特定说明
func (sc *StartupChecker) ShowBrowserSpecificInstructions() {
	sc.logger.Info("🌐 浏览器特定配置：")
	sc.logger.Info("")
	sc.logger.Info("Chrome/Edge：")
	sc.logger.Info("   1. 设置 -> 隐私和安全 -> 安全 -> 管理证书")
	sc.logger.Info("   2. 受信任的根证书颁发机构 -> 导入")
	sc.logger.Info("   3. 选择FlexProxy CA证书文件")
	sc.logger.Info("")
	sc.logger.Info("Firefox：")
	sc.logger.Info("   1. 设置 -> 隐私与安全 -> 证书 -> 查看证书")
	sc.logger.Info("   2. 证书颁发机构 -> 导入")
	sc.logger.Info("   3. 选择FlexProxy CA证书文件")
	sc.logger.Info("   4. 勾选'信任此CA来标识网站'")
	sc.logger.Info("")
	sc.logger.Info("Safari (macOS)：")
	sc.logger.Info("   系统证书安装后Safari会自动信任")
	sc.logger.Info("")
}

// GetCertificateInfo 获取证书信息
func (sc *StartupChecker) GetCertificateInfo() map[string]interface{} {
	info := make(map[string]interface{})

	installed, err := sc.certManager.CheckCertificateInstallation()
	info["installed"] = installed
	info["check_error"] = err
	info["cert_path"] = sc.certManager.GetCACertPath()
	info["os"] = runtime.GOOS

	return info
}