// Package certificate 提供证书管理功能
package certificate

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"fmt"
	"math/big"
	"net"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/logger"
)

// CertificateManager 证书管理器
type CertificateManager struct {
	mu           sync.RWMutex
	caCert       *x509.Certificate
	caKey        *rsa.PrivateKey
	certCache    map[string]*tls.Certificate
	certDir      string
	logger       logger.Logger
	autoInstall  bool
}

// Config 证书管理器配置
type Config struct {
	CertDir     string // 证书存储目录
	AutoInstall bool   // 是否自动安装证书
}

// NewCertificateManager 创建证书管理器
func NewCertificateManager(config *Config, logger logger.Logger) (*CertificateManager, error) {
	if config == nil {
		config = &Config{
			CertDir:     constants.DefaultCertDir,
			AutoInstall: true,
		}
	}

	cm := &CertificateManager{
		certCache:   make(map[string]*tls.Certificate),
		certDir:     config.CertDir,
		logger:      logger,
		autoInstall: config.AutoInstall,
	}

	// 确保证书目录存在
	if err := os.MkdirAll(cm.certDir, constants.StandardDirPermission); err != nil {
		return nil, fmt.Errorf("创建证书目录失败: %v", err)
	}

	// 初始化CA证书
	if err := cm.initCA(); err != nil {
		return nil, fmt.Errorf("初始化CA证书失败: %v", err)
	}

	return cm, nil
}

// initCA 初始化CA证书
func (cm *CertificateManager) initCA() error {
	caKeyPath := filepath.Join(cm.certDir, "ca-key.pem")
	caCertPath := filepath.Join(cm.certDir, "ca-cert.pem")

	// 检查CA证书是否已存在
	if cm.loadExistingCA(caKeyPath, caCertPath) {
		cm.logger.Info("加载现有CA证书成功")
		return nil
	}

	// 生成新的CA证书
	cm.logger.Info("生成新的CA证书...")
	return cm.generateCA(caKeyPath, caCertPath)
}

// loadExistingCA 加载现有CA证书
func (cm *CertificateManager) loadExistingCA(keyPath, certPath string) bool {
	// 检查文件是否存在
	if _, err := os.Stat(keyPath); os.IsNotExist(err) {
		return false
	}
	if _, err := os.Stat(certPath); os.IsNotExist(err) {
		return false
	}

	// 加载私钥
	keyData, err := os.ReadFile(keyPath)
	if err != nil {
		cm.logger.Error(fmt.Sprintf("读取CA私钥失败: %v", err))
		return false
	}

	keyBlock, _ := pem.Decode(keyData)
	if keyBlock == nil {
		cm.logger.Error("解析CA私钥PEM失败")
		return false
	}

	caKey, err := x509.ParsePKCS1PrivateKey(keyBlock.Bytes)
	if err != nil {
		cm.logger.Error(fmt.Sprintf("解析CA私钥失败: %v", err))
		return false
	}

	// 加载证书
	certData, err := os.ReadFile(certPath)
	if err != nil {
		cm.logger.Error(fmt.Sprintf("读取CA证书失败: %v", err))
		return false
	}

	certBlock, _ := pem.Decode(certData)
	if certBlock == nil {
		cm.logger.Error("解析CA证书PEM失败")
		return false
	}

	caCert, err := x509.ParseCertificate(certBlock.Bytes)
	if err != nil {
		cm.logger.Error(fmt.Sprintf("解析CA证书失败: %v", err))
		return false
	}

	// 检查证书是否过期
	if time.Now().After(caCert.NotAfter) {
		cm.logger.Warn("CA证书已过期，将重新生成")
		return false
	}

	cm.caCert = caCert
	cm.caKey = caKey
	return true
}

// generateCA 生成新的CA证书
func (cm *CertificateManager) generateCA(keyPath, certPath string) error {
	// 生成私钥
	caKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return fmt.Errorf("生成CA私钥失败: %v", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization:  []string{constants.CertOrganization},
			Country:       []string{constants.CertCountry},
			Province:      []string{""},
			Locality:      []string{constants.CertLocality},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
			CommonName:    constants.CertCommonName,
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().Add(constants.CACertValidityYears * 365 * 24 * time.Hour), // 10年有效期
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
		IsCA:                  true,
	}

	// 生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &caKey.PublicKey, caKey)
	if err != nil {
		return fmt.Errorf("创建CA证书失败: %v", err)
	}

	// 解析证书
	caCert, err := x509.ParseCertificate(certDER)
	if err != nil {
		return fmt.Errorf("解析CA证书失败: %v", err)
	}

	// 保存私钥
	keyFile, err := os.Create(keyPath)
	if err != nil {
		return fmt.Errorf("创建CA私钥文件失败: %v", err)
	}
	defer keyFile.Close()

	keyPEM := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(caKey),
	}
	if err := pem.Encode(keyFile, keyPEM); err != nil {
		return fmt.Errorf("写入CA私钥失败: %v", err)
	}

	// 保存证书
	certFile, err := os.Create(certPath)
	if err != nil {
		return fmt.Errorf("创建CA证书文件失败: %v", err)
	}
	defer certFile.Close()

	certPEM := &pem.Block{
		Type:  "CERTIFICATE",
		Bytes: certDER,
	}
	if err := pem.Encode(certFile, certPEM); err != nil {
		return fmt.Errorf("写入CA证书失败: %v", err)
	}

	cm.caCert = caCert
	cm.caKey = caKey

	cm.logger.Info("CA证书生成成功")
	return nil
}

// GetCertificate 获取指定域名的证书
func (cm *CertificateManager) GetCertificate(serverName string) (*tls.Certificate, error) {
	cm.mu.RLock()
	if cert, exists := cm.certCache[serverName]; exists {
		cm.mu.RUnlock()
		return cert, nil
	}
	cm.mu.RUnlock()

	// 生成新证书
	return cm.generateCertificate(serverName)
}

// generateCertificate 为指定域名生成证书
func (cm *CertificateManager) generateCertificate(serverName string) (*tls.Certificate, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 双重检查
	if cert, exists := cm.certCache[serverName]; exists {
		return cert, nil
	}

	// 生成私钥
	key, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return nil, fmt.Errorf("生成私钥失败: %v", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(time.Now().UnixNano()),
		Subject: pkix.Name{
			Organization:  []string{"FlexProxy"},
			Country:       []string{"US"},
			Province:      []string{""},
			Locality:      []string{"San Francisco"},
			StreetAddress: []string{""},
			PostalCode:    []string{""},
			CommonName:    serverName,
		},
		NotBefore:    time.Now(),
		NotAfter:     time.Now().Add(365 * 24 * time.Hour), // 1年有效期
		KeyUsage:     x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:  []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		DNSNames:     []string{serverName},
	}

	// 如果是IP地址，添加到IPAddresses
	if ip := net.ParseIP(serverName); ip != nil {
		template.IPAddresses = []net.IP{ip}
	}

	// 生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, cm.caCert, &key.PublicKey, cm.caKey)
	if err != nil {
		return nil, fmt.Errorf("创建证书失败: %v", err)
	}

	// 创建TLS证书
	cert := &tls.Certificate{
		Certificate: [][]byte{certDER},
		PrivateKey:  key,
	}

	// 缓存证书
	cm.certCache[serverName] = cert

	cm.logger.Debug(fmt.Sprintf("为域名 %s 生成证书成功", serverName))
	return cert, nil
}

// GetCACertPath 获取CA证书路径
func (cm *CertificateManager) GetCACertPath() string {
	return filepath.Join(cm.certDir, "ca-cert.pem")
}

// CheckCertificateInstallation 检查证书是否已安装
func (cm *CertificateManager) CheckCertificateInstallation() (bool, error) {
	switch runtime.GOOS {
	case "windows":
		return cm.checkWindowsCertificate()
	case "darwin":
		return cm.checkMacOSCertificate()
	case "linux":
		return cm.checkLinuxCertificate()
	default:
		return false, fmt.Errorf("不支持的操作系统: %s", runtime.GOOS)
	}
}
