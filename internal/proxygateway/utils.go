package proxygateway

import (
	"fmt"
	"strings"

	"net/url"
	"github.com/mubeng/mubeng/common/errors"
)

// isHTTPURL 检查给定的 URL 字符串是否为 HTTP 或 HTTPS。
func isHTTPURL(s string) bool {
	return (strings.HasPrefix(s, "http://") || strings.HasPrefix(s, "https://"))
}

// GetBaseURL 从给定的 URL 字符串返回基础 URL 和解析后的 URL。
func GetBaseURL(s string) (string, *url.URL, error) {
	var u string

	if !isHTTPURL(s) {
		return u, nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "URL必须以http://或https://开头")
	}

	parsedURL, err := url.Parse(s)
	if err != nil {
		return u, nil, err
	}

	u = fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host)

	return u, parsedURL, nil
}
