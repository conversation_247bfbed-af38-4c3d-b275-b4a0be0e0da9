package proxygateway

import (
	"context"
	"fmt"
	"io"
	"strings"

	"net/http"
	"net/url"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/apigateway"
	"github.com/aws/aws-sdk-go-v2/service/apigateway/types"
	"github.com/flexp/flexp/pkg/helper/awsurl"
	"github.com/flexp/flexp/common/errors"
)

// ProxyGateway 包含与 AWS API Gateway 交互的信息。
type ProxyGateway struct {
	baseURL       string
	parsedBaseURL *url.URL
	endpoint      *url.URL

	accessKeyID     string
	secretAccessKey string

	client     *apigateway.Client
	httpClient *http.Client

	region string
	apiID  *string
}

// New 创建一个新的 ProxyGateway 实例
func New(ctx context.Context, accessKeyID, secretAccessKey, region string) (*ProxyGateway, error) {
	creds := credentials.NewStaticCredentialsProvider(accessKeyID, secretAccessKey, "")
	if _, err := creds.Retrieve(ctx); err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSCredentialsFailed, "无法获取AWS凭证")
	}

	if !ValidRegionCodes[region] {
		return nil, errors.NewErrorWithDetails(errors.ErrTypeAWS, errors.ErrCodeAWSRegionInvalid, "无效的AWS区域", fmt.Sprintf("区域: %s", region))
	}

	pg := &ProxyGateway{
		baseURL:         "",
		region:          region,
		apiID:           nil,
		endpoint:        nil,
		accessKeyID:     accessKeyID,
		secretAccessKey: secretAccessKey,
		httpClient:      new(http.Client),
	}

	if err := pg.createClient(ctx, creds); err != nil {
		return nil, err
	}

	return pg, nil
}

// NewFromURL 从 AWS URL 创建一个新的 ProxyGateway 实例
func NewFromURL(ctx context.Context, s string) (*ProxyGateway, error) {
	u, err := awsurl.Parse(s)
	if err != nil {
		return nil, err
	}

	return New(ctx, u.AccessKeyID, u.SecretAccessKey, u.Region)
}

// createClient 创建一个新的 API Gateway 客户端
func (pg *ProxyGateway) createClient(ctx context.Context, credentials credentials.StaticCredentialsProvider) error {
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(pg.region),
		config.WithCredentialsProvider(credentials),
	)

	if err != nil {
		return errors.WrapErrorWithDetails(err, errors.ErrTypeAWS, errors.ErrCodeAWSConfigLoadFailed, "无法加载AWS SDK配置", fmt.Sprintf("区域: %s", pg.region))
	}

	pg.client = apigateway.NewFromConfig(cfg)

	return nil
}

// storeEndpoint 存储 API 端点 URL
func (pg *ProxyGateway) storeEndpoint(region, apiID string) error {
	endpoint := fmt.Sprintf("https://%s.execute-api.%s.amazonaws.com/%s", apiID, region, StageName)
	parsedEndpoint, err := url.Parse(endpoint)
	if err != nil {
		return errors.WrapErrorWithDetails(err, errors.ErrTypeAWS, errors.ErrCodeAWSEndpointInvalid, "无法解析API Gateway端点URL", fmt.Sprintf("端点: %s", endpoint))
	}

	pg.endpoint = parsedEndpoint

	return nil
}

// SetBaseURL 设置网关的基础 URL
func (pg *ProxyGateway) SetBaseURL(u string) error {
	var err error

	pg.baseURL, pg.parsedBaseURL, err = GetBaseURL(u)
	if err != nil {
		return err
	}

	return nil
}

// SetHTTPClient 设置用于请求的 HTTP 客户端
func (pg *ProxyGateway) SetHTTPClient(client *http.Client) {
	pg.httpClient = client
}

// Start 创建 API Gateway 并部署它
func (pg *ProxyGateway) Start(ctx context.Context) error {
	if pg.parsedBaseURL == nil {
		return errors.NewError(errors.ErrTypeAWS, errors.ErrCodeAWSConfigInvalid, "基础URL未设置")
	}

	// 列出现有的API
	apis, err := pg.client.GetRestApis(ctx, new(apigateway.GetRestApisInput))
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法列出REST API")
	}

	// 查找具有匹配标签的现有API
	var existingAPI *types.RestApi
	for _, api := range apis.Items {
		if api.Tags["baseURL"] == pg.baseURL && api.Tags["region"] == pg.region {
			existingAPI = &api
			break
		}
	}

	if existingAPI != nil {
		pg.apiID = existingAPI.Id
	} else {
		// 如果不存在则创建新的API
		api, err := pg.client.CreateRestApi(ctx, &apigateway.CreateRestApiInput{
			Name:        aws.String(fmt.Sprintf("flexp-proxy-gateway-%s", pg.region)),
			Description: aws.String(fmt.Sprintf("flexp Proxy Gateway (%s)", pg.baseURL)),
			Tags: map[string]string{
				"baseURL": pg.baseURL,
				"region":  pg.region,
			},
			EndpointConfiguration: &types.EndpointConfiguration{
				Types: []types.EndpointType{types.EndpointTypeRegional},
			},
		})

		if err != nil {
			return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法创建REST API")
		}

		pg.apiID = api.Id
	}

	// 如果 API 已存在，只需存储端点并返回
	if existingAPI != nil {
		return pg.storeEndpoint(pg.region, *pg.apiID)
	}

	// 获取根资源 ID
	resources, err := pg.client.GetResources(ctx, &apigateway.GetResourcesInput{
		RestApiId: pg.apiID,
	})
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法获取API资源")
	}

	var rootResourceID string
	for _, resource := range resources.Items {
		if *resource.Path == "/" {
			rootResourceID = *resource.Id
			break
		}
	}

	// 创建代理资源
	proxyResource, err := pg.client.CreateResource(ctx, &apigateway.CreateResourceInput{
		RestApiId: pg.apiID,
		ParentId:  aws.String(rootResourceID),
		PathPart:  aws.String("{proxy+}"),
	})
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法创建代理资源")
	}

	_, err = pg.client.PutMethod(ctx, &apigateway.PutMethodInput{
		RestApiId:         pg.apiID,
		ResourceId:        aws.String(rootResourceID),
		HttpMethod:        aws.String("ANY"),
		AuthorizationType: aws.String("NONE"),
		RequestParameters: map[string]bool{
			"method.request.path.proxy":                    true,
			"method.request.header.X-Flexp-Forwarded-For": false,
		},
	})
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法创建ANY方法")
	}

	_, err = pg.client.PutIntegration(ctx, &apigateway.PutIntegrationInput{
		RestApiId:             pg.apiID,
		ResourceId:            aws.String(rootResourceID),
		HttpMethod:            aws.String("ANY"),
		IntegrationHttpMethod: aws.String("ANY"),
		Uri:                   aws.String(fmt.Sprintf("%s/{proxy}", pg.baseURL)),
		Type:                  types.IntegrationTypeHttpProxy,
		RequestParameters: map[string]string{
			"integration.request.path.proxy":             "method.request.path.proxy",
			"integration.request.header.X-Forwarded-For": "method.request.header.X-Flexp-Forwarded-For",
		},
	})
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法创建集成")
	}

	_, err = pg.client.PutMethod(ctx, &apigateway.PutMethodInput{
		RestApiId:         pg.apiID,
		ResourceId:        proxyResource.Id,
		HttpMethod:        aws.String("ANY"),
		AuthorizationType: aws.String("NONE"),
		RequestParameters: map[string]bool{
			"method.request.path.proxy":                    true,
			"method.request.header.X-Flexp-Forwarded-For": false,
		},
	})
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法创建ANY方法")
	}

	_, err = pg.client.PutIntegration(ctx, &apigateway.PutIntegrationInput{
		RestApiId:             pg.apiID,
		ResourceId:            proxyResource.Id,
		HttpMethod:            aws.String("ANY"),
		IntegrationHttpMethod: aws.String("ANY"),
		Uri:                   aws.String(fmt.Sprintf("%s/{proxy}", pg.baseURL)),
		Type:                  types.IntegrationTypeHttpProxy,
		RequestParameters: map[string]string{
			"integration.request.path.proxy":             "method.request.path.proxy",
			"integration.request.header.X-Forwarded-For": "method.request.header.X-Flexp-Forwarded-For",
		},
	})
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法创建集成")
	}

	// 部署 API
	_, err = pg.client.CreateDeployment(ctx, &apigateway.CreateDeploymentInput{
		RestApiId: pg.apiID,
		StageName: aws.String(StageName),
	})
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法创建部署")
	}

	// 存储端点
	return pg.storeEndpoint(pg.region, *pg.apiID)
}

// GetEndpoint 返回 API Gateway 端点
func (pg *ProxyGateway) GetEndpoint() *url.URL {
	return pg.endpoint
}

// Send 通过网关发送 HTTP 请求
func (pg *ProxyGateway) Send(method, path string, body io.Reader, headers http.Header) (*http.Response, error) {
	if pg.endpoint == nil {
		return nil, errors.NewError(errors.ErrTypeAWS, errors.ErrCodeAWSEndpointNotAvailable, "没有可用的端点，请确保网关已启动")
	}

	if strings.HasPrefix(path, "http://") || strings.HasPrefix(path, "https://") {
		parsedURL, err := url.Parse(path)
		if err != nil {
			return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "无效的路径URL", fmt.Sprintf("路径: %s", path))
		}

		path = parsedURL.Path
	}

	path = strings.TrimPrefix(path, "/")

	pg.endpoint.Path = filepath.Join(pg.endpoint.Path, path)
	url := pg.endpoint.String()

	// 创建请求
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPRequestCreateFailed, "无法创建HTTP请求", fmt.Sprintf("方法: %s, URL: %s", method, url))
	}

	// 复制头部
	for key, values := range headers {
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	req.Header.Del("Host")

	if x := req.Header.Get("X-Forwarded-For"); x != "" {
		req.Header.Add("X-Flexp-Forwarded-For", x)
		req.Header.Del("X-Forwarded-For")
	}

	// 发送请求
	resp, err := pg.httpClient.Do(req)
	if err != nil {
		return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeHTTP, errors.ErrCodeHTTPRequestFailed, "通过AWS区域发送请求失败", fmt.Sprintf("区域: %s", pg.region))
	}

	return resp, nil
}

// SendRequest 通过网关发送 [http.Request]
func (pg *ProxyGateway) SendRequest(req *http.Request) (*http.Response, error) {
	headers := req.Header.Clone()

	return pg.Send(req.Method, req.URL.String(), req.Body, headers)
}

// Close 清理网关资源
func (pg *ProxyGateway) Close(ctx context.Context) error {
	_, err := pg.client.DeleteRestApi(ctx, &apigateway.DeleteRestApiInput{
		RestApiId: pg.apiID,
	})

	return err
}

// Clean 删除网关创建的所有资源
func (pg *ProxyGateway) Clean(ctx context.Context) error {
	apis, err := pg.client.GetRestApis(ctx, new(apigateway.GetRestApisInput))
	if err != nil {
		return errors.WrapError(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法列出REST API")
	}

	for _, api := range apis.Items {
		if strings.HasPrefix(*api.Name, "Flexp-proxy-gateway") {
			_, err := pg.client.DeleteRestApi(ctx, &apigateway.DeleteRestApiInput{
				RestApiId: api.Id,
			})
			if err != nil {
				return errors.WrapErrorWithDetails(err, errors.ErrTypeAWS, errors.ErrCodeAWSAPICallFailed, "无法删除API", fmt.Sprintf("API名称: %s", *api.Name))
			}
		}
	}

	return nil
}
