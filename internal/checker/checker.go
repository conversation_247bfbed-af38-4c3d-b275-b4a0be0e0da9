package checker

import (
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/hashicorp/go-retryablehttp"
	"github.com/logrusorgru/aurora"
	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/pkg/helper"
	"github.com/mubeng/mubeng/pkg/flexproxy"
	"github.com/sourcegraph/conc/pool"
)

// Do 从列表中检查代理。
//
// 如果启用详细模式，则显示已死亡的代理，
// 或将活跃代理保存到用户定义的文件中。
func Do(opt *common.Options) {
	p := pool.New().WithMaxGoroutines(opt.Goroutine)
	c := retryablehttp.NewClient()
	c.RetryMax = opt.MaxRetries
	c.Logger = nil

	for _, proxy := range opt.ProxyManager.GetProxies() {
		address := helper.EvalFunc(proxy)

		p.Go(func() {
			addr, err := check(c, address, opt.Timeout)
			if len(opt.Countries) > 0 && !isMatchCC(opt.Countries, addr.Country) {
				return
			}

			if err != nil {
				if opt.Verbose {
					fmt.Printf("[%s] %s\n", aurora.Red(constants.CheckStatusDied), address)
				}
			} else {
				fmt.Printf(
					"[%s] [%s] [%s] %s (%s)\n",
					aurora.Green(constants.CheckStatusLive), aurora.Magenta(addr.Country),
					aurora.Cyan(addr.IP), address, aurora.Yellow(addr.Duration),
				)

				if opt.Output != "" {
					fmt.Fprintf(opt.Result, "%s\n", address)
				}
			}
		})
	}

	p.Wait()
}

func isMatchCC(cc []string, code string) bool {
	if code == "" {
		return false
	}

	for _, c := range cc {
		if code == strings.ToUpper(strings.TrimSpace(c)) {
			return true
		}
	}

	return false
}

func check(c *retryablehttp.Client, address string, timeout time.Duration) (IPInfo, error) {
	req, err := retryablehttp.NewRequest("GET", endpoint, nil)
	if err != nil {
		return ipinfo, err
	}
	req.Header.Add("Connection", "close")

	tr, err := flexproxy.Transport(address, "local", nil, "")
	if err != nil {
		return ipinfo, err
	}

	proxy := &flexproxy.Proxy{
		Address:   address,
		Transport: tr,
	}

	client, err := proxy.New(req.Request)
	if err != nil {
		return ipinfo, err
	}

	c.HTTPClient = client
	c.HTTPClient.Timeout = timeout

	start := time.Now()
	resp, err := c.Do(req)
	if err != nil {
		return ipinfo, err
	}
	duration := time.Since(start)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return ipinfo, err
	}

	err = json.Unmarshal(body, &ipinfo)
	if err != nil {
		return ipinfo, err
	}
	ipinfo.Duration = duration.Truncate(time.Millisecond)

	defer resp.Body.Close()
	defer tr.CloseIdleConnections()

	return ipinfo, nil
}
