package proxymanager

import (
	"bufio"
	"context" // 为 StartBanCleaner 添加
	"errors"
	"fmt"
	"math/rand"
	"os"
	"strconv" // 为 ParseInt 添加
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify" // 为 Watch 方法添加
	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/pkg/flexproxy" // 假设仍需要用于 placeholder.ReplaceAllString 和 Transport
	"github.com/flexp/flexp/pkg/helper"    // 假设仍需要用于 helper.Eval
	"github.com/flexp/flexp/common/constants"
	flexerrors "github.com/flexp/flexp/common/errors"
)

// var placeholder = strings.NewReplacer("https", "http", "socks5", "http", "socks4", "http") // 已移除，将使用 vars.go 中的 placeholder

// IPBanInfo 存储IP封禁信息
type IPBanInfo struct {
	ExpiresAt   time.Time // 封禁到期时间，零值表示永久
	IsPermanent bool      // 是否永久封禁
}

// IsActive 检查封禁是否仍然有效
func (bi *IPBanInfo) IsActive() bool {
	if bi.IsPermanent {
		return true
	}
	return !bi.ExpiresAt.IsZero() && time.Now().Before(bi.ExpiresAt)
}

// ProxyQualityInfo 代理质量信息
type ProxyQualityInfo struct {
	ProxyURL        string                        // 代理URL
	SuccessCount    int64                         // 成功请求数
	FailureCount    int64                         // 失败请求数
	TotalRequests   int64                         // 总请求数
	AvgResponseTime time.Duration                 // 平均响应时间
	LastUsed        time.Time                     // 最后使用时间
	QualityScore    float64                       // 质量评分 (0-100)
	QualityTier     string                        // 质量等级: "premium", "standard", "backup"
	DomainStats     map[string]*DomainPerformance // 域名级别性能统计
	CreatedAt       time.Time                     // 创建时间
	UpdatedAt       time.Time                     // 更新时间
}

// DomainPerformance 域名级别性能统计
type DomainPerformance struct {
	Domain          string        // 域名
	SuccessCount    int64         // 成功请求数
	FailureCount    int64         // 失败请求数
	AvgResponseTime time.Duration // 平均响应时间
	LastUsed        time.Time     // 最后使用时间
	QualityScore    float64       // 该域名下的质量评分
}

// ProxyPool 分层代理池
type ProxyPool struct {
	PremiumProxies  []string            // 高质量代理池 (成功率>90%, 延迟<500ms)
	StandardProxies []string            // 标准代理池 (成功率60-90%)
	BackupProxies   []string            // 备用代理池 (成功率30-60%)
	DomainPools     map[string][]string // 域名专用代理池
}

// ProxyManager 定义代理列表和当前代理位置
type ProxyManager struct {
	mu                  sync.Mutex
	CurrentIndex        int                              // 当前代理索引
	filepath            string                           // 代理文件路径
	Length              int                              // 代理数量
	Proxies             []string                         // 代理列表
	GlobalBannedIPs     map[string]*IPBanInfo            // 全局封禁的IP
	DomainBannedIPs     map[string]map[string]*IPBanInfo // 域名级别封禁的IP，格式为：domain -> ip -> ban info
	URLBannedIPs        map[string]map[string]*IPBanInfo // URL级别封禁的IP，格式为：url -> ip -> ban info
	PermanentBlockedIPs map[string]bool                  // 永久封禁的IP或域名
	TrustedIPs          map[string]bool                  // 受信任的IP或域名
	Config              *common.Config                   // 配置信息
	banSystemInitialized bool                           // 封禁系统是否已初始化
	proxyCache          struct {                         // 代理缓存
		proxy    string    // 缓存的代理
		lastUsed time.Time // 最后使用时间
	}
	ProxyQuality map[string]*ProxyQualityInfo // 代理质量信息映射
	QualityPools *ProxyPool                   // 质量分层代理池
	qualityMutex sync.RWMutex                 // 质量信息读写锁
	watcher      *fsnotify.Watcher            // 用于监控代理文件变更
}

func init() {
	rand.Seed(time.Now().UnixNano())
}

// New 初始化代理管理器
func New(filename string, cfg *common.Config) (*ProxyManager, error) {
	pm := &ProxyManager{
		filepath:            filename,
		Proxies:             []string{},
		GlobalBannedIPs:     make(map[string]*IPBanInfo),
		DomainBannedIPs:     make(map[string]map[string]*IPBanInfo),
		URLBannedIPs:        make(map[string]map[string]*IPBanInfo),
		PermanentBlockedIPs: make(map[string]bool),
		TrustedIPs:          make(map[string]bool),
		Config:              cfg, // Store common.Config
		ProxyQuality:        make(map[string]*ProxyQualityInfo),
		QualityPools: &ProxyPool{
			PremiumProxies:  make([]string, 0),
			StandardProxies: make([]string, 0),
			BackupProxies:   make([]string, 0),
			DomainPools:     make(map[string][]string),
		},
	}

	if err := pm.loadProxiesFromFile(); err != nil {
		return nil, flexerrors.WrapErrorWithDetails(err, flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerLoadFailed, "代理加载失败", "文件名: "+filename)
	}

	// 移除自动初始化封禁系统，改为显式调用
	// 这样可以避免重复初始化和重复日志输出
	pm.InitQualitySystem() // 在代理加载后初始化质量系统

	return pm, nil
}

// 使用统一的日志管理器
var proxyManagerLogger = logger.GetProxyManagerLogger()

// loadProxiesFromFile 从文件加载代理列表
func (p *ProxyManager) loadProxiesFromFile() error {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.Proxies = []string{} // 清空现有代理列表
	keys := make(map[string]bool)

	file, err := os.Open(p.filepath)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		proxy := helper.Eval(scanner.Text())
		if _, value := keys[proxy]; !value {
			normalizedProxy := placeholder.ReplaceAllString(proxy, "") // 确保占位符已定义
			_, err = flexproxy.Transport(normalizedProxy, "", nil, "") // 使用标准化代理
			if err == nil || errors.Is(err, flexproxy.ErrSwitchTransportAWSProtocolScheme) {
				keys[proxy] = true                   // 使用原始代理字符串作为键
				p.Proxies = append(p.Proxies, proxy) // 存储原始代理字符串
			} else {
				proxyManagerLogger.GetRawLogger().Warnf("无效的代理: %s, 错误: %v", proxy, err)
			}
		}
	}
	p.Length = len(p.Proxies)
	if p.Length < 1 {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoValidProxies, "没有有效的代理URL", "文件路径: "+p.filepath)
	}
	proxyManagerLogger.GetRawLogger().Infof("从 %s 加载了 %d 个代理", p.filepath, p.Length)
	return scanner.Err()
}

// Count 返回可用代理的数量
func (p *ProxyManager) Count() int {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.Length = len(p.Proxies)
	return p.Length
}

// GetProxies 返回代理列表的副本
func (p *ProxyManager) GetProxies() []string {
	p.mu.Lock()
	defer p.mu.Unlock()
	proxies := make([]string, len(p.Proxies))
	copy(proxies, p.Proxies)
	return proxies
}

// GetProxy (已弃用，请使用 RotateProxy 或 GetProxyForDomain)
// 为了接口兼容性，它可以调用 RotateProxy。
func (p *ProxyManager) GetProxy(mode string) (string, error) {
	return p.RotateProxy() // 默认使用通用轮询
}

// GetRandomProxy 从列表中返回一个随机代理
func (p *ProxyManager) GetRandomProxy() (string, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.Length == 0 {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoValidProxies, "没有可用的代理", "代理列表为空")
	}

	index := rand.Intn(p.Length)
	return p.Proxies[index], nil
}

// GetCachedProxy 返回缓存的代理（如果可用且未过期）
func (p *ProxyManager) GetCachedProxy() (string, bool) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.proxyCache.proxy != "" && time.Since(p.proxyCache.lastUsed) < 5*time.Minute {
		return p.proxyCache.proxy, true
	}
	return "", false
}

// SetCachedProxy 在缓存中设置代理
func (p *ProxyManager) SetCachedProxy(proxy string) {
	p.mu.Lock()
	defer p.mu.Unlock()

	p.proxyCache.proxy = proxy
	p.proxyCache.lastUsed = time.Now()
}

// RotateProxy 实现 ProxyManagerInterface 方法。
// 它提供通用的代理轮询机制。
func (p *ProxyManager) RotateProxy(domain ...string) (string, error) {
	// 如果提供了域名，使用 GetProxyForDomain，否则使用通用轮询。
	// 'mode'（随机/顺序）可以是全局配置或传递的参数。
	// 为简单起见，如果未指定则假设默认模式。
	rotationMode := "random" // 默认或来自配置
	if p.Config != nil && p.Config.Global.IPRotationMode != "" {
		rotationMode = p.Config.Global.IPRotationMode
	}

	var targetDomain string
	if len(domain) > 0 {
		targetDomain = domain[0]
	}
	return p.GetProxyForDomain(rotationMode, targetDomain)
}

// GetProxyForDomain 为特定域名获取代理，支持域名专用池优先选择
func (p *ProxyManager) GetProxyForDomain(mode string, domain string) (string, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if time.Since(p.proxyCache.lastUsed) < 5*time.Second && p.proxyCache.proxy != "" {
		return p.proxyCache.proxy, nil
	}

	var proxy string
	var err error

	if domain != "" {
		proxy, err = p.getProxyFromDomainPool(domain, mode)
		if err == nil && proxy != "" {
			p.proxyCache.proxy = proxy
			p.proxyCache.lastUsed = time.Now()
			return proxy, nil
		}
	}

	switch strings.ToLower(mode) {
	case "random":
		proxy, err = p.randomProxy()
	case "quality":
		proxy, err = p.qualityProxy()
	case "smart":
		proxy, err = p.smartProxy(domain)
	default:
		proxy, err = p.nextProxy()
	}

	if err == nil && proxy != "" {
		p.proxyCache.proxy = proxy
		p.proxyCache.lastUsed = time.Now()
	}
	return proxy, err
}

func (p *ProxyManager) getProxyFromDomainPool(domain string, mode string) (string, error) {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()

	if p.QualityPools == nil || p.QualityPools.DomainPools == nil {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerQualityPoolNotInitialized, "质量池未初始化", "域名: "+domain)
	}
	domainProxies, exists := p.QualityPools.DomainPools[domain]
	if !exists || len(domainProxies) == 0 {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoDomainProxies, "没有可用的域名专用代理", "域名: "+domain)
	}
	var availableProxies []string
	for _, proxy := range domainProxies {
		if !p.isProxyBannedInternal(proxy, domain, "domain") { // 使用内部检查
			availableProxies = append(availableProxies, proxy)
		}
	}
	if len(availableProxies) == 0 {
		return "", flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的未封禁域名专用代理", "域名: "+domain)
	}
	var selectedProxy string
	switch strings.ToLower(mode) {
	case "random":
		selectedProxy = availableProxies[rand.Intn(len(availableProxies))]
	case "quality":
		// 质量模式：选择第一个（假设已按质量排序）
		selectedProxy = availableProxies[0]
	case "smart":
		// 智能模式：优先选择质量较好的代理
		if len(availableProxies) > 1 {
			// 简化实现：选择前半部分中的随机一个
			topCount := (len(availableProxies) + 1) / 2
			selectedProxy = availableProxies[rand.Intn(topCount)]
		} else {
			selectedProxy = availableProxies[0]
		}
	default:
		// 顺序选择
		selectedProxy = availableProxies[0]
	}
	proxyManagerLogger.GetRawLogger().Debugf("从域名 %s 专用池选择代理: %s", domain, selectedProxy)
	return selectedProxy, nil
}

func (p *ProxyManager) nextProxy() (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	maxAttempts := count
	if p.Config != nil && p.Config.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Config.Global.MaxProxyFetchAttempts
		if maxAttempts > count {
			maxAttempts = count
		}
	}

	startIndex := p.CurrentIndex
	for i := 0; i < maxAttempts; i++ {
		p.CurrentIndex = (p.CurrentIndex + 1) % count
		proxy := p.Proxies[p.CurrentIndex]
		if !p.isProxyBannedInternal(proxy, "", "global") { // 检查全局禁用
			return proxy, nil
		}
		if p.CurrentIndex == startIndex && i > 0 {
			break
		} // 完整循环
	}
	return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerAllProxiesBanned, "所有尝试的代理都被封禁或不可用（顺序模式）")
}

func (p *ProxyManager) randomProxy() (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	maxAttempts := count
	if p.Config != nil && p.Config.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Config.Global.MaxProxyFetchAttempts
		if maxAttempts > count {
			maxAttempts = count
		}
	}

	triedIndices := make(map[int]bool)
	for i := 0; i < maxAttempts; i++ {
		if len(triedIndices) >= count {
			break
		} // 所有代理都已尝试

		var randomIndex int
		for {
			randomIndex = rand.Intn(count)
			if !triedIndices[randomIndex] {
				triedIndices[randomIndex] = true
				break
			}
		}
		proxy := p.Proxies[randomIndex]
		if !p.isProxyBannedInternal(proxy, "", "global") { // 检查全局禁用
			return proxy, nil
		}
	}
	return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerAllProxiesBanned, "所有尝试的代理都被封禁或不可用（随机模式）")
}

// qualityProxy 基于质量选择代理
func (p *ProxyManager) qualityProxy() (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	// 如果质量池已初始化，使用质量池选择
	if p.QualityPools != nil && len(p.QualityPools.PremiumProxies) > 0 {
		return p.GetQualityProxy("premium", "")
	}

	// 否则回退到随机选择
	return p.randomProxy()
}

// smartProxy 智能代理选择
func (p *ProxyManager) smartProxy(domain string) (string, error) {
	count := len(p.Proxies)
	if count == 0 {
		return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerNoAvailableProxies, "没有可用的代理")
	}

	// 智能模式的核心逻辑：
	// 1. 检查是否有缓存的代理且仍然可用
	// 2. 如果没有明确的重试指示，继续使用当前代理
	// 3. 如果需要重试，根据重试类型决定是否切换代理

	// 检查缓存的代理是否仍然可用
	if p.proxyCache.proxy != "" && time.Since(p.proxyCache.lastUsed) < 30*time.Second {
		// 检查缓存的代理是否被封禁
		if !p.isProxyBannedInternal(p.proxyCache.proxy, domain, "domain") &&
		   !p.isProxyBannedInternal(p.proxyCache.proxy, "", "global") {
			proxyManagerLogger.GetRawLogger().Debugf("智能模式：继续使用缓存代理 %s", p.proxyCache.proxy)
			return p.proxyCache.proxy, nil
		}
	}

	// 如果缓存代理不可用，选择新代理
	// 优先使用质量池，如果没有则使用随机选择
	if p.QualityPools != nil && len(p.QualityPools.PremiumProxies) > 0 {
		proxy, err := p.GetQualityProxy("premium", domain)
		if err == nil && proxy != "" {
			proxyManagerLogger.GetRawLogger().Debugf("智能模式：从质量池选择代理 %s", proxy)
			return proxy, nil
		}
	}

	// 回退到随机选择
	proxy, err := p.randomProxy()
	if err == nil && proxy != "" {
		proxyManagerLogger.GetRawLogger().Debugf("智能模式：随机选择代理 %s", proxy)
	}
	return proxy, err
}

// InitBanSystem 初始化封禁系统
// 这是一个独立的方法，需要在适当的时机显式调用
// 避免在ProxyManager创建时自动调用，防止重复初始化
func (p *ProxyManager) InitBanSystem(cfg interface{}) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 检查是否已经初始化，避免重复初始化
	if p.banSystemInitialized {
		proxyManagerLogger.GetRawLogger().Debug("封禁系统已经初始化，跳过重复初始化")
		return
	}

	// 在加载新配置前清除现有的封禁信息
	p.GlobalBannedIPs = make(map[string]*IPBanInfo)
	p.DomainBannedIPs = make(map[string]map[string]*IPBanInfo)
	p.URLBannedIPs = make(map[string]map[string]*IPBanInfo)
	p.PermanentBlockedIPs = make(map[string]bool)
	p.TrustedIPs = make(map[string]bool)

	// 类型断言获取具体的配置类型
	if config, ok := cfg.(*common.Config); ok && config != nil && config.Global.Enable {
		for _, ipOrDomain := range config.Global.BlockedIPs {
			p.PermanentBlockedIPs[ipOrDomain] = true
		}
		for _, ipOrDomain := range config.Global.TrustedIPs {
			p.TrustedIPs[ipOrDomain] = true
		}
		for _, banCfg := range config.Global.GlobalBannedIPs {
			durationStr := fmt.Sprintf("%v", banCfg.Duration)
			p.banIPInternal(banCfg.IP, durationStr, "global", "")
		}
		for _, banCfg := range config.Global.BannedDomains {
			durationStr := fmt.Sprintf("%v", banCfg.Duration)
			p.banDomainInternal(banCfg.Domain, durationStr)
		}
		proxyManagerLogger.GetRawLogger().Infof("封禁系统初始化完成：永久封禁 %d 个IP/域名，信任 %d 个IP/域名",
			len(p.PermanentBlockedIPs), len(p.TrustedIPs))
	}

	// 标记为已初始化
	p.banSystemInitialized = true
}

// ResetBanSystem 重置封禁系统初始化状态
// 用于需要重新初始化封禁系统的场景（如配置重载）
func (p *ProxyManager) ResetBanSystem() {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.banSystemInitialized = false
	proxyManagerLogger.GetRawLogger().Debug("封禁系统初始化状态已重置")
}

// IsBanSystemInitialized 检查封禁系统是否已初始化
func (p *ProxyManager) IsBanSystemInitialized() bool {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.banSystemInitialized
}

// BanIP 在给定持续时间、范围和资源下封禁IP
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) BanIP(ip, durationStr, scope, resource string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.banIPInternal(ip, durationStr, scope, resource)
}

// banIPInternal 是封禁IP的内部实现
func (p *ProxyManager) banIPInternal(ip, durationStr, scope, resource string) error {
	if p.TrustedIPs[ip] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerTrustedIPCannotBan, "受信任的IP无法封禁", "IP: "+ip)
	}
	if p.PermanentBlockedIPs[ip] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerIPAlreadyBlocked, "IP已被永久封禁", "IP: "+ip)
	}

	var expiresAt time.Time
	isPermanent := false
	if strings.ToLower(durationStr) == "reboot" || durationStr == "0" || durationStr == "" {
		isPermanent = true // 或者如果""或"0"表示其他含义，则视为仅会话有效
	} else {
		duration, err := time.ParseDuration(durationStr) // 假设durationStr格式如"300ms", "2s", "1h"
		if err != nil {
			// 尝试解析为毫秒整数
			ms, intErr := strconv.ParseInt(durationStr, 10, 64)
			if intErr != nil {
				return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyManagerInvalidDuration, "无效的封禁持续时间", "持续时间: "+durationStr+", 解析错误: "+err.Error())
			}
			duration = time.Duration(ms) * time.Millisecond
		}
		if duration <= 0 { // 如果解析的持续时间为0或负数，则视为永久/重启
			isPermanent = true
		} else {
			expiresAt = time.Now().Add(duration)
		}
	}
	banInfo := &IPBanInfo{ExpiresAt: expiresAt, IsPermanent: isPermanent}

	switch strings.ToLower(scope) {
	case "global":
		p.GlobalBannedIPs[ip] = banInfo
	case "domain":
		if resource == "" {
			return flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeInvalidResource, "域名范围需要域名资源")
		}
		if p.DomainBannedIPs[resource] == nil {
			p.DomainBannedIPs[resource] = make(map[string]*IPBanInfo)
		}
		p.DomainBannedIPs[resource][ip] = banInfo
	case "url":
		if resource == "" {
			return flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeInvalidResource, "URL范围需要URL字符串资源")
		}
		if p.URLBannedIPs[resource] == nil {
			p.URLBannedIPs[resource] = make(map[string]*IPBanInfo)
		}
		p.URLBannedIPs[resource][ip] = banInfo
	default:
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeInvalidScope, "未知的封禁范围", "范围: "+scope)
	}
	proxyManagerLogger.GetRawLogger().Infof("已封禁IP %s (范围: %s, 资源: %s, 持续时间: %s, 永久: %v)", ip, scope, resource, durationStr, isPermanent)
	return nil
}

// BanDomain 封禁整个域名（不是域名下的特定IP）
// 将域名添加到封禁域名列表中
func (p *ProxyManager) BanDomain(domain, durationStr string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	return p.banDomainInternal(domain, durationStr)
}
func (p *ProxyManager) banDomainInternal(domain, durationStr string) error {
	if p.TrustedIPs[domain] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeTrustedResource, "域名是受信任的，不能封禁", "域名: "+domain)
	}
	if p.PermanentBlockedIPs[domain] {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodePermanentlyBlocked, "域名已永久封禁", "域名: "+domain)
	}

	// 封禁域名意味着将其添加到PermanentBlockedIPs或新的定时域名封禁结构中。
	// 为了简化当前结构，如果持续时间是"reboot"或0，则设为永久。
	// 否则，这需要一个新的定时域名封禁映射。
	// 目前，如果持续时间是"reboot"，我们将非永久域名封禁视为添加到PermanentBlockedIPs
	// 这是一个简化。适当的定时域名封禁需要像DomainBannedIPs这样的自己的映射。
	if strings.ToLower(durationStr) == "reboot" || durationStr == "0" || durationStr == "" {
		p.PermanentBlockedIPs[domain] = true
		proxyManagerLogger.GetRawLogger().Infof("已永久封禁域名: %s", domain)
	} else {
		// TODO: 如有必要，实现定时域名封禁。
		// 这可能涉及将域名添加到DomainBannedIPs中的特殊条目，
		// 例如：p.DomainBannedIPs[domain]["*"] = banInfo
		// 目前，记录警告信息，定时域名封禁被视为永久封禁。
		proxyManagerLogger.GetRawLogger().Warnf("域名 %s 的定时封禁 (持续时间: %s) 目前被视为永久封禁。", domain, durationStr)
		p.PermanentBlockedIPs[domain] = true
	}
	return nil
}

// isProxyBannedInternal 是用于封禁检查的内部无锁版本
// 假设调用者已持有锁
func (p *ProxyManager) isProxyBannedInternal(proxy, resource, scope string) bool {
	if p.PermanentBlockedIPs[proxy] {
		return true
	}
	if resource != "" && p.PermanentBlockedIPs[resource] {
		return true
	} // 如果资源（域名/URL）本身被封禁
	if p.TrustedIPs[proxy] {
		return false
	}
	if resource != "" && p.TrustedIPs[resource] {
		return false
	}

	switch strings.ToLower(scope) {
	case "global":
		if banInfo, ok := p.GlobalBannedIPs[proxy]; ok && banInfo.IsActive() {
			return true
		}
	case "domain":
		if resource != "" {
			if domainBans, ok := p.DomainBannedIPs[resource]; ok {
				if banInfo, ok := domainBans[proxy]; ok && banInfo.IsActive() {
					return true
				}
			}
		}
	case "url":
		if resource != "" {
			if urlBans, ok := p.URLBannedIPs[resource]; ok {
				if banInfo, ok := urlBans[proxy]; ok && banInfo.IsActive() {
					return true
				}
			}
		}
	}
	return false
}

// IsIPBanned 根据给定的范围和资源检查IP是否被封禁
// 此方法实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsIPBanned(ip, resource, scope string) bool {
	p.mu.Lock() // 如果成为瓶颈，考虑使用RLock
	defer p.mu.Unlock()
	return p.isProxyBannedInternal(ip, resource, scope)
}

// IsIPPermanentlyBlocked 检查IP或域名是否在永久封禁列表中
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsIPPermanentlyBlocked(ipOrDomain string) bool {
	p.mu.Lock() // 考虑使用RLock
	defer p.mu.Unlock()
	return p.PermanentBlockedIPs[ipOrDomain]
}

// IsTrustedIP 检查IP或域名是否在信任列表中
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) IsTrustedIP(ipOrDomain string) bool {
	p.mu.Lock() // 考虑使用RLock
	defer p.mu.Unlock()
	return p.TrustedIPs[ipOrDomain]
}

// StartBanCleaner 启动一个协程定期清理过期的封禁
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) StartBanCleaner(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(1 * time.Minute) // 每分钟清理一次
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				p.cleanupExpiredBans()
			case <-ctx.Done():
				return
			}
		}
	}()
}

// cleanupExpiredBans 从所有范围中移除过期的IP封禁
func (p *ProxyManager) cleanupExpiredBans() {
	p.mu.Lock()
	defer p.mu.Unlock()
	now := time.Now()
	cleanedCount := 0
	for ip, banInfo := range p.GlobalBannedIPs {
		if !banInfo.IsPermanent && !banInfo.ExpiresAt.IsZero() && now.After(banInfo.ExpiresAt) {
			delete(p.GlobalBannedIPs, ip)
			cleanedCount++
		}
	}
	for domain, ipMap := range p.DomainBannedIPs {
		for ip, banInfo := range ipMap {
			if !banInfo.IsPermanent && !banInfo.ExpiresAt.IsZero() && now.After(banInfo.ExpiresAt) {
				delete(p.DomainBannedIPs[domain], ip)
				cleanedCount++
			}
		}
		if len(p.DomainBannedIPs[domain]) == 0 {
			delete(p.DomainBannedIPs, domain)
		}
	}
	for url, ipMap := range p.URLBannedIPs {
		for ip, banInfo := range ipMap {
			if !banInfo.IsPermanent && !banInfo.ExpiresAt.IsZero() && now.After(banInfo.ExpiresAt) {
				delete(p.URLBannedIPs[url], ip)
				cleanedCount++
			}
		}
		if len(p.URLBannedIPs[url]) == 0 {
			delete(p.URLBannedIPs, url)
		}
	}
	if cleanedCount > 0 {
		proxyManagerLogger.GetRawLogger().Infof("清理了 %d 个过期的IP封禁。", cleanedCount)
	}
}

// RemoveProxy 从列表中移除代理
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) RemoveProxy(target string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	found := false
	for i, proxy := range p.Proxies {
		if proxy == target {
			p.Proxies = append(p.Proxies[:i], p.Proxies[i+1:]...)
			p.Length = len(p.Proxies)
			proxyManagerLogger.GetRawLogger().Infof("已移除代理: %s。剩余代理数: %d", target, p.Length)
			found = true
			break
		}
	}
	if !found {
		return flexerrors.NewErrorWithDetails(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeProxyNotFound, "代理未在列表中找到", "代理: "+target)
	}
	// 同时从质量跟踪和池中移除
	p.qualityMutex.Lock()
	delete(p.ProxyQuality, target)
	p.removeFromAllQualityPools(target)
	p.qualityMutex.Unlock()
	return nil
}

func (p *ProxyManager) removeFromAllQualityPools(proxyURL string) {
	// 假设调用者在必要时持有qualityMutex
	p.QualityPools.PremiumProxies = p.removeFromSlice(p.QualityPools.PremiumProxies, proxyURL)
	p.QualityPools.StandardProxies = p.removeFromSlice(p.QualityPools.StandardProxies, proxyURL)
	p.QualityPools.BackupProxies = p.removeFromSlice(p.QualityPools.BackupProxies, proxyURL)
	for domain, proxies := range p.QualityPools.DomainPools {
		p.QualityPools.DomainPools[domain] = p.removeFromSlice(proxies, proxyURL)
		if len(p.QualityPools.DomainPools[domain]) == 0 {
			delete(p.QualityPools.DomainPools, domain)
		}
	}
}

// Watch 监控代理文件变更
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) Watch() (*fsnotify.Watcher, error) {
	var err error
	p.watcher, err = fsnotify.NewWatcher()
	if err != nil {
		return nil, flexerrors.WrapErrorWithDetails(err, flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeWatcherCreationFailed, "创建文件监视器失败", "路径: "+p.filepath)
	}

	go func() {
		for {
			select {
			case event, ok := <-p.watcher.Events:
				if !ok {
					return
				}
				if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
					proxyManagerLogger.GetRawLogger().Infof("代理文件 %s 已修改，正在重新加载。", p.filepath)
			if loadErr := p.loadProxiesFromFile(); loadErr != nil {
				proxyManagerLogger.GetRawLogger().Errorf("重新加载代理文件 %s 时出错: %v", p.filepath, loadErr)
					} else {
						// 为新增/移除的代理重新初始化质量系统
				p.InitQualitySystem()
					}
				}
			case err, ok := <-p.watcher.Errors:
				if !ok {
					return
				}
				proxyManagerLogger.GetRawLogger().Errorf("文件监视器错误: %v", err)
			}
		}
	}()

	err = p.watcher.Add(p.filepath)
	if err != nil {
		p.watcher.Close()
		return nil, flexerrors.WrapErrorWithDetails(err, flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeWatcherAddFailed, "添加代理文件到监视器失败", "路径: "+p.filepath)
	}

	// 直接返回底层的fsnotify.Watcher
	return p.watcher, nil
}

// InitQualitySystem 初始化或重新初始化代理质量管理系统
func (p *ProxyManager) InitQualitySystem() {
	p.qualityMutex.Lock()
	defer p.qualityMutex.Unlock()

	p.ProxyQuality = make(map[string]*ProxyQualityInfo)
	p.QualityPools = &ProxyPool{
		PremiumProxies:  make([]string, 0),
		StandardProxies: make([]string, 0),
		BackupProxies:   make([]string, 0),
		DomainPools:     make(map[string][]string),
	}
	now := time.Now()
	for _, proxy := range p.Proxies { // 假设p.Proxies是最新的
		p.ProxyQuality[proxy] = &ProxyQualityInfo{
			ProxyURL: proxy, SuccessCount: 0, FailureCount: 0, TotalRequests: 0,
			AvgResponseTime: 0, LastUsed: time.Time{}, QualityScore: 50.0,
			QualityTier: "standard", DomainStats: make(map[string]*DomainPerformance),
			CreatedAt: now, UpdatedAt: now,
		}
		p.QualityPools.StandardProxies = append(p.QualityPools.StandardProxies, proxy)
	}
	proxyManagerLogger.GetRawLogger().Infof("代理质量系统已为 %d 个代理初始化/重新初始化。", len(p.Proxies))
}

// UpdateProxyQuality 更新指定代理的质量指标
func (p *ProxyManager) UpdateProxyQuality(proxyURL string, success bool, responseTime time.Duration, domain string) {
	p.qualityMutex.Lock()
	defer p.qualityMutex.Unlock()

	quality, exists := p.ProxyQuality[proxyURL]
	if !exists {
		now := time.Now()
		quality = &ProxyQualityInfo{
			ProxyURL: proxyURL, SuccessCount: 0, FailureCount: 0, TotalRequests: 0,
			AvgResponseTime: 0, LastUsed: now, QualityScore: 50.0, QualityTier: "standard",
			DomainStats: make(map[string]*DomainPerformance), CreatedAt: now, UpdatedAt: now,
		}
		p.ProxyQuality[proxyURL] = quality
		// 如果是通过质量更新看到的新代理，则添加到标准池
		if !p.isProxyInAnyPool(proxyURL) {
			p.QualityPools.StandardProxies = append(p.QualityPools.StandardProxies, proxyURL)
		}
	}

	quality.TotalRequests++
	quality.LastUsed = time.Now()
	quality.UpdatedAt = time.Now()
	if success {
		quality.SuccessCount++
	} else {
		quality.FailureCount++
	}

	if quality.AvgResponseTime == 0 {
		quality.AvgResponseTime = responseTime
	} else {
		quality.AvgResponseTime = time.Duration(float64(quality.AvgResponseTime)*constants.ProxyResponseTimeSmoothingFactor + float64(responseTime)*constants.ProxyLatencySmoothingFactor)
	}

	if domain != "" {
		domainStats, dExists := quality.DomainStats[domain]
		if !dExists {
			domainStats = &DomainPerformance{Domain: domain, QualityScore: 50.0}
			quality.DomainStats[domain] = domainStats
		}
		if success {
			domainStats.SuccessCount++
		} else {
			domainStats.FailureCount++
		}
		domainStats.LastUsed = time.Now()
		if domainStats.AvgResponseTime == 0 {
			domainStats.AvgResponseTime = responseTime
		} else {
			domainStats.AvgResponseTime = time.Duration(float64(domainStats.AvgResponseTime)*constants.ProxyResponseTimeSmoothingFactor + float64(responseTime)*constants.ProxyLatencySmoothingFactor)
		}
		domainStats.QualityScore = p.calculateQualityScore(domainStats.SuccessCount, domainStats.FailureCount, domainStats.AvgResponseTime)
	}

	quality.QualityScore = p.calculateQualityScore(quality.SuccessCount, quality.FailureCount, quality.AvgResponseTime)
	oldTier := quality.QualityTier
	quality.QualityTier = p.determineQualityTier(quality.QualityScore, quality.AvgResponseTime)
	if oldTier != quality.QualityTier {
		p.redistributeProxy(proxyURL, oldTier, quality.QualityTier)
	}
}
func (p *ProxyManager) isProxyInAnyPool(proxyURL string) bool {
	// 假设调用者已经对qualityMutex进行了RLock或Lock
	for _, p := range p.QualityPools.PremiumProxies {
		if p == proxyURL {
			return true
		}
	}
	for _, proxy := range p.QualityPools.StandardProxies {
		if proxy == proxyURL {
			return true
		}
	}
	for _, proxy := range p.QualityPools.BackupProxies {
		if proxy == proxyURL {
			return true
		}
	}
	for _, proxies := range p.QualityPools.DomainPools {
		for _, proxy := range proxies {
			if proxy == proxyURL {
				return true
			}
		}
	}
	return false
}

func (p *ProxyManager) calculateQualityScore(successCount, failureCount int64, avgResponseTime time.Duration) float64 {
	if successCount+failureCount == 0 {
		return 50.0
	}
	successRate := float64(successCount) / float64(successCount+failureCount) * 100
	responseTimeScore := 100.0
	if avgResponseTime > 0 {
		responseTimeMs := float64(avgResponseTime.Milliseconds())
		if responseTimeMs > 500 { // 500ms是良好的响应时间
			responseTimeScore = 100 - (responseTimeMs-500)/15 // 分数下降更快，2秒时为0
			if responseTimeScore < 0 {
				responseTimeScore = 0
			}
		}
	}
	// 加权分数：成功率70%，响应时间30%
	finalScore := successRate*constants.ProxySuccessRateWeight + responseTimeScore*constants.ProxyResponseTimeWeight
	return finalScore
}

func (p *ProxyManager) determineQualityTier(score float64, avgResponseTime time.Duration) string {
	if score >= 90 && avgResponseTime.Milliseconds() <= 500 {
		return "premium"
	}
	if score >= 70 && avgResponseTime.Milliseconds() <= 1000 {
		return "standard"
	} // 调整后的标准层级
	if score >= 50 {
		return "standard"
	} // 更宽泛的标准层级
	return "backup"
}

func (p *ProxyManager) redistributeProxy(proxyURL, oldTier, newTier string) {
	// 假设调用者持有qualityMutex
	// 从旧层级移除
	switch oldTier {
	case "premium":
		p.QualityPools.PremiumProxies = p.removeFromSlice(p.QualityPools.PremiumProxies, proxyURL)
	case "standard":
		p.QualityPools.StandardProxies = p.removeFromSlice(p.QualityPools.StandardProxies, proxyURL)
	case "backup":
		p.QualityPools.BackupProxies = p.removeFromSlice(p.QualityPools.BackupProxies, proxyURL)
	}
	// 添加到新层级
	switch newTier {
	case "premium":
		p.QualityPools.PremiumProxies = append(p.QualityPools.PremiumProxies, proxyURL)
	case "standard":
		p.QualityPools.StandardProxies = append(p.QualityPools.StandardProxies, proxyURL)
	case "backup":
		p.QualityPools.BackupProxies = append(p.QualityPools.BackupProxies, proxyURL)
	}
}

func (p *ProxyManager) removeFromSlice(slice []string, item string) []string {
	for i, v := range slice {
		if v == item {
			return append(slice[:i], slice[i+1:]...)
		}
	}
	return slice // 未找到，返回原始切片
}

// GetQualityProxy 根据首选质量等级和域名选择代理
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) GetQualityProxy(preferredTier string, domain string) (string, error) {
	p.qualityMutex.RLock() // 使用RLock读取池数据
	defer p.qualityMutex.RUnlock()

	tryTier := func(tierProxies []string) (string, bool) {
		if len(tierProxies) == 0 {
			return "", false
		}
		// 目前从该层级进行简单的随机选择
		// 可以添加更复杂的选择逻辑（例如，该层级中最近最少使用的）
		var available []string
		for _, proxy := range tierProxies {
			if !p.isProxyBannedInternal(proxy, domain, "domain") && !p.isProxyBannedInternal(proxy, "", "global") {
				available = append(available, proxy)
			}
		}
		if len(available) > 0 {
			return available[rand.Intn(len(available))], true
		}
		return "", false
	}

	var proxy string
	var found bool

	switch strings.ToLower(preferredTier) {
	case "premium":
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
	case "standard":
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		} // 如果标准层级失败，尝试高级层级
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
	case "backup":
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		}
	default: // "auto"或未知，尝试最佳可用
		if proxy, found = tryTier(p.QualityPools.PremiumProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.StandardProxies); found {
			return proxy, nil
		}
		if proxy, found = tryTier(p.QualityPools.BackupProxies); found {
			return proxy, nil
		}
	}
	return "", flexerrors.NewError(flexerrors.ErrTypeProxyManager, flexerrors.ErrCodeNoSuitableProxy, "没有合适的质量代理可用")
}

// AddToDomainPool 将代理添加到域名专用池
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) AddToDomainPool(proxyURL, domain string) {
	p.qualityMutex.Lock()
	defer p.qualityMutex.Unlock()
	if p.QualityPools.DomainPools == nil {
		p.QualityPools.DomainPools = make(map[string][]string)
	}
	if p.QualityPools.DomainPools[domain] == nil {
		p.QualityPools.DomainPools[domain] = make([]string, 0)
	}
	for _, existing := range p.QualityPools.DomainPools[domain] {
		if existing == proxyURL {
			return
		} // 已存在
	}
	p.QualityPools.DomainPools[domain] = append(p.QualityPools.DomainPools[domain], proxyURL)
	proxyManagerLogger.GetRawLogger().Debugf("已将代理 %s 添加到域名 %s 的专用池", proxyURL, domain)
}

// GetProxyQualityInfo 获取特定代理的质量信息
func (p *ProxyManager) GetProxyQualityInfo(proxyURL string) (*ProxyQualityInfo, bool) {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()
	info, exists := p.ProxyQuality[proxyURL]
	return info, exists
}

// GetQualityStats 提供代理质量的摘要统计
func (p *ProxyManager) GetQualityStats() map[string]interface{} {
	p.qualityMutex.RLock()
	defer p.qualityMutex.RUnlock()
	stats := make(map[string]interface{})
	tierCounts := make(map[string]int)
	for _, q := range p.ProxyQuality {
		tierCounts[q.QualityTier]++
	}
	stats["tier_counts"] = tierCounts
	// 如需要可添加更多统计信息
	return stats
}

// GetAllProxies 实现 ProxyManagerInterface 接口方法
// 返回当前加载的所有代理字符串切片
func (p *ProxyManager) GetAllProxies() []string {
	p.mu.Lock()
	defer p.mu.Unlock()
	// 返回副本以防止外部修改内部切片
	proxiesCopy := make([]string, len(p.Proxies))
	copy(proxiesCopy, p.Proxies)
	return proxiesCopy
}

// Reload 实现 ProxyManagerInterface 接口方法
// 从配置文件重新加载代理
func (p *ProxyManager) Reload() error {
	proxyManagerLogger.GetRawLogger().Infof("正在从文件重新加载代理: %s", p.filepath)
	if err := p.loadProxiesFromFile(); err != nil {
		proxyManagerLogger.GetRawLogger().Errorf("重新加载代理文件 %s 时出错: %v", p.filepath, err)
		return err
	}
	// 为新增/移除的代理重新初始化质量系统
	p.InitQualitySystem()
	proxyManagerLogger.GetRawLogger().Infof("成功重新加载了 %d 个代理。", p.Length)
	return nil
}

// Start 实现 ProxyManagerInterface 接口方法
// 启动代理管理器和任何后台进程
func (p *ProxyManager) Start(ctx context.Context) error {
	proxyManagerLogger.GetRawLogger().Info("正在启动代理管理器")
	// 启动封禁清理器
	p.StartBanCleaner(ctx)
	return nil
}

// Stop 实现 ProxyManagerInterface 接口方法
// 停止代理管理器并清理资源
func (p *ProxyManager) Stop() error {
	proxyManagerLogger.GetRawLogger().Info("正在停止代理管理器")
	// 目前不需要特定的清理操作
	return nil
}

// UnbanIP 从所有封禁列表中移除IP
// 实现 ProxyManagerInterface 接口
func (p *ProxyManager) UnbanIP(ip string) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	
	// 从全局封禁中移除
	delete(p.GlobalBannedIPs, ip)
	
	// 从域名特定封禁中移除
	for domain, ipMap := range p.DomainBannedIPs {
		delete(ipMap, ip)
		if len(ipMap) == 0 {
			delete(p.DomainBannedIPs, domain)
		}
	}
	
	// 从URL特定封禁中移除
	for url, ipMap := range p.URLBannedIPs {
		delete(ipMap, ip)
		if len(ipMap) == 0 {
			delete(p.URLBannedIPs, url)
		}
	}
	
	// 如果存在，从永久封禁中移除
	delete(p.PermanentBlockedIPs, ip)
	
	proxyManagerLogger.GetRawLogger().Infof("已解除封禁IP: %s", ip)
	return nil
}
