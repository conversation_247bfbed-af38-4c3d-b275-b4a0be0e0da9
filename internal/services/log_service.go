// Package services 提供各种服务实现
package services

import (
	"fmt"

	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/internal/interfaces"
)

// logService 统一日志服务实现
type logService struct {
	logger *logger.LoggerAdapter
	component string
	fields map[string]interface{}
}

// NewLogService 创建日志服务实例
func NewLogService(outputFile string) interfaces.LogService {
	// 使用统一日志适配器
	log := logger.GetLoggerAdapter(logger.ModuleLogService)
	
	ls := &logService{
		logger: log,
		component: "log_service",
		fields: make(map[string]interface{}),
	}
	
	ls.logger.Info("日志服务已初始化")
	return ls
}

// NewLogServiceFromAdapter 从LoggerAdapter创建日志服务实例
func NewLogServiceFromAdapter(loggerAdapter *logger.LoggerAdapter) interfaces.LogService {
	ls := &logService{
		logger: loggerAdapter,
		component: "log_service",
		fields: make(map[string]interface{}),
	}
	
	return ls
}

// Info 记录信息级别日志
func (ls *logService) Info(msg string, args ...interface{}) {
	ls.logger.Info(msg, args...)
}

// Warn 记录警告级别日志
func (ls *logService) Warn(msg string, args ...interface{}) {
	ls.logger.Warn(msg, args...)
}

// Error 记录错误级别日志
func (ls *logService) Error(msg string, args ...interface{}) {
	ls.logger.Error(msg, args...)
}

// Debug 记录调试级别日志
func (ls *logService) Debug(msg string, args ...interface{}) {
	ls.logger.Debug(msg, args...)
}

// Fatal 记录致命错误日志并退出程序
func (ls *logService) Fatal(msg string, args ...interface{}) {
	ls.logger.Fatal(msg, args...)
}



// GetLogger 获取日志器实例
func (ls *logService) GetLogger() interface{} {
	return ls.logger
}

// WithTraceID 为日志添加追踪ID
func (ls *logService) WithTraceID(traceID string) interfaces.LogService {
	newFields := make(map[string]interface{})
	for k, v := range ls.fields {
		newFields[k] = v
	}
	newFields["trace_id"] = traceID
	newLogger := &logService{
		logger: ls.logger,
		component: ls.component,
		fields: newFields,
	}
	return newLogger
}

// WithFields 为日志添加字段
func (ls *logService) WithFields(fields map[string]interface{}) interfaces.LogService {
	newFields := make(map[string]interface{})
	for k, v := range ls.fields {
		newFields[k] = v
	}
	for k, v := range fields {
		newFields[k] = v
	}
	newLogger := &logService{
		logger: ls.logger,
		component: ls.component,
		fields: newFields,
	}
	return newLogger
}

// LogError 记录FlexProxyError类型的错误
func (ls *logService) LogError(err error, msg string, args ...interface{}) {
	// 先记录用户提供的消息
	if msg != "" {
		ls.logger.Error(msg, args...)
	}
	
	if flexErr, ok := err.(*errors.FlexProxyError); ok {
		errorMsg := fmt.Sprintf("FlexProxyError: %s [type=%s, code=%s, trace_id=%s, timestamp=%s]",
			flexErr.Message,
			flexErr.Type.String(),
			flexErr.Code.String(),
			flexErr.TraceID,
			flexErr.Timestamp.Format(constants.TimeFormatDefault))
		ls.logger.Error(errorMsg)
		
		// 记录错误详情
		if flexErr.Details != "" {
			ls.logger.Debug("错误详情: %s", flexErr.Details)
		}
		
		// 记录错误链
		if flexErr.Cause != nil {
			ls.logger.Debug("错误原因: %s", flexErr.Cause.Error())
		}
	} else {
		ls.Error("未知错误: %v", err)
	}
}