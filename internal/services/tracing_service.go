// Package services 提供各种服务实现
package services

import (
	"fmt"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/common/trace"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// TracingService 追踪服务实现
type tracingService struct {
	mu        sync.RWMutex
	config    *common.TracingConfig
	generator trace.TraceIDGenerator
	spans     map[string]*SpanInfo
	logger    logger.Logger
	enabled   bool
}

// SpanInfo 跨度信息
type SpanInfo struct {
	TraceID   string                 `json:"trace_id"`
	SpanID    string                 `json:"span_id"`
	ParentID  string                 `json:"parent_id,omitempty"`
	Operation string                 `json:"operation"`
	StartTime time.Time              `json:"start_time"`
	EndTime   *time.Time             `json:"end_time,omitempty"`
	Duration  *time.Duration         `json:"duration,omitempty"`
	Tags      map[string]interface{} `json:"tags,omitempty"`
	Logs      []LogEntry             `json:"logs,omitempty"`
	Status    SpanStatus             `json:"status"`
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time            `json:"timestamp"`
	Level     string               `json:"level"`
	Message   string               `json:"message"`
	Fields    map[string]interface{} `json:"fields,omitempty"`
}

// SpanStatus 跨度状态
type SpanStatus string

const (
	SpanStatusOK    SpanStatus = "OK"
	SpanStatusError SpanStatus = "ERROR"
	SpanStatusActive SpanStatus = "ACTIVE"
)

// NewTracingService 创建新的追踪服务
func NewTracingService(config *common.TracingConfig, log logger.Logger) interfaces.TracingService {
	if log == nil {
		log = logger.GetLogger("tracing")
	}

	if config == nil {
		config = &common.TracingConfig{
			Enabled:            false,
			HexGeneratorLength: 16,
			SequenceModulus:    1000000,
		}
	}

	// 根据配置选择追踪ID生成器
	var generator trace.TraceIDGenerator
	if config.HexGeneratorLength > 0 {
		generator = trace.NewHexGenerator(config.HexGeneratorLength)
	} else {
		generator = trace.UUIDGen
	}

	ts := &tracingService{
		config:    config,
		generator: generator,
		spans:     make(map[string]*SpanInfo),
		logger:    log,
		enabled:   config.Enabled,
	}

	ts.logger.Info("追踪服务已初始化")
	return ts
}

// IsEnabled 检查追踪是否启用
func (ts *tracingService) IsEnabled() bool {
	ts.mu.RLock()
	defer ts.mu.RUnlock()
	return ts.enabled
}

// Enable 启用追踪服务
func (ts *tracingService) Enable() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.enabled = true
	ts.logger.Info("追踪服务已启用")
	return nil
}

// Disable 禁用追踪服务
func (ts *tracingService) Disable() error {
	ts.mu.Lock()
	defer ts.mu.Unlock()

	ts.enabled = false
	ts.logger.Info("追踪服务已禁用")
	return nil
}

// StartSpan 开始一个新的跨度
func (ts *tracingService) StartSpan(traceID, operation string) (string, error) {
	if !ts.IsEnabled() {
		return "", fmt.Errorf("追踪服务未启用")
	}

	if operation == "" {
		return "", fmt.Errorf("操作名称不能为空")
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	// 如果没有提供traceID，生成一个新的
	if traceID == "" {
		traceID = ts.GenerateTraceID()
	}

	// 生成span ID
	spanID := ts.generator.Generate()

	// 创建span
	span := &SpanInfo{
		SpanID:    spanID,
		TraceID:   traceID,
		Operation: operation,
		StartTime: time.Now(),
		Status:    SpanStatusActive,
		Tags:      make(map[string]interface{}),
		Logs:      make([]LogEntry, 0),
	}

	ts.spans[spanID] = span

	ts.logger.Debug("开始新的跨度",
		"span_id", spanID,
		"trace_id", traceID,
		"operation", operation)

	return spanID, nil
}

// FinishSpan 结束span
func (ts *tracingService) FinishSpan(spanID string) error {
	if !ts.IsEnabled() {
		return fmt.Errorf("追踪服务未启用")
	}

	if spanID == "" {
		return fmt.Errorf("spanID不能为空")
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	span, exists := ts.spans[spanID]
	if !exists {
		ts.logger.Warn("尝试结束不存在的span", "spanID", spanID)
		return fmt.Errorf("span不存在: %s", spanID)
	}

	now := time.Now()
	span.EndTime = &now
	duration := now.Sub(span.StartTime)
	span.Duration = &duration
	span.Status = SpanStatusOK

	// 记录span完成
	ts.logger.Debug("Span已完成",
		"spanID", spanID,
		"duration", duration,
		"status", span.Status)

	// 清理已完成的span
	delete(ts.spans, spanID)
	return nil
}

// AddSpanTag 添加跨度标签
func (ts *tracingService) AddSpanTag(spanID string, key string, value interface{}) error {
	if !ts.IsEnabled() || spanID == "" {
		return fmt.Errorf("追踪服务未启用或spanID为空")
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	span, exists := ts.spans[spanID]
	if !exists {
		return fmt.Errorf("span不存在: %s", spanID)
	}

	span.Tags[key] = value
	return nil
}

// AddSpanLog 添加跨度日志
func (ts *tracingService) AddSpanLog(spanID string, level string, message string, fields map[string]interface{}) error {
	if !ts.IsEnabled() || spanID == "" {
		return fmt.Errorf("追踪服务未启用或spanID为空")
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	span, exists := ts.spans[spanID]
	if !exists {
		ts.logger.Warn(fmt.Sprintf("Span不存在: %s", spanID))
		return fmt.Errorf("span不存在: %s", spanID)
	}

	logEntry := LogEntry{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}

	span.Logs = append(span.Logs, logEntry)
	ts.logger.Debug(fmt.Sprintf("添加span日志: %s - %s", spanID, message))
	return nil
}

// GetSpan 获取span信息
func (ts *tracingService) GetSpan(spanID string) (interface{}, error) {
	if !ts.IsEnabled() {
		return nil, fmt.Errorf("追踪服务未启用")
	}

	if spanID == "" {
		return nil, fmt.Errorf("spanID不能为空")
	}

	ts.mu.RLock()
	defer ts.mu.RUnlock()

	span, exists := ts.spans[spanID]
	if !exists {
		return nil, fmt.Errorf("span不存在: %s", spanID)
	}

	return span, nil
}

// GetTraceSpans 获取追踪的所有跨度
func (ts *tracingService) GetTraceSpans(traceID string) ([]interface{}, error) {
	if !ts.IsEnabled() {
		return nil, fmt.Errorf("追踪服务未启用")
	}

	if traceID == "" {
		return nil, fmt.Errorf("traceID不能为空")
	}

	ts.mu.RLock()
	defer ts.mu.RUnlock()

	var spans []interface{}
	for _, span := range ts.spans {
		if span.TraceID == traceID {
			// 返回副本以避免并发修改
			spanCopy := *span
			spanCopy.Tags = make(map[string]interface{})
			for k, v := range span.Tags {
				spanCopy.Tags[k] = v
			}
			spanCopy.Logs = make([]LogEntry, len(span.Logs))
			copy(spanCopy.Logs, span.Logs)
			spans = append(spans, &spanCopy)
		}
	}

	return spans, nil
}

// InjectHeaders 注入追踪头
func (ts *tracingService) InjectHeaders(traceID string, headers map[string]string) error {
	if !ts.IsEnabled() {
		return fmt.Errorf("追踪服务未启用")
	}

	if traceID == "" {
		return fmt.Errorf("traceID不能为空")
	}

	if headers == nil {
		return fmt.Errorf("headers不能为空")
	}

	// 注入追踪头
	headers["X-Trace-ID"] = traceID

	// 如果有当前span，也注入span ID
	ts.mu.RLock()
	for _, span := range ts.spans {
		if span.TraceID == traceID && span.Status == SpanStatusActive {
			headers["X-Span-ID"] = span.SpanID
			break
		}
	}
	ts.mu.RUnlock()

	return nil
}

// ExtractHeaders 从HTTP头中提取追踪信息
func (ts *tracingService) ExtractHeaders(headers map[string]string) (string, error) {
	traceID := headers["X-Trace-ID"]
	if traceID == "" {
		return "", fmt.Errorf("未找到追踪ID")
	}
	return traceID, nil
}

// GenerateTraceID 生成新的追踪ID
func (ts *tracingService) GenerateTraceID() string {
	return ts.generator.Generate()
}

// CleanupOldSpans 清理旧的跨度数据
func (ts *tracingService) CleanupOldSpans(maxAge time.Duration) {
	if !ts.IsEnabled() {
		return
	}

	ts.mu.Lock()
	defer ts.mu.Unlock()

	now := time.Now()
	var cleanedCount int

	for spanID, span := range ts.spans {
		// 如果跨度已结束且超过最大年龄，则删除
		if span.EndTime != nil && now.Sub(*span.EndTime) > maxAge {
			delete(ts.spans, spanID)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		ts.logger.Debug(fmt.Sprintf("清理了 %d 个旧跨度", cleanedCount))
	}
}

// GetStats 获取追踪统计信息
func (ts *tracingService) GetStats() map[string]interface{} {
	ts.mu.RLock()
	defer ts.mu.RUnlock()

	stats := map[string]interface{}{
		"enabled":     ts.enabled,
		"total_spans": len(ts.spans),
		"generator":   fmt.Sprintf("%T", ts.generator),
	}

	// 统计不同状态的跨度
	var activeCount, okCount, errorCount int
	for _, span := range ts.spans {
		switch span.Status {
		case SpanStatusActive:
			activeCount++
		case SpanStatusOK:
			okCount++
		case SpanStatusError:
			errorCount++
		}
	}

	stats["active_spans"] = activeCount
	stats["completed_spans"] = okCount
	stats["error_spans"] = errorCount

	return stats
}