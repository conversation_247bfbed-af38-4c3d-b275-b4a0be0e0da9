// Package services 提供各种服务实现
package services

import (
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// nullCacheService 空缓存服务实现，用于禁用缓存功能时
type nullCacheService struct {
	logger logger.Logger
}

// NewNullCacheService 创建空缓存服务实例
func NewNullCacheService(log logger.Logger) interfaces.CacheService {
	if log == nil {
		log = logger.GetLogger("null-cache")
	}
	
	return &nullCacheService{
		logger: log,
	}
}

// GetDNSCache 获取DNS缓存（空实现）
func (ncs *nullCacheService) GetDNSCache(key string) ([]string, bool) {
	return nil, false
}

// SetDNSCache 设置DNS缓存（空实现）
func (ncs *nullCacheService) SetDNSCache(key string, value []string, ttl int) {
	// 空实现，不执行任何操作
}

// GetRegexCache 获取正则缓存（空实现）
func (ncs *nullCacheService) GetRegexCache(pattern string) (interface{}, bool) {
	return nil, false
}

// SetRegexCache 设置正则缓存（空实现）
func (ncs *nullCacheService) SetRegexCache(pattern string, compiled interface{}) {
	// 空实现，不执行任何操作
}

// UpdateProxyPool 更新代理池（空实现）
func (ncs *nullCacheService) UpdateProxyPool(proxies []string) {
	// 空实现，不执行任何操作
}

// GetCacheStats 获取缓存统计（空实现）
func (ncs *nullCacheService) GetCacheStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":     false,
		"dns_cache":   0,
		"regex_cache": 0,
		"proxy_pool":  0,
	}
}

// ClearAllCache 清空所有缓存（空实现）
func (ncs *nullCacheService) ClearAllCache() {
	// 空实现，不执行任何操作
}

// StartCleanupRoutine 启动清理协程（空实现）
func (ncs *nullCacheService) StartCleanupRoutine() {
	// 空实现，不执行任何操作
}
