// Package services 提供各种服务实现
package services

import (
	"github.com/flexp/flexp/internal/interfaces"
)

// nullLogService 空日志服务实现，用于禁用日志功能时
type nullLogService struct{}

// NewNullLogService 创建空日志服务实例
func NewNullLogService() interfaces.LogService {
	return &nullLogService{}
}

// Info 记录信息级别日志（空实现）
func (nls *nullLogService) Info(msg string, args ...interface{}) {
	// 空实现，不执行任何操作
}

// Warn 记录警告级别日志（空实现）
func (nls *nullLogService) Warn(msg string, args ...interface{}) {
	// 空实现，不执行任何操作
}

// Error 记录错误级别日志（空实现）
func (nls *nullLogService) Error(msg string, args ...interface{}) {
	// 空实现，不执行任何操作
}

// Debug 记录调试级别日志（空实现）
func (nls *nullLogService) Debug(msg string, args ...interface{}) {
	// 空实现，不执行任何操作
}

// Fatal 记录致命错误日志（空实现）
func (nls *nullLogService) Fatal(msg string, args ...interface{}) {
	// 空实现，不执行任何操作
}

// GetLogger 获取日志器实例（空实现，返回nil）
func (nls *nullLogService) GetLogger() interface{} {
	return nil
}

// WithTraceID 为日志添加追踪ID（空实现，返回自身）
func (nls *nullLogService) WithTraceID(traceID string) interfaces.LogService {
	return nls
}

// WithFields 为日志添加字段（空实现，返回自身）
func (nls *nullLogService) WithFields(fields map[string]interface{}) interfaces.LogService {
	return nls
}

// LogError 记录FlexProxyError类型的错误（空实现）
func (nls *nullLogService) LogError(err error, msg string, args ...interface{}) {
	// 空实现，不执行任何操作
}
