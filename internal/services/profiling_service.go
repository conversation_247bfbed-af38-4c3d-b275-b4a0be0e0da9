// Package services 提供各种服务实现
package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"path/filepath"
	"runtime"
	"runtime/pprof"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// ProfilingService 性能分析服务实现
type profilingService struct {
	mu           sync.RWMutex
	config       *common.ProfilingConfig
	logger       logger.Logger
	running      bool
	profiles     map[string]*ProfileSession
	server       *http.Server
	ctx          context.Context
	cancel       context.CancelFunc
}

// ProfileSession 性能分析会话
type ProfileSession struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Duration    time.Duration          `json:"duration"`
	FilePath    string                 `json:"file_path"`
	Status      string                 `json:"status"`
	Metadata    map[string]interface{} `json:"metadata"`
	file        *os.File
	active      bool
}

// ProfileType 性能分析类型
type ProfileType string

const (
	CPUProfile    ProfileType = "cpu"
	MemoryProfile ProfileType = "memory"
	BlockProfile  ProfileType = "block"
	MutexProfile  ProfileType = "mutex"
	GoroutineProfile ProfileType = "goroutine"
	HeapProfile   ProfileType = "heap"
	AllocsProfile ProfileType = "allocs"
	ThreadProfile ProfileType = "threadcreate"
)

// ProfileStats 性能分析统计
type ProfileStats struct {
	TotalSessions    int                    `json:"total_sessions"`
	ActiveSessions   int                    `json:"active_sessions"`
	CompletedSessions int                   `json:"completed_sessions"`
	FailedSessions   int                    `json:"failed_sessions"`
	TotalSize        int64                  `json:"total_size_bytes"`
	ByType           map[string]int         `json:"by_type"`
	LastUpdate       time.Time              `json:"last_update"`
}

// NewProfilingService 创建新的性能分析服务
func NewProfilingService(config *common.ProfilingConfig, log logger.Logger) interfaces.ProfilingService {
	if log == nil {
		log = logger.GetLogger("profiling")
	}

	if config == nil {
		config = &common.ProfilingConfig{
			Enabled:       true,
			CPUProfile:    true,
			MemoryProfile: true,
			BlockProfile:  false,
			MutexProfile:  false,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	ps := &profilingService{
		config:   config,
		logger:   log,
		profiles: make(map[string]*ProfileSession),
		ctx:      ctx,
		cancel:   cancel,
	}

	ps.logger.Info("性能分析服务已初始化")
	return ps
}

// Start 启动性能分析服务
func (ps *profilingService) Start() error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if ps.running {
		return fmt.Errorf("性能分析服务已在运行")
	}

	if !ps.config.Enabled {
		ps.logger.Info("性能分析服务已禁用")
		return nil
	}

	// 设置运行时性能分析参数
	ps.setupRuntimeProfiling()

	// 启动HTTP服务器
	if err := ps.startProfilingServer(); err != nil {
		return fmt.Errorf("启动性能分析服务器失败: %v", err)
	}

	// 启动清理任务
	go ps.cleanupRoutine()

	ps.running = true
	ps.logger.Info("性能分析服务已启动，端口: 6060")
	return nil
}

// Stop 停止性能分析服务
func (ps *profilingService) Stop() error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if !ps.running {
		return nil
	}

	ps.cancel()

	// 停止所有活动的性能分析会话
	for id, session := range ps.profiles {
		if session.active {
			ps.stopProfileSession(id)
		}
	}

	// 停止HTTP服务器
	if ps.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := ps.server.Shutdown(ctx); err != nil {
			ps.logger.Error(fmt.Sprintf("停止性能分析服务器失败: %v", err))
		}
	}

	ps.running = false
	ps.logger.Info("性能分析服务已停止")
	return nil
}

// IsEnabled 检查性能分析服务是否启用
func (ps *profilingService) IsEnabled() bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	return ps.config.Enabled && ps.running
}

// StartProfile 开始性能分析
func (ps *profilingService) StartProfile(profileType string, duration time.Duration) (string, error) {
	if !ps.IsEnabled() {
		return "", fmt.Errorf("性能分析服务未启用")
	}

	sessionID := fmt.Sprintf("%s_%d", profileType, time.Now().Unix())
	filePath := filepath.Join("./profiles", fmt.Sprintf("%s.prof", sessionID))

	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建性能分析文件失败: %v", err)
	}

	session := &ProfileSession{
		ID:        sessionID,
		Type:      string(profileType),
		StartTime: time.Now(),
		FilePath:  filePath,
		Status:    "running",
		Metadata:  make(map[string]interface{}),
		file:      file,
		active:    true,
	}

	ps.mu.Lock()
	ps.profiles[sessionID] = session
	ps.mu.Unlock()

	// 开始相应类型的性能分析
	if err := ps.startProfileByType(ProfileType(profileType), file); err != nil {
		file.Close()
		os.Remove(filePath)
		ps.mu.Lock()
		delete(ps.profiles, sessionID)
		ps.mu.Unlock()
		return "", fmt.Errorf("启动性能分析失败: %v", err)
	}

	// 如果指定了持续时间，自动停止
	if duration > 0 {
		go func() {
			time.Sleep(duration)
			ps.StopProfile(sessionID)
		}()
	}

	ps.logger.Info(fmt.Sprintf("开始性能分析: %s (类型: %s)", sessionID, profileType))
	return sessionID, nil
}

// StopProfile 停止性能分析
func (ps *profilingService) StopProfile(sessionID string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	return ps.stopProfileSession(sessionID)
}

// stopProfileSession 停止性能分析会话（内部方法）
func (ps *profilingService) stopProfileSession(sessionID string) error {
	session, exists := ps.profiles[sessionID]
	if !exists {
		return fmt.Errorf("性能分析会话不存在: %s", sessionID)
	}

	if !session.active {
		return fmt.Errorf("性能分析会话已停止: %s", sessionID)
	}

	// 停止相应类型的性能分析
	if err := ps.stopProfileByType(ProfileType(session.Type)); err != nil {
		ps.logger.Error(fmt.Sprintf("停止性能分析失败: %v", err))
	}

	// 关闭文件
	if session.file != nil {
		session.file.Close()
	}

	session.EndTime = time.Now()
	session.Duration = session.EndTime.Sub(session.StartTime)
	session.Status = "completed"
	session.active = false

	// 获取文件大小
	if stat, err := os.Stat(session.FilePath); err == nil {
		session.Metadata["file_size"] = stat.Size()
	}

	ps.logger.Info(fmt.Sprintf("停止性能分析: %s (耗时: %v)", sessionID, session.Duration))
	return nil
}

// GetProfile 获取性能分析结果
func (ps *profilingService) GetProfile(sessionID string) ([]byte, error) {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	session, exists := ps.profiles[sessionID]
	if !exists {
		return nil, fmt.Errorf("性能分析会话不存在: %s", sessionID)
	}

	// 读取文件内容
	if session.FilePath == "" {
		return nil, fmt.Errorf("性能分析文件路径为空")
	}

	data, err := os.ReadFile(session.FilePath)
	if err != nil {
		return nil, fmt.Errorf("读取性能分析文件失败: %v", err)
	}

	return data, nil
}

// ListProfiles 列出所有性能分析会话
func (ps *profilingService) ListProfiles() []interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	profiles := make([]interface{}, 0, len(ps.profiles))
	for _, session := range ps.profiles {
		sessionCopy := *session
		sessionCopy.file = nil // 不暴露文件句柄
		profiles = append(profiles, &sessionCopy)
	}

	return profiles
}

// DeleteProfile 删除性能分析会话
func (ps *profilingService) DeleteProfile(sessionID string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	session, exists := ps.profiles[sessionID]
	if !exists {
		return fmt.Errorf("性能分析会话不存在: %s", sessionID)
	}

	// 如果会话仍在运行，先停止它
	if session.active {
		ps.stopProfileSession(sessionID)
	}

	// 删除文件
	if err := os.Remove(session.FilePath); err != nil && !os.IsNotExist(err) {
		ps.logger.Error(fmt.Sprintf("删除性能分析文件失败: %v", err))
	}

	// 从内存中删除
	delete(ps.profiles, sessionID)

	ps.logger.Info(fmt.Sprintf("删除性能分析会话: %s", sessionID))
	return nil
}

// GetStats 获取性能分析统计信息
func (ps *profilingService) GetStats() map[string]interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	stats := map[string]interface{}{
		"by_type":     make(map[string]int),
		"last_update": time.Now(),
		"total_sessions": 0,
		"active_sessions": 0,
		"completed_sessions": 0,
		"failed_sessions": 0,
		"total_size_bytes": int64(0),
	}

	byType := stats["by_type"].(map[string]int)
	var totalSize int64
	for _, session := range ps.profiles {
		stats["total_sessions"] = stats["total_sessions"].(int) + 1
		byType[session.Type]++

		if session.active {
			stats["active_sessions"] = stats["active_sessions"].(int) + 1
		} else if session.Status == "completed" {
			stats["completed_sessions"] = stats["completed_sessions"].(int) + 1
		} else {
			stats["failed_sessions"] = stats["failed_sessions"].(int) + 1
		}

		if size, ok := session.Metadata["file_size"].(int64); ok {
			totalSize += size
		}
	}

	stats["total_size_bytes"] = totalSize
	stats["enabled"] = ps.config.Enabled
	stats["cpu_profile"] = ps.config.CPUProfile
	stats["memory_profile"] = ps.config.MemoryProfile
	stats["block_profile"] = ps.config.BlockProfile
	stats["mutex_profile"] = ps.config.MutexProfile
	return stats
}

// TakeSnapshot 拍摄快照
func (ps *profilingService) TakeSnapshot(profileType string) ([]byte, error) {
	sessionID, err := ps.StartProfile(profileType, 0)
	if err != nil {
		return nil, err
	}
	
	// 等待一小段时间让分析器收集数据
	time.Sleep(100 * time.Millisecond)
	
	// 停止分析
	if err := ps.StopProfile(sessionID); err != nil {
		return nil, err
	}
	
	// 获取分析结果
	return ps.GetProfile(sessionID)
}

// setupRuntimeProfiling 设置运行时性能分析参数
func (ps *profilingService) setupRuntimeProfiling() {
	if ps.config.BlockProfile {
		runtime.SetBlockProfileRate(1)
	}

	if ps.config.MutexProfile {
		runtime.SetMutexProfileFraction(1)
	}
}

// startProfilingServer 启动性能分析HTTP服务器
func (ps *profilingService) startProfilingServer() error {
	mux := http.NewServeMux()

	// 注册自定义端点
	mux.HandleFunc("/profiles", ps.handleProfiles)
	mux.HandleFunc("/profiles/", ps.handleProfileDetail)
	mux.HandleFunc("/stats", ps.handleStats)
	mux.HandleFunc("/snapshot", ps.handleSnapshot)

	ps.server = &http.Server{
		Addr: ":6061",
		Handler: mux,
	}

	go func() {
		if err := ps.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ps.logger.Error(fmt.Sprintf("性能分析服务器错误: %v", err))
		}
	}()

	return nil
}

// startProfileByType 根据类型开始性能分析
func (ps *profilingService) startProfileByType(profileType ProfileType, file *os.File) error {
	switch profileType {
	case CPUProfile:
		return pprof.StartCPUProfile(file)
	case MemoryProfile, HeapProfile:
		// 内存分析不需要启动，在停止时写入
		return nil
	case BlockProfile:
		runtime.SetBlockProfileRate(1)
		return nil
	case MutexProfile:
		runtime.SetMutexProfileFraction(1)
		return nil
	case GoroutineProfile, AllocsProfile, ThreadProfile:
		// 这些分析不需要启动，在停止时写入
		return nil
	default:
		return fmt.Errorf("不支持的性能分析类型: %s", profileType)
	}
}

// stopProfileByType 根据类型停止性能分析
func (ps *profilingService) stopProfileByType(profileType ProfileType) error {
	switch profileType {
	case CPUProfile:
		pprof.StopCPUProfile()
		return nil
	case MemoryProfile, HeapProfile:
		return pprof.WriteHeapProfile(ps.getCurrentFile(profileType))
	case BlockProfile:
		return pprof.Lookup("block").WriteTo(ps.getCurrentFile(profileType), 0)
	case MutexProfile:
		return pprof.Lookup("mutex").WriteTo(ps.getCurrentFile(profileType), 0)
	case GoroutineProfile:
		return pprof.Lookup("goroutine").WriteTo(ps.getCurrentFile(profileType), 0)
	case AllocsProfile:
		return pprof.Lookup("allocs").WriteTo(ps.getCurrentFile(profileType), 0)
	case ThreadProfile:
		return pprof.Lookup("threadcreate").WriteTo(ps.getCurrentFile(profileType), 0)
	default:
		return fmt.Errorf("不支持的性能分析类型: %s", profileType)
	}
}

// getCurrentFile 获取当前性能分析文件
func (ps *profilingService) getCurrentFile(profileType ProfileType) *os.File {
	for _, session := range ps.profiles {
		if session.Type == string(profileType) && session.active {
			return session.file
		}
	}
	return nil
}

// cleanupRoutine 清理例程
func (ps *profilingService) cleanupRoutine() {
	ticker := time.NewTicker(24 * time.Hour) // 每天清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ps.ctx.Done():
			return
		case <-ticker.C:
			ps.cleanupOldProfiles()
		}
	}
}

// cleanupOldProfiles 清理旧的性能分析文件
func (ps *profilingService) cleanupOldProfiles() {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	cutoff := time.Now().AddDate(0, 0, -7)
	var toDelete []string

	for id, session := range ps.profiles {
		if !session.active && session.EndTime.Before(cutoff) {
			toDelete = append(toDelete, id)
		}
	}

	for _, id := range toDelete {
		session := ps.profiles[id]
		if err := os.Remove(session.FilePath); err != nil && !os.IsNotExist(err) {
			ps.logger.Error(fmt.Sprintf("删除过期性能分析文件失败: %v", err))
		}
		delete(ps.profiles, id)
		ps.logger.Info(fmt.Sprintf("清理过期性能分析: %s", id))
	}
}

// HTTP处理器

// handleProfiles 处理性能分析列表请求
func (ps *profilingService) handleProfiles(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		profiles := ps.ListProfiles()
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(profiles)
	case http.MethodPost:
		ps.handleCreateProfile(w, r)
	default:
		w.WriteHeader(http.StatusMethodNotAllowed)
	}
}

// handleCreateProfile 处理创建性能分析请求
func (ps *profilingService) handleCreateProfile(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Type     string `json:"type"`
		Duration string `json:"duration"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	var duration time.Duration
	if req.Duration != "" {
		var err error
		duration, err = time.ParseDuration(req.Duration)
		if err != nil {
			http.Error(w, "Invalid duration format", http.StatusBadRequest)
			return
		}
	}

	sessionID, err := ps.StartProfile(req.Type, duration)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"session_id": sessionID})
}

// handleProfileDetail 处理性能分析详情请求
func (ps *profilingService) handleProfileDetail(w http.ResponseWriter, r *http.Request) {
	sessionID := r.URL.Path[len("/profiles/"):]
	if sessionID == "" {
		http.Error(w, "Session ID required", http.StatusBadRequest)
		return
	}

	switch r.Method {
	case http.MethodGet:
		session, err := ps.GetProfile(sessionID)
		if err != nil {
			http.Error(w, err.Error(), http.StatusNotFound)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(session)
	case http.MethodDelete:
		if err := ps.DeleteProfile(sessionID); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		w.WriteHeader(http.StatusNoContent)
	default:
		w.WriteHeader(http.StatusMethodNotAllowed)
	}
}

// handleStats 处理统计信息请求
func (ps *profilingService) handleStats(w http.ResponseWriter, r *http.Request) {
	stats := ps.GetStats()
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// handleSnapshot 处理快照请求
func (ps *profilingService) handleSnapshot(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		w.WriteHeader(http.StatusMethodNotAllowed)
		return
	}

	profileType := r.URL.Query().Get("type")
	if profileType == "" {
		profileType = "heap"
	}

	data, err := ps.TakeSnapshot(profileType)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=snapshot_%s_%d.prof", profileType, time.Now().Unix()))
	w.Write(data)
}

// StartCPUProfile 开始CPU性能分析
func (ps *profilingService) StartCPUProfile(filename string) error {
	if !ps.IsEnabled() {
		return fmt.Errorf("性能分析服务未启用")
	}

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建CPU性能分析文件失败: %v", err)
	}

	return pprof.StartCPUProfile(file)
}

// StopCPUProfile 停止CPU性能分析
func (ps *profilingService) StopCPUProfile() error {
	pprof.StopCPUProfile()
	return nil
}

// WriteHeapProfile 写入堆内存性能分析
func (ps *profilingService) WriteHeapProfile(filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建堆内存性能分析文件失败: %v", err)
	}
	defer file.Close()

	return pprof.WriteHeapProfile(file)
}

// WriteGoroutineProfile 写入协程性能分析
func (ps *profilingService) WriteGoroutineProfile(filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建协程性能分析文件失败: %v", err)
	}
	defer file.Close()

	return pprof.Lookup("goroutine").WriteTo(file, 0)
}

// WriteBlockProfile 写入阻塞性能分析
func (ps *profilingService) WriteBlockProfile(filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建阻塞性能分析文件失败: %v", err)
	}
	defer file.Close()

	return pprof.Lookup("block").WriteTo(file, 0)
}

// WriteMutexProfile 写入互斥锁性能分析
func (ps *profilingService) WriteMutexProfile(filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建互斥锁性能分析文件失败: %v", err)
	}
	defer file.Close()

	return pprof.Lookup("mutex").WriteTo(file, 0)
}

// GetProfileData 获取性能分析数据
func (ps *profilingService) GetProfileData(profileType string) ([]byte, error) {
	ps.mu.RLock()
	defer ps.mu.RUnlock()

	for _, session := range ps.profiles {
		if session.Type == profileType && !session.active {
			return os.ReadFile(session.FilePath)
		}
	}

	return nil, fmt.Errorf("未找到类型为 %s 的性能分析数据", profileType)
}

// SetSamplingRate 设置采样率
func (ps *profilingService) SetSamplingRate(rate int) error {
	if rate <= 0 {
		return fmt.Errorf("采样率必须大于0")
	}
	// 这里可以根据需要实现具体的采样率设置逻辑
	ps.logger.Info(fmt.Sprintf("设置采样率: %d", rate))
	return nil
}

// GetSamplingRate 获取采样率
func (ps *profilingService) GetSamplingRate() int {
	// 返回默认采样率
	return 100
}

// SetMemProfileRate 设置内存性能分析采样率
func (ps *profilingService) SetMemProfileRate(rate int) {
	runtime.MemProfileRate = rate
}

// GetMemProfileRate 获取内存性能分析采样率
func (ps *profilingService) GetMemProfileRate() int {
	return runtime.MemProfileRate
}

// SetBlockProfileRate 设置阻塞性能分析采样率
func (ps *profilingService) SetBlockProfileRate(rate int) {
	runtime.SetBlockProfileRate(rate)
}

// GetBlockProfileRate 获取阻塞性能分析采样率
func (ps *profilingService) GetBlockProfileRate() int {
	// runtime包没有提供获取BlockProfileRate的方法，返回默认值
	return 1
}

// SetMutexProfileFraction 设置互斥锁性能分析采样率
func (ps *profilingService) SetMutexProfileFraction(rate int) {
	runtime.SetMutexProfileFraction(rate)
}

// GetMutexProfileFraction 获取互斥锁性能分析采样率
func (ps *profilingService) GetMutexProfileFraction() int {
	// runtime包没有提供获取MutexProfileFraction的方法，返回默认值
	return 1
}

// GetRuntimeStats 获取运行时统计信息
func (ps *profilingService) GetRuntimeStats() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"goroutines":     runtime.NumGoroutine(),
		"cgo_calls":      runtime.NumCgoCall(),
		"alloc":          m.Alloc,
		"total_alloc":    m.TotalAlloc,
		"sys":            m.Sys,
		"lookups":        m.Lookups,
		"mallocs":        m.Mallocs,
		"frees":          m.Frees,
		"heap_alloc":     m.HeapAlloc,
		"heap_sys":       m.HeapSys,
		"heap_idle":      m.HeapIdle,
		"heap_inuse":     m.HeapInuse,
		"heap_released":  m.HeapReleased,
		"heap_objects":   m.HeapObjects,
		"stack_inuse":    m.StackInuse,
		"stack_sys":      m.StackSys,
		"gc_sys":         m.GCSys,
		"next_gc":        m.NextGC,
		"last_gc":        m.LastGC,
		"pause_total_ns": m.PauseTotalNs,
		"num_gc":         m.NumGC,
		"num_forced_gc":  m.NumForcedGC,
	}
}

// EnableProfiling 启用指定类型的性能分析
func (ps *profilingService) EnableProfiling(profileType string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	switch profileType {
	case "cpu":
		ps.config.CPUProfile = true
	case "memory", "heap":
		ps.config.MemoryProfile = true
	case "block":
		ps.config.BlockProfile = true
		runtime.SetBlockProfileRate(1)
	case "mutex":
		ps.config.MutexProfile = true
		runtime.SetMutexProfileFraction(1)
	default:
		return fmt.Errorf("不支持的性能分析类型: %s", profileType)
	}

	ps.logger.Info(fmt.Sprintf("已启用 %s 性能分析", profileType))
	return nil
}

// DisableProfiling 禁用指定类型的性能分析
func (ps *profilingService) DisableProfiling(profileType string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	switch profileType {
	case "cpu":
		ps.config.CPUProfile = false
	case "memory", "heap":
		ps.config.MemoryProfile = false
	case "block":
		ps.config.BlockProfile = false
		runtime.SetBlockProfileRate(0)
	case "mutex":
		ps.config.MutexProfile = false
		runtime.SetMutexProfileFraction(0)
	default:
		return fmt.Errorf("不支持的性能分析类型: %s", profileType)
	}

	ps.logger.Info(fmt.Sprintf("已禁用 %s 性能分析", profileType))
	return nil
}