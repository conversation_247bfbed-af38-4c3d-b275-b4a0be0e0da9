// Package services 提供各种服务实现
package services

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// ProxyService 提供代理管理功能
// proxyService 代理管理服务实现
type proxyService struct {
	mu            sync.RWMutex
	proxies       []string
	currentIndex  int
	failedProxies map[string]time.Time
	bannedIPs     map[string]time.Time
	bannedDomains map[string]time.Time
	trustedIPs    map[string]bool
	config        *common.Config
	cacheService  interfaces.CacheService
	logger        logger.Logger
	banCleaner    context.CancelFunc
}

// NewProxyService 创建新的代理服务实例
func NewProxyService(cacheService interfaces.CacheService, log logger.Logger) interfaces.ProxyService {
	if log == nil {
		log = logger.GetProxyServiceLogger().GetRawLogger()
	}
	
	return &proxyService{
		proxies:       make([]string, 0),
		currentIndex:  0,
		failedProxies: make(map[string]time.Time),
		bannedIPs:     make(map[string]time.Time),
		bannedDomains: make(map[string]time.Time),
		trustedIPs:    make(map[string]bool),
		cacheService:  cacheService,
		logger:        log,
	}
}

// GetNextProxy 获取下一个可用代理
func (ps *proxyService) GetNextProxy() (string, error) {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	if len(ps.proxies) == 0 {
		ps.logger.Error("代理池为空")
		return "", errors.NewFlexProxyError(
			errors.ErrTypeProxy,
			errors.ErrCodeProxyNotFound,
			"代理池为空",
		)
	}
	
	// 根据配置的IP轮换模式选择代理
	if ps.config != nil {
		switch ps.config.Global.IPRotationMode {
		case constants.StrategyRandom:
			return ps.getRandomProxy()
		case constants.StrategySmart:
			return ps.getSmartProxy()
		case constants.StrategyQuality:
			return ps.getQualityProxy()
		}
	}

	// 默认顺序轮换
	return ps.getSequentialProxy()
}

// getSequentialProxy 顺序获取代理
func (ps *proxyService) getSequentialProxy() (string, error) {
	startIndex := ps.currentIndex
	
	for i := 0; i < len(ps.proxies); i++ {
		proxy := ps.proxies[ps.currentIndex]
		ps.currentIndex = (ps.currentIndex + 1) % len(ps.proxies)
		
		// 检查代理是否可用
		if ps.isProxyAvailable(proxy) {
			ps.logger.Debug(fmt.Sprintf("选择代理: %s, 策略: %s", proxy, constants.StrategySequential))
			return proxy, nil
		}
		
		// 如果回到起始位置，说明所有代理都不可用
		if ps.currentIndex == startIndex {
			break
		}
	}
	
	ps.logger.Error("没有可用的代理")
	return "", errors.WrapErrorWithDetails(
		nil,
		errors.ErrTypeProxy,
		errors.ErrCodeProxyUnavailable,
		"没有可用的代理",
		"",
	)
}

// getRandomProxy 随机获取代理
func (ps *proxyService) getRandomProxy() (string, error) {
	availableProxies := make([]string, 0)
	
	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableProxies = append(availableProxies, proxy)
		}
	}
	
	if len(availableProxies) == 0 {
		ps.logger.Error("没有可用的代理")
		return "", errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeProxy,
			errors.ErrCodeProxyUnavailable,
			"没有可用的代理",
			"",
		)
	}
	
	// 简单的随机选择（可以使用更好的随机算法）
	index := time.Now().UnixNano() % int64(len(availableProxies))
	proxy := availableProxies[index]
	ps.logger.Debug(fmt.Sprintf("随机选择代理: %s, 策略: %s, 可用数量: %d", proxy, constants.StrategyRandom, len(availableProxies)))
	return proxy, nil
}

// getQualityProxy 基于质量获取代理
func (ps *proxyService) getQualityProxy() (string, error) {
	// 目前简化实现，优先选择成功率高的代理
	// 可以根据需要扩展为更复杂的质量评估算法

	availableProxies := make([]string, 0)
	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		ps.logger.Error("没有可用的代理")
		return "", errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeProxy,
			errors.ErrCodeProxyUnavailable,
			"没有可用的代理",
			"",
		)
	}

	// 简化实现：选择第一个可用代理（可以扩展为基于质量指标的选择）
	proxy := availableProxies[0]
	ps.logger.Debug(fmt.Sprintf("质量选择代理: %s, 策略: %s, 可用数量: %d", proxy, constants.StrategyQuality, len(availableProxies)))
	return proxy, nil
}

// getSmartProxy 智能获取代理
func (ps *proxyService) getSmartProxy() (string, error) {
	// 智能模式：根据当前情况选择最合适的策略
	// 1. 如果有高质量代理，优先使用
	// 2. 否则使用随机选择避免模式化

	availableProxies := make([]string, 0)
	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableProxies = append(availableProxies, proxy)
		}
	}

	if len(availableProxies) == 0 {
		ps.logger.Error("没有可用的代理")
		return "", errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeProxy,
			errors.ErrCodeProxyUnavailable,
			"没有可用的代理",
			"",
		)
	}

	// 智能选择：如果失败代理较少，使用顺序选择；否则使用随机选择
	var proxy string
	if len(ps.failedProxies) < len(ps.proxies)/2 {
		// 失败代理较少，使用顺序选择保持稳定性
		proxy = ps.getSequentialProxyFromList(availableProxies)
	} else {
		// 失败代理较多，使用随机选择增加成功概率
		index := time.Now().UnixNano() % int64(len(availableProxies))
		proxy = availableProxies[index]
	}

	ps.logger.Debug(fmt.Sprintf("智能选择代理: %s, 策略: %s, 可用数量: %d, 失败数量: %d", proxy, constants.StrategySmart, len(availableProxies), len(ps.failedProxies)))
	return proxy, nil
}

// getSequentialProxyFromList 从给定列表中顺序选择代理
func (ps *proxyService) getSequentialProxyFromList(proxies []string) string {
	if len(proxies) == 0 {
		return ""
	}

	// 使用当前索引在可用代理列表中选择
	index := ps.currentIndex % len(proxies)
	ps.currentIndex++
	return proxies[index]
}

// isProxyAvailable 检查代理是否可用
func (ps *proxyService) isProxyAvailable(proxy string) bool {
	// 检查是否在失败列表中
	if failTime, exists := ps.failedProxies[proxy]; exists {
		// 检查冷却时间
		cooldownTime := constants.DefaultRetryInterval
		if ps.config != nil && ps.config.Global.RetryProxyCooldownTime > 0 {
			cooldownTime = time.Duration(ps.config.Global.RetryProxyCooldownTime) * time.Second
		}
		
		if time.Since(failTime) < cooldownTime {
			return false
		}
		
		// 冷却时间已过，移除失败记录
		delete(ps.failedProxies, proxy)
		ps.logger.Debug(fmt.Sprintf("代理冷却时间已过，重新可用: %s", proxy))
	}
	
	return true
}

// MarkProxyFailed 标记代理失败
func (ps *proxyService) MarkProxyFailed(proxy string) {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	ps.failedProxies[proxy] = time.Now()
	ps.logger.Warn(fmt.Sprintf("代理标记为失败: %s, 失败数量: %d, 时间: %s", proxy, len(ps.failedProxies), time.Now().Format(constants.TimeFormatDefault)))
}

// MarkProxySuccess 标记代理成功
func (ps *proxyService) MarkProxySuccess(proxy string) {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	// 移除失败记录
	delete(ps.failedProxies, proxy)
	ps.logger.Debug(fmt.Sprintf("代理标记为成功: %s, 剩余失败数量: %d", proxy, len(ps.failedProxies)))
}

// GetProxyCount 获取代理数量
func (ps *proxyService) GetProxyCount() int {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	return len(ps.proxies)
}

// InitBanSystem 初始化封禁系统
func (ps *proxyService) InitBanSystem(config interface{}) {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	// 类型断言
	cfg, ok := config.(*common.Config)
	if !ok {
		ps.logger.Error("InitBanSystem: 无效的配置类型")
		return
	}
	ps.config = cfg
	
	// 初始化被阻止的IP
	for _, ip := range cfg.Global.BlockedIPs {
		ps.bannedIPs[ip] = time.Now().Add(24 * time.Hour) // 默认24小时
	}
	
	// 初始化被阻止的域名
	for _, domainConfig := range cfg.Global.BannedDomains {
		var domain string
		if domainConfig.Domain != "" {
			domain = domainConfig.Domain
		} else {
			continue
		}
		
		// 处理持续时间
		var duration time.Duration
		switch v := domainConfig.Duration.(type) {
		case int:
			duration = time.Duration(v) * time.Second
		case string:
			if v == "reboot" {
				duration = 24 * time.Hour // 默认24小时
			} else {
				continue
			}
		default:
			duration = 24 * time.Hour // 默认24小时
		}
		
		ps.bannedDomains[domain] = time.Now().Add(duration)
	}
	
	// 初始化受信任的IP
	for _, ip := range cfg.Global.TrustedIPs {
		ps.trustedIPs[ip] = true
	}
	
	ps.logger.Info(fmt.Sprintf("封禁系统已初始化，封禁IP: %d, 封禁域名: %d, 受信任IP: %d", len(ps.bannedIPs), len(ps.bannedDomains), len(ps.trustedIPs)))
}

// StartBanCleaner 启动封禁清理器
func (ps *proxyService) StartBanCleaner(ctx context.Context) {
	cleanerCtx, cancel := context.WithCancel(ctx)
	ps.banCleaner = cancel
	
	go func() {
		ticker := time.NewTicker(10 * time.Minute) // 每10分钟清理一次
		defer ticker.Stop()
		
		for {
			select {
			case <-cleanerCtx.Done():
				return
			case <-ticker.C:
				ps.cleanExpiredBans()
			}
		}
	}()
	
	ps.logger.Info("封禁清理器已启动")
}

// cleanExpiredBans 清理过期的封禁
func (ps *proxyService) cleanExpiredBans() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	now := time.Now()
	expiredIPs := 0
	expiredDomains := 0
	
	// 清理过期的IP封禁
	for ip, expireTime := range ps.bannedIPs {
		if now.After(expireTime) {
			delete(ps.bannedIPs, ip)
			expiredIPs++
			ps.logger.Debug(fmt.Sprintf("IP解封: %s", ip))
		}
	}
	
	// 清理过期的域名封禁
	for domain, expireTime := range ps.bannedDomains {
		if now.After(expireTime) {
			delete(ps.bannedDomains, domain)
			expiredDomains++
			ps.logger.Debug(fmt.Sprintf("域名解封: %s", domain))
		}
	}
	
	if expiredIPs > 0 || expiredDomains > 0 {
		ps.logger.Info(fmt.Sprintf("清理过期封禁项完成，清理IP: %d, 清理域名: %d, 剩余封禁IP: %d, 剩余封禁域名: %d", expiredIPs, expiredDomains, len(ps.bannedIPs), len(ps.bannedDomains)))
	}
}

// BanIP 封禁IP
func (ps *proxyService) BanIP(ip string, duration int) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	ps.bannedIPs[ip] = time.Now().Add(time.Duration(duration) * time.Second)
	ps.logger.Warn(fmt.Sprintf("IP已被封禁: %s, 持续时间: %d秒, 总封禁IP数: %d, 时间: %s", ip, duration, len(ps.bannedIPs), time.Now().Format(constants.TimeFormatDefault)))
	return nil
}

// BanDomain 封禁域名
func (ps *proxyService) BanDomain(domain string, duration int) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	ps.bannedDomains[domain] = time.Now().Add(time.Duration(duration) * time.Second)
	ps.logger.Warn(fmt.Sprintf("域名已被封禁: %s, 持续时间: %d秒, 总封禁域名数: %d, 时间: %s", domain, duration, len(ps.bannedDomains), time.Now().Format(constants.TimeFormatDefault)))
	return nil
}

// IsIPBanned 检查IP是否被封禁
func (ps *proxyService) IsIPBanned(ip string) bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	// 检查是否是受信任的IP
	if ps.trustedIPs[ip] {
		return false
	}
	
	// 检查是否被封禁
	if expireTime, exists := ps.bannedIPs[ip]; exists {
		return time.Now().Before(expireTime)
	}
	
	return false
}

// IsDomainBanned 检查域名是否被封禁
func (ps *proxyService) IsDomainBanned(domain string) bool {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	if expireTime, exists := ps.bannedDomains[domain]; exists {
		return time.Now().Before(expireTime)
	}
	
	return false
}

// UpdateProxyList 更新代理列表
func (ps *proxyService) UpdateProxyList(proxies []string) {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	oldCount := len(ps.proxies)
	ps.proxies = make([]string, len(proxies))
	copy(ps.proxies, proxies)
	ps.currentIndex = 0
	// 清理失败代理记录
	ps.failedProxies = make(map[string]time.Time)
	
	// 更新缓存服务中的代理池
	ps.cacheService.UpdateProxyPool(proxies)
	
	ps.logger.Info(fmt.Sprintf("代理列表已更新，旧数量: %d, 新数量: %d, 重置失败代理: true", oldCount, len(proxies)))
}

// GetBanStats 获取封禁统计信息
func (ps *proxyService) GetBanStats() map[string]interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	stats := map[string]interface{}{
		"banned_ips":      len(ps.bannedIPs),
		"banned_domains":  len(ps.bannedDomains),
		"trusted_ips":     len(ps.trustedIPs),
		"failed_proxies":  len(ps.failedProxies),
		"total_proxies":   len(ps.proxies),
		"current_index":   ps.currentIndex,
	}
	
	ps.logger.Debug(fmt.Sprintf("获取封禁统计: 封禁IP: %d, 封禁域名: %d, 受信任IP: %d, 失败代理: %d, 总代理: %d, 当前索引: %d", len(ps.bannedIPs), len(ps.bannedDomains), len(ps.trustedIPs), len(ps.failedProxies), len(ps.proxies), ps.currentIndex))
	return stats
}

// Stop 停止代理服务
func (ps *proxyService) Stop() {
	if ps.banCleaner != nil {
		ps.banCleaner()
		ps.logger.Info(fmt.Sprintf("代理服务已停止，总代理: %d, 失败代理: %d, 封禁IP: %d, 封禁域名: %d", len(ps.proxies), len(ps.failedProxies), len(ps.bannedIPs), len(ps.bannedDomains)))
	}
}

// UnbanIP 解封IP
func (ps *proxyService) UnbanIP(ip string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	if _, exists := ps.bannedIPs[ip]; !exists {
		ps.logger.Warn(fmt.Sprintf("尝试解封不存在的IP: %s", ip))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"IP不在封禁列表中",
			fmt.Sprintf("ip: %s", ip),
		)
	}
	
	delete(ps.bannedIPs, ip)
	ps.logger.Info(fmt.Sprintf("IP已解封: %s, 剩余封禁IP: %d", ip, len(ps.bannedIPs)))
	return nil
}

// UnbanDomain 解封域名
func (ps *proxyService) UnbanDomain(domain string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	if _, exists := ps.bannedDomains[domain]; !exists {
		ps.logger.Warn(fmt.Sprintf("尝试解封不存在的域名: %s", domain))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidInput,
			"域名不在封禁列表中",
			fmt.Sprintf("domain: %s", domain),
		)
	}
	
	delete(ps.bannedDomains, domain)
	ps.logger.Info(fmt.Sprintf("域名已解封: %s, 剩余封禁域名: %d", domain, len(ps.bannedDomains)))
	return nil
}

// GetProxyStats 获取代理统计信息
func (ps *proxyService) GetProxyStats() map[string]interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	availableCount := 0
	for _, proxy := range ps.proxies {
		if ps.isProxyAvailable(proxy) {
			availableCount++
		}
	}
	
	stats := map[string]interface{}{
		"total_proxies":     len(ps.proxies),
		"available_proxies": availableCount,
		"failed_proxies":    len(ps.failedProxies),
		"current_index":     ps.currentIndex,
		"rotation_mode":     "sequential",
	}
	
	if ps.config != nil {
		stats["rotation_mode"] = ps.config.Global.IPRotationMode
	}
	
	ps.logger.Debug(fmt.Sprintf("获取代理统计: 总代理: %d, 可用代理: %d, 失败代理: %d, 当前索引: %d, 轮换模式: %v", len(ps.proxies), availableCount, len(ps.failedProxies), ps.currentIndex, stats["rotation_mode"]))
	return stats
}

// ResetFailedProxies 重置失败代理记录
func (ps *proxyService) ResetFailedProxies() {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	failedCount := len(ps.failedProxies)
	ps.failedProxies = make(map[string]time.Time)
	
	ps.logger.Info(fmt.Sprintf("失败代理记录已重置，重置数量: %d, 总代理: %d", failedCount, len(ps.proxies)))
}