// Package services 提供各种服务实现
package services

import (
	"context"
	"fmt"
	"net"
	"time"

	"github.com/miekg/dns"
	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/common/trace"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// dnsService DNS解析服务实现
// DNSService 提供DNS解析功能
type dnsService struct {
	mode          string
	customServers []common.CustomDNSServer
	cacheService  interfaces.CacheService
	logger        logger.Logger
	cacheTTL      int
	noCache       bool
	timeout       time.Duration
}

// NewDNSService 创建新的DNS服务
func NewDNSService(cacheService interfaces.CacheService, loggerService interfaces.LogService) interfaces.DNSService {
	return &dnsService{
		mode:         constants.DNSModeLocal, // 默认本地解析
		cacheService: cacheService,
		logger:       logger.GetDNSServiceLogger().GetRawLogger(),
		cacheTTL:     constants.DefaultCacheTTLSeconds, // 默认5分钟TTL
		noCache:      false,
		timeout:      constants.DefaultDNSTimeout, // 默认5秒超时
	}
}

// SetMode 设置DNS解析模式
func (ds *dnsService) SetMode(mode string) error {
	traceID := trace.GenerateTraceID()
	
	switch mode {
	case constants.DNSModeLocal, constants.DNSModeRemote, constants.DNSModeCustom:
		ds.mode = mode
		ds.logger.Info(fmt.Sprintf("DNS解析模式已设置: %s [trace_id=%s]", mode, traceID))
		return nil
	default:
		ds.logger.Error(fmt.Sprintf("不支持的DNS解析模式: %s [trace_id=%s]", mode, traceID))
		return errors.WrapErrorWithDetails(
			nil,
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidConfig,
			"不支持的DNS解析模式",
			fmt.Sprintf("mode: %s", mode),
		)
	}
}

// SetCustomServers 设置自定义DNS服务器
func (ds *dnsService) SetCustomServers(servers []interface{}) error {
	traceID := trace.GenerateTraceID()
	
	if len(servers) == 0 {
		ds.logger.Error(fmt.Sprintf("自定义DNS服务器列表不能为空 [trace_id=%s]", traceID))
		return errors.NewFlexProxyError(
			errors.ErrTypeValidation,
			errors.ErrCodeInvalidConfig,
			"自定义DNS服务器列表不能为空",
		)
	}
	
	// 类型断言转换
	customServers := make([]common.CustomDNSServer, len(servers))
	for i, server := range servers {
		if customServer, ok := server.(common.CustomDNSServer); ok {
			customServers[i] = customServer
		} else {
			ds.logger.Error(fmt.Sprintf("无效的DNS服务器类型: index=%d [trace_id=%s]", i, traceID))
			return errors.NewFlexProxyError(
				errors.ErrTypeValidation,
				errors.ErrCodeInvalidConfig,
				"无效的DNS服务器类型",
			)
		}
	}
	
	ds.customServers = customServers
	ds.logger.Info(fmt.Sprintf("自定义DNS服务器已设置: 数量=%d [trace_id=%s]", len(customServers), traceID))
	return nil
}

// Resolve 将主机名解析为IP地址
func (ds *dnsService) Resolve(hostname string) ([]string, error) {
	traceID := trace.GenerateTraceID()
	
	// 检查缓存
	if !ds.noCache {
		if cached, found := ds.cacheService.GetDNSCache("resolve:" + hostname); found {
			ds.logger.Debug(fmt.Sprintf("DNS缓存命中: hostname=%s, ips=%v [trace_id=%s]", hostname, cached, traceID))
			return cached, nil
		}
	}
	
	var ips []string
	var err error
	
	switch ds.mode {
	case constants.DNSModeLocal:
		ips, err = ds.resolveLocal(hostname, traceID)
	case constants.DNSModeCustom:
		ips, err = ds.resolveCustom(hostname, traceID)
	case constants.DNSModeRemote:
		ips, err = ds.resolveRemote(hostname, traceID)
	default:
		ips, err = ds.resolveLocal(hostname, traceID)
	}
	
	if err != nil {
		ds.logger.Error(fmt.Sprintf("DNS解析失败: hostname=%s, mode=%s, error=%v [trace_id=%s]", hostname, ds.mode, err, traceID))
		return nil, errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeNetwork,
			errors.ErrCodeDNSResolutionFailed,
			"DNS解析失败",
			fmt.Sprintf("hostname: %s, mode: %s", hostname, ds.mode),
		)
	}
	
	// 缓存结果
	if !ds.noCache && len(ips) > 0 {
		ds.cacheService.SetDNSCache("resolve:"+hostname, ips, ds.cacheTTL)
	}
	
	ds.logger.Info(fmt.Sprintf("DNS解析成功: hostname=%s, ips=%v, mode=%s [trace_id=%s]", hostname, ips, ds.mode, traceID))
	return ips, nil
}

// ReverseResolve DNS反向解析
func (ds *dnsService) ReverseResolve(ip string) (string, error) {
	traceID := trace.GenerateTraceID()
	
	// 检查缓存
	if !ds.noCache {
		if cached, found := ds.cacheService.GetDNSCache("reverse:" + ip); found {
			if len(cached) > 0 {
				hostname := cached[0]
				ds.logger.Debug(fmt.Sprintf("反向DNS缓存命中: ip=%s, hostname=%s [trace_id=%s]", ip, hostname, traceID))
				return hostname, nil
			}
		}
	}
	
	var hostname string
	var err error
	
	switch ds.mode {
	case constants.DNSModeLocal:
		hostname, err = ds.reverseResolveLocal(ip, traceID)
	case constants.DNSModeCustom:
		hostname, err = ds.reverseResolveCustom(ip, traceID)
	case constants.DNSModeRemote:
		hostname, err = ds.reverseResolveRemote(ip, traceID)
	default:
		hostname, err = ds.reverseResolveLocal(ip, traceID)
	}
	
	if err != nil {
		ds.logger.Error(fmt.Sprintf("反向DNS解析失败: ip=%s, mode=%s, error=%v [trace_id=%s]", ip, ds.mode, err, traceID))
		return "", errors.WrapErrorWithDetails(
			err,
			errors.ErrTypeNetwork,
			errors.ErrCodeDNSResolutionFailed,
			"反向DNS解析失败",
			fmt.Sprintf("ip: %s, mode: %s", ip, ds.mode),
		)

	}
	
	// 缓存结果
	if !ds.noCache && hostname != "" {
		ds.cacheService.SetDNSCache("reverse:"+ip, []string{hostname}, ds.cacheTTL)
	}
	
	ds.logger.Debug(fmt.Sprintf("反向DNS解析成功: ip=%s, hostname=%s, mode=%s [trace_id=%s]", ip, hostname, ds.mode, traceID))
	return hostname, nil
}

// resolveLocal 本地DNS解析
func (ds *dnsService) resolveLocal(hostname string, traceID string) ([]string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), ds.timeout)
	defer cancel()
	
	ds.logger.Debug(fmt.Sprintf("开始本地DNS解析: hostname=%s, timeout=%s [trace_id=%s]", hostname, ds.timeout.String(), traceID))
	
	addrs, err := net.DefaultResolver.LookupIPAddr(ctx, hostname)
	if err != nil {
		ds.logger.Error(fmt.Sprintf("本地DNS解析失败: hostname=%s, error=%v [trace_id=%s]", hostname, err, traceID))
		return nil, err
	}
	
	ips := make([]string, 0, len(addrs))
	for _, addr := range addrs {
		ips = append(ips, addr.IP.String())
	}
	
	ds.logger.Debug(fmt.Sprintf("本地DNS解析完成: hostname=%s, count=%d [trace_id=%s]", hostname, len(ips), traceID))
	
	return ips, nil
}

// resolveCustom 自定义DNS服务器解析
func (ds *dnsService) resolveCustom(hostname string, traceID string) ([]string, error) {
	if len(ds.customServers) == 0 {
		ds.logger.Debug(fmt.Sprintf("无自定义DNS服务器，回退到本地解析: hostname=%s [trace_id=%s]", hostname, traceID))
		return ds.resolveLocal(hostname, traceID)
	}
	
	ds.logger.Debug(fmt.Sprintf("开始自定义DNS解析: hostname=%s, server_count=%d [trace_id=%s]", hostname, len(ds.customServers), traceID))
	
	// 尝试每个自定义DNS服务器
	var lastErr error
	for i, server := range ds.customServers {
		ds.logger.Debug(fmt.Sprintf("尝试DNS服务器: hostname=%s, server=%s, attempt=%d [trace_id=%s]", hostname, server.Server, i+1, traceID))
		
		c := dns.Client{
			Timeout: ds.timeout,
		}
		
		m := dns.Msg{}
		m.SetQuestion(dns.Fqdn(hostname), dns.TypeA)
		
		r, _, err := c.Exchange(&m, server.Server+":53")
		if err != nil {
			lastErr = err
			ds.logger.Warn(fmt.Sprintf("DNS服务器解析失败: hostname=%s, server=%s, error=%v [trace_id=%s]", hostname, server.Server, err, traceID))
			continue
		}
		
		ips := make([]string, 0)
		for _, ans := range r.Answer {
			if a, ok := ans.(*dns.A); ok {
				ips = append(ips, a.A.String())
			}
		}
		
		if len(ips) > 0 {
			ds.logger.Debug(fmt.Sprintf("自定义DNS解析成功: hostname=%s, server=%s, count=%d [trace_id=%s]", hostname, server.Server, len(ips), traceID))
			return ips, nil
		}
	}
	
	ds.logger.Error(fmt.Sprintf("所有自定义DNS服务器解析失败: hostname=%s, server_count=%d, last_error=%v [trace_id=%s]", hostname, len(ds.customServers), lastErr, traceID))
	return nil, errors.WrapErrorWithDetails(
		lastErr,
		errors.ErrTypeNetwork,
		errors.ErrCodeDNSResolutionFailed,
		"所有自定义DNS服务器解析失败",
		fmt.Sprintf("hostname: %s, server_count: %d", hostname, len(ds.customServers)),
	)
}

// resolveRemote 远程DNS解析（通过代理）
func (ds *dnsService) resolveRemote(hostname string, traceID string) ([]string, error) {
	ds.logger.Debug(fmt.Sprintf("开始远程DNS解析: hostname=%s, server=%s [trace_id=%s]", hostname, constants.DefaultPublicDNS, traceID))
	
	// 使用公共DNS服务器
	c := dns.Client{
		Timeout: ds.timeout,
	}
	
	m := dns.Msg{}
	m.SetQuestion(dns.Fqdn(hostname), dns.TypeA)
	
	r, _, err := c.Exchange(&m, constants.DefaultPublicDNS+":53")
	if err != nil {
		ds.logger.Error(fmt.Sprintf("远程DNS解析失败: hostname=%s, server=%s, error=%v [trace_id=%s]", hostname, constants.DefaultPublicDNS, err, traceID))
		return nil, err
	}
	
	ips := make([]string, 0)
	for _, ans := range r.Answer {
		if a, ok := ans.(*dns.A); ok {
			ips = append(ips, a.A.String())
		}
	}
	
	ds.logger.Debug(fmt.Sprintf("远程DNS解析完成: hostname=%s, server=%s, count=%d [trace_id=%s]", hostname, constants.DefaultPublicDNS, len(ips), traceID))
	
	return ips, nil
}

// reverseResolveLocal 本地反向DNS解析
func (ds *dnsService) reverseResolveLocal(ip string, traceID string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), ds.timeout)
	defer cancel()
	
	ds.logger.Debug(fmt.Sprintf("开始本地反向DNS解析: ip=%s [trace_id=%s]", ip, traceID))
	
	names, err := net.DefaultResolver.LookupAddr(ctx, ip)
	if err != nil {
		ds.logger.Error(fmt.Sprintf("本地反向DNS解析失败: ip=%s, error=%v [trace_id=%s]", ip, err, traceID))
		return "", err
	}
	
	if len(names) > 0 {
		ds.logger.Debug(fmt.Sprintf("本地反向DNS解析成功: ip=%s, hostname=%s [trace_id=%s]", ip, names[0], traceID))
		return names[0], nil
	}
	
	ds.logger.Warn(fmt.Sprintf("未找到反向DNS记录: ip=%s [trace_id=%s]", ip, traceID))
	return "", errors.WrapErrorWithDetails(
		nil,
		errors.ErrTypeNetwork,
		errors.ErrCodeDNSResolutionFailed,
		"未找到反向DNS记录",
		fmt.Sprintf("ip: %s", ip),
	)
}

// reverseResolveCustom 自定义DNS服务器反向解析
func (ds *dnsService) reverseResolveCustom(ip string, traceID string) (string, error) {
	if len(ds.customServers) == 0 {
		ds.logger.Debug(fmt.Sprintf("无自定义DNS服务器，回退到本地反向解析: ip=%s [trace_id=%s]", ip, traceID))
		return ds.reverseResolveLocal(ip, traceID)
	}
	
	// 使用第一个自定义DNS服务器
	server := ds.customServers[0]
	
	ds.logger.Debug(fmt.Sprintf("开始自定义反向DNS解析: ip=%s, server=%s [trace_id=%s]", ip, server.Server, traceID))
	
	c := dns.Client{
		Timeout: ds.timeout,
	}
	
	// 构造反向查询
	arpa, err := dns.ReverseAddr(ip)
	if err != nil {
		ds.logger.Error(fmt.Sprintf("构造反向DNS查询失败: ip=%s, error=%v [trace_id=%s]", ip, err, traceID))
		return "", err
	}
	
	m := dns.Msg{}
	m.SetQuestion(arpa, dns.TypePTR)
	
	r, _, err := c.Exchange(&m, server.Server+":53")
	if err != nil {
		ds.logger.Error(fmt.Sprintf("自定义反向DNS解析失败: ip=%s, server=%s, error=%v [trace_id=%s]", ip, server.Server, err, traceID))
		return "", err
	}
	
	for _, ans := range r.Answer {
		if ptr, ok := ans.(*dns.PTR); ok {
			ds.logger.Debug(fmt.Sprintf("自定义反向DNS解析成功: ip=%s, hostname=%s, server=%s [trace_id=%s]", ip, ptr.Ptr, server.Server, traceID))
			return ptr.Ptr, nil
		}
	}
	
	ds.logger.Warn(fmt.Sprintf("未找到反向DNS记录: ip=%s, server=%s [trace_id=%s]", ip, server.Server, traceID))
	return "", errors.WrapErrorWithDetails(
		nil,
		errors.ErrTypeNetwork,
		errors.ErrCodeDNSResolutionFailed,
		"未找到反向DNS记录",
		fmt.Sprintf("ip: %s, server: %s", ip, server.Server),
	)
}

// reverseResolveRemote 远程反向DNS解析（通过代理）
func (ds *dnsService) reverseResolveRemote(ip string, traceID string) (string, error) {
	// 这里需要通过代理进行反向DNS解析
	// 暂时回退到本地解析
	ds.logger.Warn(fmt.Sprintf("远程反向DNS解析暂未实现，回退到本地解析: ip=%s [trace_id=%s]", ip, traceID))
	return ds.reverseResolveLocal(ip, traceID)
}

// SetCacheTTL 设置缓存TTL
func (ds *dnsService) SetCacheTTL(ttl int) {
	traceID := trace.GenerateTraceID()
	ds.cacheTTL = ttl
	ds.logger.Info(fmt.Sprintf("DNS缓存TTL已设置: ttl=%d秒 [trace_id=%s]", ttl, traceID))
}

// SetNoCache 设置是否禁用缓存
func (ds *dnsService) SetNoCache(noCache bool) {
	traceID := trace.GenerateTraceID()
	ds.noCache = noCache
	cacheStatus := "启用"
	if noCache {
		cacheStatus = "禁用"
	}
	ds.logger.Info(fmt.Sprintf("DNS缓存状态已更新: disabled=%t, status=%s [trace_id=%s]", noCache, cacheStatus, traceID))
}

// SetTimeout 设置DNS查询超时时间
func (ds *dnsService) SetTimeout(timeout time.Duration) {
	traceID := trace.GenerateTraceID()
	ds.timeout = timeout
	ds.logger.Info(fmt.Sprintf("DNS查询超时时间已设置: timeout_duration=%s, timeout_seconds=%.2f [trace_id=%s]", timeout.String(), timeout.Seconds(), traceID))
}