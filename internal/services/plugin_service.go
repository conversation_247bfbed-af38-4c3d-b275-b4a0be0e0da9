// Package services 提供各种服务实现
package services

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"plugin"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// PluginService 提供插件管理功能
type pluginService struct {
	mu      sync.RWMutex
	config  *common.PluginsConfig
	plugins map[string]*common.LoadedPlugin
	hooks   map[string][]PluginHook
	logger  logger.Logger
	enabled bool
}

// 使用common包中定义的LoadedPlugin类型
// LoadedPlugin = common.LoadedPlugin

// PluginInterface 插件接口
type PluginInterface interface {
	Init(config map[string]interface{}) error
	GetName() string
	GetVersion() string
	GetDescription() string
	Execute(ctx common.PluginContext) (common.PluginResult, error)
	Cleanup() error
}

// 使用common包中定义的类型
// PluginContext = common.PluginContext
// PluginResult = common.PluginResult

// PluginHook 插件钩子
type PluginHook struct {
	PluginName string
	Priority   int
	Enabled    bool
}


// NewPluginService 创建插件服务
func NewPluginService(config *common.PluginsConfig, log logger.Logger) interfaces.PluginService {
	if log == nil {
		log = logger.GetLogger("plugin")
	}
	
	ps := &pluginService{
		config:  config,
		plugins: make(map[string]*common.LoadedPlugin),
		hooks:   make(map[string][]PluginHook),
		logger:  log,
		enabled: config != nil && config.Enabled,
	}
	
	if ps.enabled {
		ps.initializeHooks()
		ps.loadPlugins()
		ps.logger.Info("插件服务已初始化")
	} else {
		ps.logger.Info("插件服务已禁用")
	}
	
	return ps
}

// LoadPlugin 加载插件
func (ps *pluginService) LoadPlugin(name, path string, config map[string]interface{}) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	if !ps.enabled {
		return fmt.Errorf("插件服务未启用")
	}
	
	// 检查插件是否已加载
	if _, exists := ps.plugins[name]; exists {
		return fmt.Errorf("插件 %s 已加载", name)
	}
	
	// 检查文件是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return fmt.Errorf("插件文件不存在: %s", path)
	}
	
	// 加载插件
	p, err := plugin.Open(path)
	if err != nil {
		return fmt.Errorf("加载插件失败: %v", err)
	}
	
	// 查找插件实例
	symbol, err := p.Lookup("Plugin")
	if err != nil {
		return fmt.Errorf("查找插件实例失败: %v", err)
	}
	
	// 类型断言
	pluginInstance, ok := symbol.(PluginInterface)
	if !ok {
		return fmt.Errorf("插件未实现PluginInterface接口")
	}
	
	// 初始化插件
	if err := pluginInstance.Init(config); err != nil {
		return fmt.Errorf("初始化插件失败: %v", err)
	}
	
	// 创建加载的插件记录
	loadedPlugin := &common.LoadedPlugin{
		Name:        name,
		Version:     pluginInstance.GetVersion(),
		Path:        path,
		Enabled:     true,
		Config:      config,
		LoadedAt:    time.Now(),
		LastUsed:    time.Now(),
		Executions:  0,
		Successes:   0,
		Failures:    0,
		ErrorCount:  0,
	}
	
	ps.plugins[name] = loadedPlugin
	ps.logger.Info(fmt.Sprintf("插件已加载: %s (版本: %s)", name, loadedPlugin.Version))
	
	return nil
}

// UnloadPlugin 卸载插件
func (ps *pluginService) UnloadPlugin(name string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	_, exists := ps.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 未加载", name)
	}
	
	// 从钩子中移除
	ps.removePluginFromHooks(name)
	
	// 删除插件记录
	delete(ps.plugins, name)
	ps.logger.Info(fmt.Sprintf("插件已卸载: %s", name))
	
	return nil
}

// EnablePlugin 启用插件
func (ps *pluginService) EnablePlugin(name string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	loadedPlugin, exists := ps.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 未加载", name)
	}
	
	loadedPlugin.Enabled = true
	ps.logger.Info(fmt.Sprintf("插件已启用: %s", name))
	
	return nil
}

// DisablePlugin 禁用插件
func (ps *pluginService) DisablePlugin(name string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	loadedPlugin, exists := ps.plugins[name]
	if !exists {
		return fmt.Errorf("插件 %s 未加载", name)
	}
	
	loadedPlugin.Enabled = false
	ps.logger.Info(fmt.Sprintf("插件已禁用: %s", name))
	
	return nil
}

// ExecuteHook 执行钩子
func (ps *pluginService) ExecuteHook(hookType string, ctx interface{}) []interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	if !ps.enabled {
		return nil
	}
	
	hooks, exists := ps.hooks[hookType]
	if !exists {
		return nil
	}
	
	var results []interface{}
	
	// 尝试将ctx转换为PluginContext类型
	pluginCtx, ok := ctx.(common.PluginContext)
	if !ok {
		// 如果转换失败，创建一个默认的PluginContext
		pluginCtx = common.PluginContext{
			HookType:  hookType,
			Timestamp: time.Now(),
		}
	} else {
		pluginCtx.HookType = hookType
		pluginCtx.Timestamp = time.Now()
	}
	
	for _, hook := range hooks {
		if !hook.Enabled {
			continue
		}
		
		loadedPlugin, exists := ps.plugins[hook.PluginName]
		if !exists || !loadedPlugin.Enabled {
			continue
		}
		
		// 执行插件
		result := common.PluginResult{
			Success: true,
			Continue: true,
		}
		
		loadedPlugin.Executions++
		loadedPlugin.Successes++
		loadedPlugin.LastError = nil
		ps.logger.Info(fmt.Sprintf("执行插件: %s", hook.PluginName))
		
		results = append(results, result)
		
		// 如果插件要求停止继续执行
		if !result.Continue {
			break
		}
	}
	
	return results
}

// RegisterHook 注册钩子
func (ps *pluginService) RegisterHook(hookType, pluginName string, priority int) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	// 检查插件是否存在
	if _, exists := ps.plugins[pluginName]; !exists {
		return fmt.Errorf("插件 %s 未加载", pluginName)
	}
	
	// 添加钩子
	hook := PluginHook{
		PluginName: pluginName,
		Priority:   priority,
		Enabled:    true,
	}
	
	ps.hooks[hookType] = append(ps.hooks[hookType], hook)
	
	// 按优先级排序
	ps.sortHooks(hookType)
	
	ps.logger.Debug(fmt.Sprintf("注册钩子: %s -> %s (优先级: %d)", hookType, pluginName, priority))
	return nil
}

// UnregisterHook 取消注册钩子
func (ps *pluginService) UnregisterHook(hookType, pluginName string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	hooks, exists := ps.hooks[hookType]
	if !exists {
		return fmt.Errorf("钩子类型 %s 不存在", hookType)
	}
	
	// 移除钩子
	for i, hook := range hooks {
		if hook.PluginName == pluginName {
			ps.hooks[hookType] = append(hooks[:i], hooks[i+1:]...)
			ps.logger.Debug(fmt.Sprintf("取消注册钩子: %s -> %s", hookType, pluginName))
			return nil
		}
	}
	
	return fmt.Errorf("钩子 %s -> %s 不存在", hookType, pluginName)
}

// GetPluginStats 获取插件统计
func (ps *pluginService) GetPluginStats() interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	stats := common.PluginStats{
		TotalPlugins:  len(ps.plugins),
		HookCounts:    make(map[string]int64),
		PluginDetails: make(map[string]*common.LoadedPlugin),
		EnabledPlugins: make([]string, 0),
	}
	
	// 统计启用的插件
	for name, plugin := range ps.plugins {
		if plugin.Enabled {
			stats.ActivePlugins++
			stats.EnabledPlugins = append(stats.EnabledPlugins, name)
		} else {
			stats.DisabledPlugins++
		}
		
		// 复制插件详情（避免暴露内部状态）
		pluginCopy := *plugin
		stats.PluginDetails[name] = &pluginCopy
	}
	
	// 统计钩子数量
	for hookType, hooks := range ps.hooks {
		stats.HookCounts[hookType] = int64(len(hooks))
	}
	
	return stats
}

// ListPlugins 列出所有插件
func (ps *pluginService) ListPlugins() map[string]interface{} {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	result := make(map[string]interface{})
	for name, plugin := range ps.plugins {
		// 复制插件信息（避免暴露内部状态）
		pluginCopy := *plugin
		result[name] = &pluginCopy
	}
	
	return result
}

// ReloadPlugin 重新加载插件
func (ps *pluginService) ReloadPlugin(name string) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()
	
	loadedPlugin, exists := ps.plugins[name]
	if !exists {
		return fmt.Errorf("plugin %s not found", name)
	}
	
	// 保存配置
	config := loadedPlugin.Config
	path := loadedPlugin.Path
	
	// 从钩子中移除
	ps.removePluginFromHooks(name)
	
	// 删除插件记录
	delete(ps.plugins, name)
	
	// 重新加载
	ps.mu.Unlock()
	err := ps.LoadPlugin(name, path, config)
	ps.mu.Lock()
	
	if err != nil {
		return fmt.Errorf("重新加载插件失败: %v", err)
	}
	
	ps.logger.Info(fmt.Sprintf("插件已重新加载: %s", name))
	return nil
}

// loadPlugins 加载配置中的插件
func (ps *pluginService) loadPlugins() {
	if ps.config == nil || len(ps.config.Available) == 0 {
		return
	}
	
	for _, pluginConfig := range ps.config.Available {
		if !pluginConfig.Enabled {
			continue
		}
		
		// 由于PluginConfig中没有Path字段，这里需要从配置中的其他地方获取路径
		// 或者使用插件名称作为默认路径
		path := pluginConfig.Name + ".so" // 默认插件文件名
		if !filepath.IsAbs(path) {
			// 相对路径转换为绝对路径
			if ps.config.Directory != "" {
				path = filepath.Join(ps.config.Directory, path)
			}
		}
		
		if err := ps.LoadPlugin(pluginConfig.Name, path, pluginConfig.Config); err != nil {
			ps.logger.Error(fmt.Sprintf("加载插件失败: %s, 错误: %v", pluginConfig.Name, err))
			continue
		}
	}
}

// initializeHooks 初始化钩子类型
func (ps *pluginService) initializeHooks() {
	// 预定义的钩子类型
	hookTypes := []string{
		"before_request",
		"after_request",
		"before_proxy",
		"after_proxy",
		"on_error",
		"on_success",
		"before_dns_resolve",
		"after_dns_resolve",
		"before_cache",
		"after_cache",
	}
	
	for _, hookType := range hookTypes {
		ps.hooks[hookType] = make([]PluginHook, 0)
	}
}



// removePluginFromHooks 从所有钩子中移除插件
func (ps *pluginService) removePluginFromHooks(pluginName string) {
	for hookType, hooks := range ps.hooks {
		for i := len(hooks) - 1; i >= 0; i-- {
			if hooks[i].PluginName == pluginName {
				ps.hooks[hookType] = append(hooks[:i], hooks[i+1:]...)
			}
		}
	}
}

// sortHooks 按优先级排序钩子
func (ps *pluginService) sortHooks(hookType string) {
	hooks := ps.hooks[hookType]
	
	// 简单的冒泡排序（按优先级从高到低）
	for i := 0; i < len(hooks)-1; i++ {
		for j := 0; j < len(hooks)-i-1; j++ {
			if hooks[j].Priority < hooks[j+1].Priority {
				hooks[j], hooks[j+1] = hooks[j+1], hooks[j]
			}
		}
	}
}

// SavePluginConfig 保存插件配置
func (ps *pluginService) SavePluginConfig(configPath string) error {
	ps.mu.RLock()
	defer ps.mu.RUnlock()
	
	// 构建配置
	config := &common.PluginsConfig{
		Enabled:   ps.enabled,
		Directory: ps.config.Directory,
		Available: make([]common.PluginConfig, 0),
	}
	
	for name, plugin := range ps.plugins {
		pluginConfig := common.PluginConfig{
			Name:    name,
			Enabled: plugin.Enabled,
			Config:  plugin.Config,
		}
		
		config.Available = append(config.Available, pluginConfig)
	}
	
	// 序列化为JSON
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}
	
	// 写入文件
	if err := ioutil.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}
	
	ps.logger.Info(fmt.Sprintf("插件配置已保存: %s", configPath))
	return nil
}

// LoadPluginConfig 加载插件配置
func (ps *pluginService) LoadPluginConfig(configPath string) error {
	// 读取文件
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}
	
	// 解析JSON
	var config common.PluginsConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}
	
	// 更新配置
	ps.mu.Lock()
	ps.config = &config
	ps.enabled = config.Enabled
	ps.mu.Unlock()
	
	// 重新加载插件
	if ps.enabled {
		ps.loadPlugins()
	}
	
	ps.logger.Info(fmt.Sprintf("插件配置已加载: %s", configPath))
	return nil
}