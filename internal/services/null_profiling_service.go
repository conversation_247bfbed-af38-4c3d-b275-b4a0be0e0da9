// Package services 提供各种服务实现
package services

import (
	"github.com/mbndr/logo"
	"github.com/flexp/flexp/internal/interfaces"
)

// nullProfilingService 空性能分析服务实现，用于禁用性能分析功能时
type nullProfilingService struct {
	logger *logo.Logger
}

// NewNullProfilingService 创建空性能分析服务实例
func NewNullProfilingService(log *logo.Logger) interfaces.ProfilingService {
	if log == nil {
		log = logo.NewSimpleLogger(nil, logo.INFO, "null-profiling", false)
	}
	
	return &nullProfilingService{
		logger: log,
	}
}

// Start 启动性能分析服务（空实现）
func (nps *nullProfilingService) Start() error {
	nps.logger.Info("空性能分析服务已启动（无实际功能）")
	return nil
}

// Stop 停止性能分析服务（空实现）
func (nps *nullProfilingService) Stop() error {
	nps.logger.Info("空性能分析服务已停止")
	return nil
}

// IsEnabled 检查服务是否启用（空实现，总是返回false）
func (nps *nullProfilingService) IsEnabled() bool {
	return false
}

// StartCPUProfile 开始CPU性能分析（空实现）
func (nps *nullProfilingService) StartCPUProfile(filename string) error {
	return nil
}

// StopCPUProfile 停止CPU性能分析（空实现）
func (nps *nullProfilingService) StopCPUProfile() error {
	return nil
}

// WriteHeapProfile 写入堆性能分析（空实现）
func (nps *nullProfilingService) WriteHeapProfile(filename string) error {
	return nil
}

// WriteGoroutineProfile 写入协程性能分析（空实现）
func (nps *nullProfilingService) WriteGoroutineProfile(filename string) error {
	return nil
}

// WriteBlockProfile 写入阻塞性能分析（空实现）
func (nps *nullProfilingService) WriteBlockProfile(filename string) error {
	return nil
}

// WriteMutexProfile 写入互斥锁性能分析（空实现）
func (nps *nullProfilingService) WriteMutexProfile(filename string) error {
	return nil
}

// GetProfileData 获取性能分析数据（空实现）
func (nps *nullProfilingService) GetProfileData(profileType string) ([]byte, error) {
	return []byte{}, nil
}

// SetSamplingRate 设置采样率（空实现）
func (nps *nullProfilingService) SetSamplingRate(rate int) error {
	return nil
}

// GetSamplingRate 获取采样率（空实现，返回0）
func (nps *nullProfilingService) GetSamplingRate() int {
	return 0
}

// SetMemProfileRate 设置内存性能分析率（空实现）
func (nps *nullProfilingService) SetMemProfileRate(rate int) {
	// 空实现
}

// GetMemProfileRate 获取内存性能分析率（空实现，返回0）
func (nps *nullProfilingService) GetMemProfileRate() int {
	return 0
}

// SetBlockProfileRate 设置阻塞性能分析率（空实现）
func (nps *nullProfilingService) SetBlockProfileRate(rate int) {
	// 空实现
}

// GetBlockProfileRate 获取阻塞性能分析率（空实现，返回0）
func (nps *nullProfilingService) GetBlockProfileRate() int {
	return 0
}

// SetMutexProfileFraction 设置互斥锁性能分析比例（空实现）
func (nps *nullProfilingService) SetMutexProfileFraction(rate int) {
	// 空实现
}

// GetMutexProfileFraction 获取互斥锁性能分析比例（空实现，返回0）
func (nps *nullProfilingService) GetMutexProfileFraction() int {
	return 0
}

// GetRuntimeStats 获取运行时统计（空实现，返回空统计）
func (nps *nullProfilingService) GetRuntimeStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":    false,
		"goroutines": 0,
		"memory":     0,
		"gc":         0,
	}
}

// EnableProfiling 启用性能分析（空实现）
func (nps *nullProfilingService) EnableProfiling(profileType string) error {
	return nil
}

// DisableProfiling 禁用性能分析（空实现）
func (nps *nullProfilingService) DisableProfiling(profileType string) error {
	return nil
}
