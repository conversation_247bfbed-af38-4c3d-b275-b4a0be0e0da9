// Package services 提供各种服务实现
package services

import (
	"crypto/tls"
	"net/http"
	"time"

	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// nullSecurityService 空安全服务实现，用于禁用安全功能时
type nullSecurityService struct {
	logger logger.Logger
}

// NewNullSecurityService 创建空安全服务实例
func NewNullSecurityService(log logger.Logger) interfaces.SecurityService {
	if log == nil {
		log = logger.GetLogger("null-security")
	}
	
	return &nullSecurityService{
		logger: log,
	}
}

// Authenticate 认证请求（空实现，返回nil）
func (nss *nullSecurityService) Authenticate(r *http.Request) interface{} {
	return nil
}

// GenerateToken 生成令牌（空实现，返回空字符串）
func (nss *nullSecurityService) GenerateToken(userID string, scopes []string, duration time.Duration) (string, error) {
	return "", nil
}

// RevokeToken 撤销令牌（空实现，返回false）
func (nss *nullSecurityService) RevokeToken(token string) bool {
	return false
}

// ValidateScope 验证作用域（空实现，返回true）
func (nss *nullSecurityService) ValidateScope(token string, requiredScope string) bool {
	return true
}

// Encrypt 加密数据（空实现，返回原始数据）
func (nss *nullSecurityService) Encrypt(plaintext string) (interface{}, error) {
	return plaintext, nil
}

// Decrypt 解密数据（空实现，返回原始数据）
func (nss *nullSecurityService) Decrypt(ciphertext, nonce string) (string, error) {
	return ciphertext, nil
}

// GetTLSConfig 获取TLS配置（空实现，返回nil）
func (nss *nullSecurityService) GetTLSConfig() *tls.Config {
	return nil
}

// ValidateRequest 验证请求（空实现，返回nil）
func (nss *nullSecurityService) ValidateRequest(r *http.Request) error {
	return nil
}

// GetAuthStats 获取认证统计（空实现，返回空统计）
func (nss *nullSecurityService) GetAuthStats() map[string]interface{} {
	return map[string]interface{}{
		"enabled":       false,
		"total_tokens":  0,
		"active_tokens": 0,
	}
}

// CleanupExpiredTokens 清理过期令牌（空实现，返回0）
func (nss *nullSecurityService) CleanupExpiredTokens() int {
	return 0
}
