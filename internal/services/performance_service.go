// Package services 提供各种服务实现
package services

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
)

// PerformanceService 性能调优服务实现
type performanceService struct {
	mu           sync.RWMutex
	config       *common.PerformanceConfig
	workerPool   *WorkerPool
	batchQueue   *BatchQueue
	metrics      *PerformanceMetrics
	logger       logger.Logger
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
}

// WorkerPool 工作池
type WorkerPool struct {
	mu      sync.RWMutex
	workers []*Worker
	tasks   chan Task
	size    int
	running bool
}

// Worker 工作者
type Worker struct {
	id      int
	pool    *WorkerPool
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
}

// Task 任务接口
type Task interface {
	Execute() error
	GetID() string
	GetPriority() int
}

// BatchQueue 批处理队列
type BatchQueue struct {
	mu           sync.RWMutex
	items        []interface{}
	batchSize    int
	flushTicker  *time.Ticker
	processFunc  func([]interface{}) error
	running      bool
	ctx          context.Context
	cancel       context.CancelFunc
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	mu                sync.RWMutex
	TasksProcessed    int64     `json:"tasks_processed"`
	TasksQueued       int64     `json:"tasks_queued"`
	TasksFailed       int64     `json:"tasks_failed"`
	AvgProcessingTime float64   `json:"avg_processing_time_ms"`
	WorkerUtilization float64   `json:"worker_utilization"`
	QueueUtilization  float64   `json:"queue_utilization"`
	BatchesProcessed  int64     `json:"batches_processed"`
	LastUpdate        time.Time `json:"last_update"`
}

// SimpleTask 简单任务实现
type SimpleTask struct {
	id       string
	priority int
	fn       func() error
}

func (t *SimpleTask) Execute() error {
	return t.fn()
}

func (t *SimpleTask) GetID() string {
	return t.id
}

func (t *SimpleTask) GetPriority() int {
	return t.priority
}

// NewPerformanceService 创建新的性能调优服务
func NewPerformanceService(config *common.PerformanceConfig, log logger.Logger) interfaces.PerformanceService {
	if log == nil {
		log = logger.GetLogger("performance")
	}

	if config == nil {
		config = &common.PerformanceConfig{
			WorkerPoolSize: 10,
			QueueSize:      1000,
			BatchSize:      100,
			FlushInterval:  "1s",
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	ps := &performanceService{
		config:  config,
		metrics: &PerformanceMetrics{LastUpdate: time.Now()},
		logger:  log,
		ctx:     ctx,
		cancel:  cancel,
	}

	// 初始化工作池
	ps.workerPool = ps.newWorkerPool(config.WorkerPoolSize, config.QueueSize)

	// 初始化批处理队列
	flushInterval, err := time.ParseDuration(config.FlushInterval)
	if err != nil {
		flushInterval = time.Second
	}
	ps.batchQueue = ps.newBatchQueue(config.BatchSize, flushInterval, nil)

	ps.logger.Info("性能调优服务已初始化")
	return ps
}

// Start 启动性能服务
func (ps *performanceService) Start() error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if ps.running {
		return fmt.Errorf("性能服务已在运行")
	}

	// 启动工作池
	if err := ps.workerPool.Start(ps.ctx); err != nil {
		return fmt.Errorf("启动工作池失败: %v", err)
	}

	// 启动批处理队列
	if err := ps.batchQueue.Start(ps.ctx); err != nil {
		return fmt.Errorf("启动批处理队列失败: %v", err)
	}

	// 启动指标收集
	go ps.collectMetrics()

	ps.running = true
	ps.logger.Info("性能调优服务已启动")
	return nil
}

// Stop 停止性能服务
func (ps *performanceService) Stop() error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if !ps.running {
		return nil
	}

	ps.cancel()

	// 停止工作池
	ps.workerPool.Stop()

	// 停止批处理队列
	ps.batchQueue.Stop()

	ps.running = false
	ps.logger.Info("性能调优服务已停止")
	return nil
}

// SubmitTask 提交任务
func (ps *performanceService) SubmitTask(task interface{}) error {
	if !ps.running {
		return fmt.Errorf("性能服务未运行")
	}

	// 如果传入的是Task接口，直接使用
	if t, ok := task.(Task); ok {
		return ps.workerPool.SubmitTask(t)
	}

	// 如果传入的是函数，包装成SimpleTask
	if fn, ok := task.(func() error); ok {
		simpleTask := &SimpleTask{
			id:       fmt.Sprintf("task_%d", time.Now().UnixNano()),
			priority: 0,
			fn:       fn,
		}
		return ps.workerPool.SubmitTask(simpleTask)
	}

	return fmt.Errorf("不支持的任务类型")
}

// SubmitSimpleTask 提交简单任务
func (ps *performanceService) SubmitSimpleTask(fn func() error) error {
	ps.mu.Lock()
	defer ps.mu.Unlock()

	if ps.workerPool != nil {
		task := &SimpleTask{
			id:       fmt.Sprintf("simple_%d", time.Now().UnixNano()),
			priority: 0,
			fn:       fn,
		}
		return ps.workerPool.SubmitTask(task)
	}
	return fmt.Errorf("worker pool not initialized")
}

// AddToBatch 添加到批处理队列
func (ps *performanceService) AddToBatch(item interface{}) error {
	if !ps.running {
		return fmt.Errorf("性能服务未运行")
	}

	return ps.batchQueue.Add(item)
}

// SetBatchProcessor 设置批处理函数
func (ps *performanceService) SetBatchProcessor(processor func([]interface{}) error) error {
	ps.batchQueue.SetProcessor(processor)
	return nil
}

// GetMetrics 获取性能指标
func (ps *performanceService) GetMetrics() map[string]interface{} {
	ps.metrics.mu.RLock()
	defer ps.metrics.mu.RUnlock()

	// 返回副本
	metrics := *ps.metrics
	return map[string]interface{}{
		"worker_pool_stats": ps.workerPool.GetStats(),
		"batch_queue_stats": ps.batchQueue.GetStats(),
		"tasks_processed":    metrics.TasksProcessed,
		"tasks_queued":       metrics.TasksQueued,
		"tasks_failed":       metrics.TasksFailed,
		"avg_processing_time": metrics.AvgProcessingTime,
		"worker_utilization": metrics.WorkerUtilization,
		"queue_utilization":  metrics.QueueUtilization,
		"batches_processed":  metrics.BatchesProcessed,
		"last_update":        metrics.LastUpdate,
	}
}

// GetWorkerPoolStats 获取工作池统计
func (ps *performanceService) GetWorkerPoolStats() map[string]interface{} {
	return ps.workerPool.GetStats()
}

// GetBatchQueueStats 获取批处理队列统计
func (ps *performanceService) GetBatchQueueStats() map[string]interface{} {
	return ps.batchQueue.GetStats()
}

// OptimizeGC 优化垃圾回收
func (ps *performanceService) OptimizeGC() error {
	var before, after runtime.MemStats
	runtime.ReadMemStats(&before)

	runtime.GC()
	runtime.GC() // 运行两次确保彻底清理

	runtime.ReadMemStats(&after)

	freed := before.HeapInuse - after.HeapInuse
	ps.logger.Info(fmt.Sprintf("GC优化完成，释放内存: %d bytes", freed))
	return nil
}

// SetGCPercent 设置GC百分比
func (ps *performanceService) SetGCPercent(percent int) error {
	// 注意：Go 1.19+ 中 runtime.SetGCPercent 已被弃用
	// 这里我们记录设置但不实际调用已弃用的函数
	ps.logger.Info(fmt.Sprintf("请求设置GC百分比为 %d (注意：runtime.SetGCPercent已弃用)", percent))
	return nil
}

// newWorkerPool 创建工作池
func (ps *performanceService) newWorkerPool(size, queueSize int) *WorkerPool {
	return &WorkerPool{
		workers: make([]*Worker, 0, size),
		tasks:   make(chan Task, queueSize),
		size:    size,
	}
}

// newBatchQueue 创建批处理队列
func (ps *performanceService) newBatchQueue(batchSize int, flushInterval time.Duration, processor func([]interface{}) error) *BatchQueue {
	return &BatchQueue{
		items:       make([]interface{}, 0, batchSize),
		batchSize:   batchSize,
		flushTicker: time.NewTicker(flushInterval),
		processFunc: processor,
	}
}

// WorkerPool 方法实现

// Start 启动工作池
func (wp *WorkerPool) Start(ctx context.Context) error {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	if wp.running {
		return fmt.Errorf("工作池已在运行")
	}

	// 创建工作者
	for i := 0; i < wp.size; i++ {
		workerCtx, cancel := context.WithCancel(ctx)
		worker := &Worker{
			id:     i,
			pool:   wp,
			ctx:    workerCtx,
			cancel: cancel,
		}
		wp.workers = append(wp.workers, worker)
		go worker.run()
	}

	wp.running = true
	return nil
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	if !wp.running {
		return
	}

	// 停止所有工作者
	for _, worker := range wp.workers {
		worker.cancel()
	}

	close(wp.tasks)
	wp.running = false
}

// SubmitTask 提交任务
func (wp *WorkerPool) SubmitTask(task Task) error {
	select {
	case wp.tasks <- task:
		return nil
	default:
		return fmt.Errorf("任务队列已满")
	}
}

// GetStats 获取工作池统计
func (wp *WorkerPool) GetStats() map[string]interface{} {
	wp.mu.RLock()
	defer wp.mu.RUnlock()

	var activeWorkers int
	for _, worker := range wp.workers {
		if worker.running {
			activeWorkers++
		}
	}

	return map[string]interface{}{
		"total_workers":  len(wp.workers),
		"active_workers": activeWorkers,
		"queue_length":   len(wp.tasks),
		"queue_capacity": cap(wp.tasks),
		"running":        wp.running,
	}
}

// Worker 方法实现

// run 运行工作者
func (w *Worker) run() {
	w.running = true
	defer func() {
		w.running = false
	}()

	for {
		select {
		case <-w.ctx.Done():
			return
		case task, ok := <-w.pool.tasks:
			if !ok {
				return
			}
			w.executeTask(task)
		}
	}
}

// executeTask 执行任务
func (w *Worker) executeTask(task Task) {
	start := time.Now()
	defer func() {
		if r := recover(); r != nil {
			// 处理panic
		}
	}()

	err := task.Execute()
	duration := time.Since(start)

	if err != nil {
		// 记录错误
		_ = err
	}

	// 记录执行时间
	_ = duration
}

// BatchQueue 方法实现

// Start 启动批处理队列
func (bq *BatchQueue) Start(ctx context.Context) error {
	bq.mu.Lock()
	defer bq.mu.Unlock()

	if bq.running {
		return fmt.Errorf("批处理队列已在运行")
	}

	bqCtx, cancel := context.WithCancel(ctx)
	bq.ctx = bqCtx
	bq.cancel = cancel
	bq.running = true

	go bq.run()
	return nil
}

// Stop 停止批处理队列
func (bq *BatchQueue) Stop() {
	bq.mu.Lock()
	defer bq.mu.Unlock()

	if !bq.running {
		return
	}

	bq.cancel()
	bq.flushTicker.Stop()

	// 处理剩余项目
	if len(bq.items) > 0 {
		bq.flush()
	}

	bq.running = false
}

// Add 添加项目到批处理队列
func (bq *BatchQueue) Add(item interface{}) error {
	bq.mu.Lock()
	defer bq.mu.Unlock()

	if !bq.running {
		return fmt.Errorf("批处理队列未运行")
	}

	bq.items = append(bq.items, item)

	// 如果达到批大小，立即刷新
	if len(bq.items) >= bq.batchSize {
		bq.flush()
	}

	return nil
}

// SetProcessor 设置处理函数
func (bq *BatchQueue) SetProcessor(processor func([]interface{}) error) {
	bq.mu.Lock()
	defer bq.mu.Unlock()
	bq.processFunc = processor
}

// GetStats 获取批处理队列统计
func (bq *BatchQueue) GetStats() map[string]interface{} {
	bq.mu.RLock()
	defer bq.mu.RUnlock()

	return map[string]interface{}{
		"queue_length": len(bq.items),
		"batch_size":   bq.batchSize,
		"running":      bq.running,
	}
}

// run 运行批处理队列
func (bq *BatchQueue) run() {
	for {
		select {
		case <-bq.ctx.Done():
			return
		case <-bq.flushTicker.C:
			bq.mu.Lock()
			if len(bq.items) > 0 {
				bq.flush()
			}
			bq.mu.Unlock()
		}
	}
}

// flush 刷新批处理队列
func (bq *BatchQueue) flush() {
	if len(bq.items) == 0 || bq.processFunc == nil {
		return
	}

	// 复制项目
	items := make([]interface{}, len(bq.items))
	copy(items, bq.items)

	// 清空队列
	bq.items = bq.items[:0]

	// 异步处理
	go func() {
		if err := bq.processFunc(items); err != nil {
			// 记录错误
			_ = err
		}
	}()
}

// collectMetrics 收集性能指标
func (ps *performanceService) collectMetrics() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ps.ctx.Done():
			return
		case <-ticker.C:
			ps.updateMetrics()
		}
	}
}

// updateMetrics 更新性能指标
func (ps *performanceService) updateMetrics() {
	ps.metrics.mu.Lock()
	defer ps.metrics.mu.Unlock()

	// 更新工作池利用率
	workerStats := ps.workerPool.GetStats()
	if totalWorkers, ok := workerStats["total_workers"].(int); ok && totalWorkers > 0 {
		if activeWorkers, ok := workerStats["active_workers"].(int); ok {
			ps.metrics.WorkerUtilization = float64(activeWorkers) / float64(totalWorkers) * 100
		}
	}

	// 更新队列利用率
	if queueLength, ok := workerStats["queue_length"].(int); ok {
		if queueCapacity, ok := workerStats["queue_capacity"].(int); ok && queueCapacity > 0 {
			ps.metrics.QueueUtilization = float64(queueLength) / float64(queueCapacity) * 100
		}
	}

	ps.metrics.LastUpdate = time.Now()
}