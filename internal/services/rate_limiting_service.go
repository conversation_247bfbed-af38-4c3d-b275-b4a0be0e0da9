// Package services 提供各种服务实现
package services

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/internal/interfaces"
)

// RateLimitingService 提供限流功能
type rateLimitingService struct {
	mu       sync.RWMutex
	config   *common.RateLimitingConfig
	limiters map[string]RateLimiter
	logger   logger.Logger
	running  bool
	ctx      context.Context
	cancel   context.CancelFunc
}

// RateLimiter 限流器接口
type RateLimiter interface {
	Allow() bool
	AllowN(n int) bool
	Wait(ctx context.Context) error
	WaitN(ctx context.Context, n int) error
	Reset()
	GetStats() RateLimitStats
}

// RateLimitStats 限流统计
type RateLimitStats struct {
	TotalRequests   int64     `json:"total_requests"`
	AllowedRequests int64     `json:"allowed_requests"`
	BlockedRequests int64     `json:"blocked_requests"`
	LastReset       time.Time `json:"last_reset"`
	CurrentTokens   int       `json:"current_tokens"`
	MaxTokens       int       `json:"max_tokens"`
}

// TokenBucketLimiter 令牌桶限流器
type TokenBucketLimiter struct {
	mu           sync.Mutex
	tokens       int
	maxTokens    int
	refillRate   int
	lastRefill   time.Time
	stats        RateLimitStats
}

// LeakyBucketLimiter 漏桶限流器
type LeakyBucketLimiter struct {
	mu         sync.Mutex
	capacity   int
	leakRate   int
	current    int
	lastLeak   time.Time
	stats      RateLimitStats
}

// FixedWindowLimiter 固定窗口限流器
type FixedWindowLimiter struct {
	mu          sync.Mutex
	windowSize  time.Duration
	maxRequests int
	current     int
	windowStart time.Time
	stats       RateLimitStats
}

// SlidingWindowLimiter 滑动窗口限流器
type SlidingWindowLimiter struct {
	mu          sync.Mutex
	windowSize  time.Duration
	maxRequests int
	requests    []time.Time
	stats       RateLimitStats
}

// NewRateLimitingService 创建限流服务
func NewRateLimitingService(config *common.RateLimitingConfig, log logger.Logger) interfaces.RateLimitingService {
	if log == nil {
		log = logger.GetRateLimitingServiceLogger().GetRawLogger()
	}
	
	ctx, cancel := context.WithCancel(context.Background())
	
	rls := &rateLimitingService{
		config:   config,
		limiters: make(map[string]RateLimiter),
		logger:   log,
		ctx:      ctx,
		cancel:   cancel,
	}
	
	rls.logger.Info("限流服务已初始化")
	return rls
}

// GetLimiterType 获取限流器类型
func (rls *rateLimitingService) GetLimiterType() string {
	if rls.config != nil && rls.config.Algorithm != "" {
		return rls.config.Algorithm
	}
	return "token_bucket" // 默认类型
}

// SetLimiterType 设置限流器类型
func (rls *rateLimitingService) SetLimiterType(limiterType string) error {
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	if rls.config == nil {
		return fmt.Errorf("config is nil")
	}
	
	rls.config.Algorithm = limiterType
	return nil
}

// Start 启动限流服务
func (rls *rateLimitingService) Start() error {
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	if !rls.config.Enabled {
		rls.logger.Info("限流服务已禁用")
		return nil
	}
	
	if rls.running {
		rls.logger.Warn("限流服务已在运行")
		return nil
	}
	
	// 启动清理协程
	go rls.cleanup()
	
	rls.running = true
	rls.logger.Info("限流服务已启动")
	return nil
}

// Stop 停止限流服务
func (rls *rateLimitingService) Stop() error {
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	if !rls.running {
		return nil
	}
	
	rls.cancel()
	rls.running = false
	rls.logger.Info("限流服务已停止")
	return nil
}

// CheckLimit 检查是否允许请求
func (rls *rateLimitingService) CheckLimit(clientIP string) bool {
	if !rls.config.Enabled {
		return true
	}
	
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	limiter, exists := rls.limiters[clientIP]
	if !exists {
		limiter = rls.createLimiter()
		rls.limiters[clientIP] = limiter
	}
	
	allowed := limiter.Allow()
	if !allowed {
		rls.logger.Debug(fmt.Sprintf("限流阻止请求: IP=%s", clientIP))
	}
	
	return allowed
}

// CheckLimitN 检查是否允许N个请求
func (rls *rateLimitingService) CheckLimitN(clientIP string, n int) bool {
	if !rls.config.Enabled {
		return true
	}
	
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	limiter, exists := rls.limiters[clientIP]
	if !exists {
		limiter = rls.createLimiter()
		rls.limiters[clientIP] = limiter
	}
	
	allowed := limiter.AllowN(n)
	if !allowed {
		rls.logger.Debug(fmt.Sprintf("限流阻止%d个请求: IP=%s", n, clientIP))
	}
	
	return allowed
}

// WaitForLimit 等待限流允许
func (rls *rateLimitingService) WaitForLimit(ctx context.Context, clientIP string) error {
	if !rls.config.Enabled {
		return nil
	}
	
	rls.mu.Lock()
	limiter, exists := rls.limiters[clientIP]
	if !exists {
		limiter = rls.createLimiter()
		rls.limiters[clientIP] = limiter
	}
	rls.mu.Unlock()
	
	return limiter.Wait(ctx)
}

// GetStats 获取限流统计
func (rls *rateLimitingService) GetStats() map[string]interface{} {
	rls.mu.RLock()
	defer rls.mu.RUnlock()
	
	stats := map[string]interface{}{
		"enabled":        rls.config.Enabled,
		"algorithm":      rls.config.Algorithm,
		"rate":           rls.config.Rate,
		"burst":          rls.config.Burst,
		"active_clients": len(rls.limiters),
		"client_stats":   make(map[string]RateLimitStats),
	}
	
	clientStats := stats["client_stats"].(map[string]RateLimitStats)
	for ip, limiter := range rls.limiters {
		clientStats[ip] = limiter.GetStats()
	}
	
	return stats
}

// ResetLimit 重置指定客户端的限流
func (rls *rateLimitingService) ResetLimit(clientIP string) {
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	if limiter, exists := rls.limiters[clientIP]; exists {
		limiter.Reset()
		rls.logger.Debug(fmt.Sprintf("重置限流: IP=%s", clientIP))
	}
}

// ResetAllLimits 重置所有限流
func (rls *rateLimitingService) ResetAllLimits() {
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	for ip, limiter := range rls.limiters {
		limiter.Reset()
		rls.logger.Debug(fmt.Sprintf("重置限流: IP=%s", ip))
	}
	
	rls.logger.Info("已重置所有限流")
}

// IsAllowed 检查是否允许请求（匹配接口）
func (rls *rateLimitingService) IsAllowed(key string, limit int, window time.Duration) bool {
	return rls.CheckLimit(key)
}

// ResetLimiter 重置指定限流器（匹配接口）
func (rls *rateLimitingService) ResetLimiter(key string) error {
	rls.ResetLimit(key)
	return nil
}

// SetGlobalLimit 设置全局限制（匹配接口）
func (rls *rateLimitingService) SetGlobalLimit(limit int, window time.Duration) error {
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	if rls.config == nil {
		return fmt.Errorf("config is nil")
	}
	
	rls.config.Rate = limit
	rls.config.Window = window.String()
	return nil
}

// GetRemainingRequests 获取剩余请求数
func (rls *rateLimitingService) GetRemainingRequests(key string) int {
	rls.mu.RLock()
	defer rls.mu.RUnlock()
	
	limiter, exists := rls.limiters[key]
	if !exists {
		// 如果限流器不存在，返回最大允许数
		return rls.config.Rate
	}
	
	stats := limiter.GetStats()
	return stats.CurrentTokens
}

// createLimiter 创建限流器
func (rls *rateLimitingService) createLimiter() RateLimiter {
	switch rls.config.Algorithm {
	case "token_bucket":
		return NewTokenBucketLimiter(rls.config.Burst, rls.config.Rate)
	case "leaky_bucket":
		return NewLeakyBucketLimiter(rls.config.Burst, rls.config.Rate)
	case "fixed_window":
		window, _ := time.ParseDuration(rls.config.Window)
		return NewFixedWindowLimiter(rls.config.Rate, window)
	case "sliding_window":
		window, _ := time.ParseDuration(rls.config.Window)
		return NewSlidingWindowLimiter(rls.config.Rate, window)
	default:
		return NewTokenBucketLimiter(rls.config.Burst, rls.config.Rate)
	}
}

// cleanup 清理过期的限流器
func (rls *rateLimitingService) cleanup() {
	cleanupPeriod, _ := time.ParseDuration(rls.config.CleanupPeriod)
	if cleanupPeriod == 0 {
		cleanupPeriod = 5 * time.Minute
	}
	
	ticker := time.NewTicker(cleanupPeriod)
	defer ticker.Stop()
	
	for {
		select {
		case <-rls.ctx.Done():
			return
		case <-ticker.C:
			rls.cleanupExpiredLimiters()
		}
	}
}

// cleanupExpiredLimiters 清理过期的限流器
func (rls *rateLimitingService) cleanupExpiredLimiters() {
	rls.mu.Lock()
	defer rls.mu.Unlock()
	
	// 这里可以根据需要实现清理逻辑
	// 例如：清理长时间未使用的限流器
	rls.logger.Debug("执行限流器清理")
}

// NewTokenBucketLimiter 创建令牌桶限流器
func NewTokenBucketLimiter(maxTokens, refillRate int) *TokenBucketLimiter {
	return &TokenBucketLimiter{
		tokens:     maxTokens,
		maxTokens:  maxTokens,
		refillRate: refillRate,
		lastRefill: time.Now(),
		stats: RateLimitStats{
			LastReset:     time.Now(),
			CurrentTokens: maxTokens,
			MaxTokens:     maxTokens,
		},
	}
}

// Allow 检查是否允许请求
func (tb *TokenBucketLimiter) Allow() bool {
	return tb.AllowN(1)
}

// AllowN 检查是否允许N个请求
func (tb *TokenBucketLimiter) AllowN(n int) bool {
	tb.mu.Lock()
	defer tb.mu.Unlock()
	
	tb.refill()
	tb.stats.TotalRequests++
	
	if tb.tokens >= n {
		tb.tokens -= n
		tb.stats.AllowedRequests++
		tb.stats.CurrentTokens = tb.tokens
		return true
	}
	
	tb.stats.BlockedRequests++
	return false
}

// Wait 等待令牌可用
func (tb *TokenBucketLimiter) Wait(ctx context.Context) error {
	return tb.WaitN(ctx, 1)
}

// WaitN 等待N个令牌可用
func (tb *TokenBucketLimiter) WaitN(ctx context.Context, n int) error {
	for {
		if tb.AllowN(n) {
			return nil
		}
		
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(time.Millisecond * 100):
			// 继续等待
		}
	}
}

// Reset 重置令牌桶
func (tb *TokenBucketLimiter) Reset() {
	tb.mu.Lock()
	defer tb.mu.Unlock()
	
	tb.tokens = tb.maxTokens
	tb.lastRefill = time.Now()
	tb.stats = RateLimitStats{
		LastReset:     time.Now(),
		CurrentTokens: tb.maxTokens,
		MaxTokens:     tb.maxTokens,
	}
}

// GetStats 获取统计信息
func (tb *TokenBucketLimiter) GetStats() RateLimitStats {
	tb.mu.Lock()
	defer tb.mu.Unlock()
	
	tb.stats.CurrentTokens = tb.tokens
	return tb.stats
}

// refill 补充令牌
func (tb *TokenBucketLimiter) refill() {
	now := time.Now()
	elapsed := now.Sub(tb.lastRefill)
	
	tokensToAdd := int(elapsed.Seconds()) * tb.refillRate
	if tokensToAdd > 0 {
		tb.tokens = min(tb.maxTokens, tb.tokens+tokensToAdd)
		tb.lastRefill = now
	}
}

// NewLeakyBucketLimiter 创建漏桶限流器
func NewLeakyBucketLimiter(capacity, leakRate int) *LeakyBucketLimiter {
	return &LeakyBucketLimiter{
		capacity: capacity,
		leakRate: leakRate,
		current:  0,
		lastLeak: time.Now(),
		stats: RateLimitStats{
			LastReset: time.Now(),
			MaxTokens: capacity,
		},
	}
}

// Allow 检查是否允许请求
func (lb *LeakyBucketLimiter) Allow() bool {
	return lb.AllowN(1)
}

// AllowN 检查是否允许N个请求
func (lb *LeakyBucketLimiter) AllowN(n int) bool {
	lb.mu.Lock()
	defer lb.mu.Unlock()
	
	lb.leak()
	lb.stats.TotalRequests++
	
	if lb.current+n <= lb.capacity {
		lb.current += n
		lb.stats.AllowedRequests++
		lb.stats.CurrentTokens = lb.current
		return true
	}
	
	lb.stats.BlockedRequests++
	return false
}

// Wait 等待空间可用
func (lb *LeakyBucketLimiter) Wait(ctx context.Context) error {
	return lb.WaitN(ctx, 1)
}

// WaitN 等待N个空间可用
func (lb *LeakyBucketLimiter) WaitN(ctx context.Context, n int) error {
	for {
		if lb.AllowN(n) {
			return nil
		}
		
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(time.Millisecond * 100):
			// 继续等待
		}
	}
}

// Reset 重置漏桶
func (lb *LeakyBucketLimiter) Reset() {
	lb.mu.Lock()
	defer lb.mu.Unlock()
	
	lb.current = 0
	lb.lastLeak = time.Now()
	lb.stats = RateLimitStats{
		LastReset: time.Now(),
		MaxTokens: lb.capacity,
	}
}

// GetStats 获取统计信息
func (lb *LeakyBucketLimiter) GetStats() RateLimitStats {
	lb.mu.Lock()
	defer lb.mu.Unlock()
	
	lb.stats.CurrentTokens = lb.current
	return lb.stats
}

// leak 漏水
func (lb *LeakyBucketLimiter) leak() {
	now := time.Now()
	elapsed := now.Sub(lb.lastLeak)
	
	leaked := int(elapsed.Seconds()) * lb.leakRate
	if leaked > 0 {
		lb.current = max(0, lb.current-leaked)
		lb.lastLeak = now
	}
}

// NewFixedWindowLimiter 创建固定窗口限流器
func NewFixedWindowLimiter(maxRequests int, windowSize time.Duration) *FixedWindowLimiter {
	return &FixedWindowLimiter{
		windowSize:  windowSize,
		maxRequests: maxRequests,
		current:     0,
		windowStart: time.Now(),
		stats: RateLimitStats{
			LastReset: time.Now(),
			MaxTokens: maxRequests,
		},
	}
}

// Allow 检查是否允许请求
func (fw *FixedWindowLimiter) Allow() bool {
	return fw.AllowN(1)
}

// AllowN 检查是否允许N个请求
func (fw *FixedWindowLimiter) AllowN(n int) bool {
	fw.mu.Lock()
	defer fw.mu.Unlock()
	
	fw.checkWindow()
	fw.stats.TotalRequests++
	
	if fw.current+n <= fw.maxRequests {
		fw.current += n
		fw.stats.AllowedRequests++
		fw.stats.CurrentTokens = fw.maxRequests - fw.current
		return true
	}
	
	fw.stats.BlockedRequests++
	return false
}

// Wait 等待窗口重置
func (fw *FixedWindowLimiter) Wait(ctx context.Context) error {
	return fw.WaitN(ctx, 1)
}

// WaitN 等待窗口重置以允许N个请求
func (fw *FixedWindowLimiter) WaitN(ctx context.Context, n int) error {
	for {
		if fw.AllowN(n) {
			return nil
		}
		
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(time.Millisecond * 100):
			// 继续等待
		}
	}
}

// Reset 重置窗口
func (fw *FixedWindowLimiter) Reset() {
	fw.mu.Lock()
	defer fw.mu.Unlock()
	
	fw.current = 0
	fw.windowStart = time.Now()
	fw.stats = RateLimitStats{
		LastReset: time.Now(),
		MaxTokens: fw.maxRequests,
	}
}

// GetStats 获取统计信息
func (fw *FixedWindowLimiter) GetStats() RateLimitStats {
	fw.mu.Lock()
	defer fw.mu.Unlock()
	
	fw.stats.CurrentTokens = fw.maxRequests - fw.current
	return fw.stats
}

// checkWindow 检查窗口是否需要重置
func (fw *FixedWindowLimiter) checkWindow() {
	now := time.Now()
	if now.Sub(fw.windowStart) >= fw.windowSize {
		fw.current = 0
		fw.windowStart = now
	}
}

// NewSlidingWindowLimiter 创建滑动窗口限流器
func NewSlidingWindowLimiter(maxRequests int, windowSize time.Duration) *SlidingWindowLimiter {
	return &SlidingWindowLimiter{
		windowSize:  windowSize,
		maxRequests: maxRequests,
		requests:    make([]time.Time, 0),
		stats: RateLimitStats{
			LastReset: time.Now(),
			MaxTokens: maxRequests,
		},
	}
}

// Allow 检查是否允许请求
func (sw *SlidingWindowLimiter) Allow() bool {
	return sw.AllowN(1)
}

// AllowN 检查是否允许N个请求
func (sw *SlidingWindowLimiter) AllowN(n int) bool {
	sw.mu.Lock()
	defer sw.mu.Unlock()
	
	now := time.Now()
	sw.cleanOldRequests(now)
	sw.stats.TotalRequests++
	
	if len(sw.requests)+n <= sw.maxRequests {
		for i := 0; i < n; i++ {
			sw.requests = append(sw.requests, now)
		}
		sw.stats.AllowedRequests++
		sw.stats.CurrentTokens = sw.maxRequests - len(sw.requests)
		return true
	}
	
	sw.stats.BlockedRequests++
	return false
}

// Wait 等待窗口空间
func (sw *SlidingWindowLimiter) Wait(ctx context.Context) error {
	return sw.WaitN(ctx, 1)
}

// WaitN 等待窗口空间以允许N个请求
func (sw *SlidingWindowLimiter) WaitN(ctx context.Context, n int) error {
	for {
		if sw.AllowN(n) {
			return nil
		}
		
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(time.Millisecond * 100):
			// 继续等待
		}
	}
}

// Reset 重置滑动窗口
func (sw *SlidingWindowLimiter) Reset() {
	sw.mu.Lock()
	defer sw.mu.Unlock()
	
	sw.requests = make([]time.Time, 0)
	sw.stats = RateLimitStats{
		LastReset: time.Now(),
		MaxTokens: sw.maxRequests,
	}
}

// GetStats 获取统计信息
func (sw *SlidingWindowLimiter) GetStats() RateLimitStats {
	sw.mu.Lock()
	defer sw.mu.Unlock()
	
	sw.stats.CurrentTokens = sw.maxRequests - len(sw.requests)
	return sw.stats
}

// cleanOldRequests 清理过期请求
func (sw *SlidingWindowLimiter) cleanOldRequests(now time.Time) {
	cutoff := now.Add(-sw.windowSize)
	validRequests := make([]time.Time, 0)
	
	for _, req := range sw.requests {
		if req.After(cutoff) {
			validRequests = append(validRequests, req)
		}
	}
	
	sw.requests = validRequests
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// GetClientIP 从请求中获取客户端IP
func GetClientIP(remoteAddr string) string {
	host, _, err := net.SplitHostPort(remoteAddr)
	if err != nil {
		return remoteAddr
	}
	return host
}