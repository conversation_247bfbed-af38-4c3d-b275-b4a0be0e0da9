// Package services 提供各种服务实现
package services

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/pprof"
	"runtime"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/internal/interfaces"
)

// DebugService 调试服务实现
type debugService struct {
	mu           sync.RWMutex
	config       *common.DebugConfig
	logger       logger.Logger
	running      bool
	server       *http.Server
	breakpoints  map[string]*Breakpoint
	watchers     map[string]*Watcher
	profiler     *Profiler
	ctx          context.Context
	cancel       context.CancelFunc
}

// Breakpoint 断点
type Breakpoint struct {
	ID        string                 `json:"id"`
	Location  string                 `json:"location"`
	Condition string                 `json:"condition"`
	Enabled   bool                   `json:"enabled"`
	HitCount  int64                  `json:"hit_count"`
	CreatedAt time.Time              `json:"created_at"`
	Callback  func(map[string]interface{}) `json:"-"`
}

// Watcher 监视器
type Watcher struct {
	ID          string                 `json:"id"`
	Expression  string                 `json:"expression"`
	Enabled     bool                   `json:"enabled"`
	LastValue   interface{}            `json:"last_value"`
	ChangeCount int64                  `json:"change_count"`
	CreatedAt   time.Time              `json:"created_at"`
	Callback    func(interface{}, interface{}) `json:"-"`
}

// Profiler 性能分析器
type Profiler struct {
	mu       sync.RWMutex
	enabled  bool
	profiles map[string]*ProfileData
}

// ProfileData 性能分析数据
type ProfileData struct {
	Name      string                 `json:"name"`
	StartTime time.Time              `json:"start_time"`
	EndTime   time.Time              `json:"end_time"`
	Duration  time.Duration          `json:"duration"`
	Data      map[string]interface{} `json:"data"`
}

// DebugInfo 调试信息
type DebugInfo struct {
	Timestamp    time.Time              `json:"timestamp"`
	Goroutines   int                    `json:"goroutines"`
	MemStats     runtime.MemStats       `json:"mem_stats"`
	GCStats      debug.GCStats          `json:"gc_stats"`
	BuildInfo    *debug.BuildInfo       `json:"build_info"`
	Breakpoints  []*Breakpoint          `json:"breakpoints"`
	Watchers     []*Watcher             `json:"watchers"`
	CustomData   map[string]interface{} `json:"custom_data"`
}

// NewDebugService 创建新的调试服务
func NewDebugService(config *common.DebugConfig, log logger.Logger) interfaces.DebugService {
	if log == nil {
		log = logger.GetLogger("debug")
	}

	if config == nil {
		config = &common.DebugConfig{
			Enabled:        false,
			VerboseLogging: false,
			DumpRequests:   false,
			DumpResponses:  false,
			ProfileEnabled: false,
			ProfilePort:    6060,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	ds := &debugService{
		config:      config,
		logger:      log,
		breakpoints: make(map[string]*Breakpoint),
		watchers:    make(map[string]*Watcher),
		profiler:    &Profiler{profiles: make(map[string]*ProfileData)},
		ctx:         ctx,
		cancel:      cancel,
	}

	ds.logger.Info("调试服务已初始化")
	return ds
}

// Start 启动调试服务
func (ds *debugService) Start() error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if ds.running {
		return fmt.Errorf("调试服务已在运行")
	}

	if !ds.config.Enabled {
		ds.logger.Info("调试服务已禁用")
		return nil
	}

	// 启动调试HTTP服务器
	if err := ds.startDebugServer(); err != nil {
		return fmt.Errorf("启动调试服务器失败: %v", err)
	}

	// 启动性能分析器
	ds.profiler.enabled = true

	ds.running = true
	ds.logger.Info(fmt.Sprintf("调试服务已启动，端口: %d", ds.config.ProfilePort))
	return nil
}

// Stop 停止调试服务
func (ds *debugService) Stop() error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if !ds.running {
		return nil
	}

	ds.cancel()

	// 停止HTTP服务器
	if ds.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := ds.server.Shutdown(ctx); err != nil {
			ds.logger.Error(fmt.Sprintf("停止调试服务器失败: %v", err))
		}
	}

	// 停止性能分析器
	ds.profiler.enabled = false

	ds.running = false
	ds.logger.Info("调试服务已停止")
	return nil
}

// IsEnabled 检查调试服务是否启用
func (ds *debugService) IsEnabled() bool {
	ds.mu.RLock()
	defer ds.mu.RUnlock()
	return ds.config.Enabled && ds.running
}

// SetBreakpoint 设置断点
func (ds *debugService) SetBreakpoint(id, location string, callback func()) error {
	if id == "" || location == "" {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeDebugBreakpointEmpty,
			errors.ErrDebugBreakpointEmpty.Message, "断点ID和位置不能为空")
	}

	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.breakpoints[id]; exists {
		return errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeDebugBreakpointExists,
			errors.ErrDebugBreakpointExists.Message, fmt.Sprintf("断点ID: %s", id))
	}

	// 包装回调函数
	wrappedCallback := func(context map[string]interface{}) {
		if callback != nil {
			callback()
		}
	}

	bp := &Breakpoint{
		ID:        id,
		Location:  location,
		Condition: "", // 默认无条件
		Enabled:   true,
		CreatedAt: time.Now(),
		Callback:  wrappedCallback,
	}

	ds.breakpoints[id] = bp
	ds.logger.Debug(fmt.Sprintf("设置断点: %s at %s", id, location))
	return nil
}

// RemoveBreakpoint 移除断点
func (ds *debugService) RemoveBreakpoint(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.breakpoints[id]; !exists {
		return fmt.Errorf("断点不存在: %s", id)
	}

	delete(ds.breakpoints, id)
	ds.logger.Debug(fmt.Sprintf("移除断点: %s", id))
	return nil
}

// EnableBreakpoint 启用断点
func (ds *debugService) EnableBreakpoint(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	bp, exists := ds.breakpoints[id]
	if !exists {
		return fmt.Errorf("断点不存在: %s", id)
	}

	bp.Enabled = true
	ds.logger.Debug(fmt.Sprintf("启用断点: %s", id))
	return nil
}

// DisableBreakpoint 禁用断点
func (ds *debugService) DisableBreakpoint(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	bp, exists := ds.breakpoints[id]
	if !exists {
		return fmt.Errorf("断点不存在: %s", id)
	}

	bp.Enabled = false
	ds.logger.Debug(fmt.Sprintf("禁用断点: %s", id))
	return nil
}

// HitBreakpoint 触发断点
func (ds *debugService) HitBreakpoint(id string, context map[string]interface{}) bool {
	ds.mu.RLock()
	bp, exists := ds.breakpoints[id]
	ds.mu.RUnlock()

	if !exists || !bp.Enabled {
		return false
	}

	// 检查条件
	if bp.Condition != "" && !ds.evaluateCondition(bp.Condition, context) {
		return false
	}

	ds.mu.Lock()
	bp.HitCount++
	ds.mu.Unlock()

	ds.logger.Debug(fmt.Sprintf("断点触发: %s (第%d次)", id, bp.HitCount))

	// 执行回调
	if bp.Callback != nil {
		go bp.Callback(context)
	}

	return true
}

// SetWatcher 设置监视器
func (ds *debugService) SetWatcher(id, expression string, callback func(interface{})) error {
	if id == "" || expression == "" {
		return fmt.Errorf("监视器ID和表达式不能为空")
	}

	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.watchers[id]; exists {
		return fmt.Errorf("监视器已存在: %s", id)
	}

	// 包装回调函数
	wrappedCallback := func(oldValue, newValue interface{}) {
		if callback != nil {
			callback(newValue)
		}
	}

	watcher := &Watcher{
		ID:         id,
		Expression: expression,
		Enabled:    true,
		CreatedAt:  time.Now(),
		Callback:   wrappedCallback,
	}

	ds.watchers[id] = watcher
	ds.logger.Debug(fmt.Sprintf("设置监视器: %s for %s", id, expression))
	return nil
}

// RemoveWatcher 移除监视器
func (ds *debugService) RemoveWatcher(id string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	if _, exists := ds.watchers[id]; !exists {
		return fmt.Errorf("监视器不存在: %s", id)
	}

	delete(ds.watchers, id)
	ds.logger.Debug(fmt.Sprintf("移除监视器: %s", id))
	return nil
}

// UpdateWatcher 更新监视器值
func (ds *debugService) UpdateWatcher(id string, value interface{}) error {
	ds.mu.RLock()
	watcher, exists := ds.watchers[id]
	ds.mu.RUnlock()

	if !exists {
		return fmt.Errorf("监视器不存在: %s", id)
	}

	if !watcher.Enabled {
		return nil
	}

	oldValue := watcher.LastValue

	ds.mu.Lock()
	watcher.LastValue = value
	watcher.ChangeCount++
	ds.mu.Unlock()

	// 如果值发生变化，执行回调
	if oldValue != value && watcher.Callback != nil {
		go watcher.Callback(oldValue, value)
	}

	return nil
}

// StartProfiling 开始性能分析
func (ds *debugService) StartProfiling(name string, duration time.Duration) error {
	if duration <= 0 {
		return fmt.Errorf("分析持续时间必须大于0")
	}

	if !ds.profiler.enabled {
		return fmt.Errorf("性能分析器未启用")
	}

	ds.profiler.mu.Lock()
	defer ds.profiler.mu.Unlock()

	if _, exists := ds.profiler.profiles[name]; exists {
		return fmt.Errorf("性能分析已存在: %s", name)
	}

	profile := &ProfileData{
		Name:      name,
		StartTime: time.Now(),
		Data:      make(map[string]interface{}),
	}

	ds.profiler.profiles[name] = profile
	ds.logger.Debug(fmt.Sprintf("开始性能分析: %s (持续时间: %v)", name, duration))
	return nil
}

// StopProfiling 停止性能分析
func (ds *debugService) StopProfiling(name string) ([]byte, error) {
	ds.profiler.mu.Lock()
	defer ds.profiler.mu.Unlock()

	profile, exists := ds.profiler.profiles[name]
	if !exists {
		return nil, fmt.Errorf("性能分析不存在: %s", name)
	}

	profile.EndTime = time.Now()
	profile.Duration = profile.EndTime.Sub(profile.StartTime)

	// 收集性能数据
	ds.collectProfileData(profile)

	delete(ds.profiler.profiles, name)
	ds.logger.Debug(fmt.Sprintf("停止性能分析: %s (耗时: %v)", name, profile.Duration))
	
	// 将profile数据序列化为JSON
	data, err := json.Marshal(profile)
	if err != nil {
		return nil, fmt.Errorf("序列化性能分析数据失败: %v", err)
	}
	
	return data, nil
}

// GetDebugInfo 获取调试信息
func (ds *debugService) GetDebugInfo() map[string]interface{} {
	ds.mu.RLock()
	defer ds.mu.RUnlock()

	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	var gcStats debug.GCStats
	debug.ReadGCStats(&gcStats)

	buildInfo, _ := debug.ReadBuildInfo()

	// 复制断点
	breakpoints := make([]*Breakpoint, 0, len(ds.breakpoints))
	for _, bp := range ds.breakpoints {
		bpCopy := *bp
		bpCopy.Callback = nil // 不序列化回调函数
		breakpoints = append(breakpoints, &bpCopy)
	}

	// 复制监视器
	watchers := make([]*Watcher, 0, len(ds.watchers))
	for _, w := range ds.watchers {
		wCopy := *w
		wCopy.Callback = nil // 不序列化回调函数
		watchers = append(watchers, &wCopy)
	}

	return map[string]interface{}{
		"timestamp":   time.Now(),
		"goroutines":  runtime.NumGoroutine(),
		"mem_stats":   memStats,
		"gc_stats":    gcStats,
		"build_info":  buildInfo,
		"breakpoints": breakpoints,
		"watchers":    watchers,
		"custom_data": make(map[string]interface{}),
	}
}

// DumpStack 获取堆栈信息
func (ds *debugService) DumpStack() string {
	buf := make([]byte, 1024*1024) // 1MB buffer
	n := runtime.Stack(buf, true)
	return string(buf[:n])
}

// ForceGC 强制垃圾回收
func (ds *debugService) ForceGC() error {
	runtime.GC()
	ds.logger.Info("强制垃圾回收完成")
	return nil
}

// SetLogLevel 设置日志级别
func (ds *debugService) SetLogLevel(level string) error {
	ds.mu.Lock()
	defer ds.mu.Unlock()

	// 注意：DebugConfig中没有LogLevel字段，这里可以通过其他方式处理
	ds.logger.Info(fmt.Sprintf("日志级别已设置为: %s", level))
	return nil
}

// startDebugServer 启动调试HTTP服务器
func (ds *debugService) startDebugServer() error {
	mux := http.NewServeMux()

	// 注册pprof处理器
	pprofPath := "/debug/pprof"
	mux.HandleFunc(pprofPath+"/", pprof.Index)
	mux.HandleFunc(pprofPath+"/cmdline", pprof.Cmdline)
	mux.HandleFunc(pprofPath+"/profile", pprof.Profile)
	mux.HandleFunc(pprofPath+"/symbol", pprof.Symbol)
	mux.HandleFunc(pprofPath+"/trace", pprof.Trace)

	// 注册自定义调试端点
	mux.HandleFunc("/debug/info", ds.handleDebugInfo)
	mux.HandleFunc("/debug/breakpoints", ds.handleBreakpoints)
	mux.HandleFunc("/debug/watchers", ds.handleWatchers)
	mux.HandleFunc("/debug/stack", ds.handleStack)
	mux.HandleFunc("/debug/gc", ds.handleGC)

	ds.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", ds.config.ProfilePort),
		Handler: mux,
	}

	go func() {
		if err := ds.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ds.logger.Error(fmt.Sprintf("调试服务器错误: %v", err))
		}
	}()

	return nil
}

// handleDebugInfo 处理调试信息请求
func (ds *debugService) handleDebugInfo(w http.ResponseWriter, r *http.Request) {
	info := ds.GetDebugInfo()
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(info)
}

// handleBreakpoints 处理断点请求
func (ds *debugService) handleBreakpoints(w http.ResponseWriter, r *http.Request) {
	ds.mu.RLock()
	breakpoints := make([]*Breakpoint, 0, len(ds.breakpoints))
	for _, bp := range ds.breakpoints {
		bpCopy := *bp
		bpCopy.Callback = nil
		breakpoints = append(breakpoints, &bpCopy)
	}
	ds.mu.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(breakpoints)
}

// handleWatchers 处理监视器请求
func (ds *debugService) handleWatchers(w http.ResponseWriter, r *http.Request) {
	ds.mu.RLock()
	watchers := make([]*Watcher, 0, len(ds.watchers))
	for _, w := range ds.watchers {
		wCopy := *w
		wCopy.Callback = nil
		watchers = append(watchers, &wCopy)
	}
	ds.mu.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(watchers)
}

// handleStack 处理堆栈请求
func (ds *debugService) handleStack(w http.ResponseWriter, r *http.Request) {
	stack := ds.DumpStack()
	w.Header().Set("Content-Type", "text/plain")
	w.Write([]byte(stack))
}

// handleGC 处理垃圾回收请求
func (ds *debugService) handleGC(w http.ResponseWriter, r *http.Request) {
	if r.Method == http.MethodPost {
		ds.ForceGC()
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("GC executed"))
	} else {
		w.WriteHeader(http.StatusMethodNotAllowed)
	}
}

// evaluateCondition 评估断点条件
func (ds *debugService) evaluateCondition(condition string, context map[string]interface{}) bool {
	// 简单的条件评估实现
	// 实际项目中可能需要更复杂的表达式解析器
	if condition == "" {
		return true
	}

	// 支持简单的等式检查
	parts := strings.Split(condition, "==")
	if len(parts) == 2 {
		key := strings.TrimSpace(parts[0])
		expectedValue := strings.TrimSpace(parts[1])
		
		if value, exists := context[key]; exists {
			return fmt.Sprintf("%v", value) == expectedValue
		}
	}

	return false
}

// collectProfileData 收集性能分析数据
func (ds *debugService) collectProfileData(profile *ProfileData) {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	profile.Data["goroutines"] = runtime.NumGoroutine()
	profile.Data["heap_alloc"] = memStats.HeapAlloc
	profile.Data["heap_sys"] = memStats.HeapSys
	profile.Data["heap_inuse"] = memStats.HeapInuse
	profile.Data["stack_inuse"] = memStats.StackInuse
	profile.Data["num_gc"] = memStats.NumGC
	profile.Data["gc_cpu_fraction"] = memStats.GCCPUFraction
}