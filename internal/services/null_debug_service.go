// Package services 提供各种服务实现
package services

import (
	"time"
	
	"github.com/mbndr/logo"
	"github.com/flexp/flexp/internal/interfaces"
)

// nullDebugService 空调试服务实现，用于禁用调试功能时
type nullDebugService struct {
	logger *logo.Logger
}

// NewNullDebugService 创建空调试服务实例
func NewNullDebugService(log *logo.Logger) interfaces.DebugService {
	if log == nil {
		log = logo.NewSimpleLogger(nil, logo.INFO, "null-debug", false)
	}
	
	return &nullDebugService{
		logger: log,
	}
}

// Start 启动调试服务（空实现）
func (nds *nullDebugService) Start() error {
	nds.logger.Info("空调试服务已启动（无实际功能）")
	return nil
}

// Stop 停止调试服务（空实现）
func (nds *nullDebugService) Stop() error {
	nds.logger.Info("空调试服务已停止")
	return nil
}

// IsEnabled 检查服务是否启用（空实现，总是返回false）
func (nds *nullDebugService) IsEnabled() bool {
	return false
}

// SetBreakpoint 设置断点（空实现）
func (nds *nullDebugService) SetBreakpoint(id string, condition string, action func()) error {
	return nil
}

// RemoveBreakpoint 移除断点（空实现）
func (nds *nullDebugService) RemoveBreakpoint(id string) error {
	return nil
}

// EnableBreakpoint 启用断点（空实现）
func (nds *nullDebugService) EnableBreakpoint(id string) error {
	return nil
}

// DisableBreakpoint 禁用断点（空实现）
func (nds *nullDebugService) DisableBreakpoint(id string) error {
	return nil
}

// HitBreakpoint 命中断点（空实现）
func (nds *nullDebugService) HitBreakpoint(id string, context map[string]interface{}) bool {
	return false
}

// SetWatcher 设置监视器（空实现）
func (nds *nullDebugService) SetWatcher(id string, expression string, callback func(interface{})) error {
	return nil
}

// RemoveWatcher 移除监视器（空实现）
func (nds *nullDebugService) RemoveWatcher(id string) error {
	return nil
}

// UpdateWatcher 更新监视器（空实现）
func (nds *nullDebugService) UpdateWatcher(id string, value interface{}) error {
	return nil
}

// StartProfiling 开始性能分析（空实现）
func (nds *nullDebugService) StartProfiling(profileType string, duration time.Duration) error {
	return nil
}

// StopProfiling 停止性能分析（空实现）
func (nds *nullDebugService) StopProfiling(profileType string) ([]byte, error) {
	return nil, nil
}

// GetDebugInfo 获取调试信息（空实现，返回空信息）
func (nds *nullDebugService) GetDebugInfo() map[string]interface{} {
	return map[string]interface{}{
		"enabled": false,
		"status":  "disabled",
	}
}

// DumpStack 转储堆栈（空实现）
func (nds *nullDebugService) DumpStack() string {
	return ""
}

// ForceGC 强制垃圾回收（空实现）
func (nds *nullDebugService) ForceGC() error {
	return nil
}

// SetLogLevel 设置日志级别（空实现）
func (nds *nullDebugService) SetLogLevel(level string) error {
	return nil
}
