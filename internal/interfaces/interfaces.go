package interfaces

import (
	"context"
	"crypto/tls"
	"net/http"
	"time"
	
	"github.com/fsnotify/fsnotify"
)

// ProxyStateResetter 代理状态重置接口
type ProxyStateResetter interface {
	ResetProxyState()
}

// ProxyManagerInterface 代理管理器接口
// 定义了代理管理器的核心功能，包括代理操作、质量管理、封禁管理等
type ProxyManagerInterface interface {
	// 基本代理操作
	GetProxy(mode string) (string, error)
	RotateProxy(domain ...string) (string, error)
	GetProxyForDomain(mode, domain string) (string, error)
	GetQualityProxy(tier, domain string) (string, error)
	
	// 代理质量管理
	UpdateProxyQuality(proxyURL string, success bool, responseTime time.Duration, domain string)
	AddToDomainPool(proxyURL, domain string)
	RemoveProxy(proxyURL string) error
	
	// 封禁管理
	BanIP(ip, durationStr, scope, resource string) error
	UnbanIP(ip string) error
	IsIPBanned(ip, resource, scope string) bool
	IsIPPermanentlyBlocked(ipOrDomain string) bool
	IsTrustedIP(ipOrDomain string) bool
	InitBanSystem(cfg interface{}) // 使用interface{}避免导入common包
	ResetBanSystem() // 重置封禁系统初始化状态
	IsBanSystemInitialized() bool // 检查封禁系统是否已初始化
	StartBanCleaner(ctx context.Context)
	
	// 代理池管理
	GetProxies() []string
	Count() int
	Reload() error
	GetAllProxies() []string
	
	// 生命周期管理
	Start(ctx context.Context) error
	Stop() error
	Watch() (*fsnotify.Watcher, error)
}

// SecurityService 安全服务接口
// 提供认证、授权、加密解密等安全功能
type SecurityService interface {
	// 认证相关
	Authenticate(r *http.Request) interface{} // 认证结果
	GenerateToken(userID string, scopes []string, duration time.Duration) (string, error)
	RevokeToken(token string) bool
	ValidateScope(token string, requiredScope string) bool
	
	// 加密解密
	Encrypt(plaintext string) (interface{}, error) // 加密结果
	Decrypt(ciphertext, nonce string) (string, error)
	
	// TLS配置
	GetTLSConfig() *tls.Config
	
	// 请求验证
	ValidateRequest(r *http.Request) error
	
	// 统计和管理
	GetAuthStats() map[string]interface{}
	CleanupExpiredTokens() int
}

// MonitoringService 监控服务接口
// 提供系统监控、指标收集、健康检查等功能
type MonitoringService interface {
	// 指标记录
	RecordMetric(name string, value interface{}, metricType interface{}, tags map[string]string)
	RecordMetricFloat(name string, value float64, tags map[string]string) error
	GetMetrics() map[string]interface{}
	
	// 健康检查
	RegisterHealthCheck(name string, check interface{})
	RegisterHealthCheckFunc(name string, check func() error) error
	GetHealthStatus() map[string]interface{}
	
	// 生命周期管理
	Start() error
	Stop() error
	
	// HTTP服务器
	StartHTTPServer(addr string) error
	StopHTTPServer() error
	
	// 系统指标
	GetSystemMetrics() map[string]interface{}
	GetProxyMetrics() map[string]interface{}
}

// PluginService 插件服务接口
// 提供插件加载、管理、钩子执行等功能
type PluginService interface {
	// 插件生命周期管理
	LoadPlugin(name, path string, config map[string]interface{}) error
	UnloadPlugin(name string) error
	EnablePlugin(name string) error
	DisablePlugin(name string) error
	ReloadPlugin(name string) error
	
	// 钩子管理
	ExecuteHook(hookType string, ctx interface{}) []interface{} // 使用interface{}避免导入common包
	RegisterHook(hookType, pluginName string, priority int) error
	UnregisterHook(hookType, pluginName string) error
	
	// 插件信息和统计
	GetPluginStats() interface{} // 使用interface{}避免导入common包
	ListPlugins() map[string]interface{} // 使用interface{}避免导入common包
	
	// 配置管理
	SavePluginConfig(configPath string) error
	LoadPluginConfig(configPath string) error
}

// CacheService 缓存服务接口
// 提供DNS缓存、正则表达式缓存和代理池缓存功能
type CacheService interface {
	// DNS缓存
	GetDNSCache(key string) ([]string, bool)
	SetDNSCache(key string, value []string, ttl int)

	// 正则表达式缓存
	GetRegexCache(pattern string) (interface{}, bool)
	SetRegexCache(pattern string, value interface{})

	// 代理池缓存
	UpdateProxyPool(proxies []string)
	StartCleanupRoutine()
	GetCacheStats() map[string]interface{}
	ClearAllCache()
}

// ConfigService 配置服务接口
// 提供配置文件加载、监控、回调等功能
type ConfigService interface {
	// 配置加载和获取
	LoadConfig(configFile string) (interface{}, error) // 使用interface{}避免导入common包
	GetConfig() interface{} // 使用interface{}避免导入common包
	
	// 配置监控
	WatchConfig() error
	
	// 配置变更回调
	AddConfigChangeCallback(callback func(interface{})) // 使用interface{}避免导入common包
	
	// 生命周期管理
	Close() error
}

// TracingService 追踪服务接口
// 提供分布式追踪、链路跟踪等功能
type TracingService interface {
	IsEnabled() bool
	Enable() error
	Disable() error
	StartSpan(operationName string, parentSpanID string) (string, error)
	FinishSpan(spanID string) error
	AddSpanTag(spanID string, key string, value interface{}) error
	AddSpanLog(spanID string, level string, message string, fields map[string]interface{}) error
	GetSpan(spanID string) (interface{}, error)
	GetTraceSpans(traceID string) ([]interface{}, error)
	InjectHeaders(spanID string, headers map[string]string) error
	ExtractHeaders(headers map[string]string) (string, error)
	GenerateTraceID() string
	GetStats() map[string]interface{}
}

// PerformanceService 性能调优服务接口
// 提供任务调度、批处理、性能优化等功能
type PerformanceService interface {
	Start() error
	Stop() error
	SubmitTask(task interface{}) error
	SubmitSimpleTask(fn func() error) error
	AddToBatch(item interface{}) error
	SetBatchProcessor(processor func([]interface{}) error) error
	GetMetrics() map[string]interface{}
	GetWorkerPoolStats() map[string]interface{}
	GetBatchQueueStats() map[string]interface{}
	OptimizeGC() error
	SetGCPercent(percent int) error
}

// DebugService 调试服务接口
// 提供断点调试、监控、性能分析等功能
type DebugService interface {
	Start() error
	Stop() error
	IsEnabled() bool
	SetBreakpoint(id string, condition string, action func()) error
	RemoveBreakpoint(id string) error
	EnableBreakpoint(id string) error
	DisableBreakpoint(id string) error
	HitBreakpoint(id string, context map[string]interface{}) bool
	SetWatcher(id string, expression string, callback func(interface{})) error
	RemoveWatcher(id string) error
	UpdateWatcher(id string, value interface{}) error
	StartProfiling(profileType string, duration time.Duration) error
	StopProfiling(profileType string) ([]byte, error)
	GetDebugInfo() map[string]interface{}
	DumpStack() string
	ForceGC() error
	SetLogLevel(level string) error
}

// ProfilingService 性能分析服务接口
// 提供CPU、内存、协程等性能分析功能
type ProfilingService interface {
	Start() error
	Stop() error
	IsEnabled() bool
	StartCPUProfile(filename string) error
	StopCPUProfile() error
	WriteHeapProfile(filename string) error
	WriteGoroutineProfile(filename string) error
	WriteBlockProfile(filename string) error
	WriteMutexProfile(filename string) error
	GetProfileData(profileType string) ([]byte, error)
	SetSamplingRate(rate int) error
	GetSamplingRate() int
	SetMemProfileRate(rate int)
	GetMemProfileRate() int
	SetBlockProfileRate(rate int)
	GetBlockProfileRate() int
	SetMutexProfileFraction(rate int)
	GetMutexProfileFraction() int
	GetRuntimeStats() map[string]interface{}
	EnableProfiling(profileType string) error
	DisableProfiling(profileType string) error
}

// LogService 日志服务接口
type LogService interface {
	// 基本日志方法
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
	Debug(msg string, args ...interface{})
	Fatal(msg string, args ...interface{})
	
	// 扩展方法
	WithTraceID(traceID string) LogService
	WithFields(fields map[string]interface{}) LogService
	LogError(err error, msg string, args ...interface{})
	GetLogger() interface{}
}

// DNSService DNS解析服务接口
type DNSService interface {
	Resolve(hostname string) ([]string, error)
	ReverseResolve(ip string) (string, error)
	SetMode(mode string) error
	SetCustomServers(servers []interface{}) error
	SetCacheTTL(ttl int)
	SetNoCache(noCache bool)
	SetTimeout(timeout time.Duration)
}

// Container 依赖注入容器接口
type Container interface {
	// 获取各种服务
	GetConfigService() ConfigService
	GetLogService() LogService
	GetCacheService() CacheService
	GetDNSService() DNSService
	GetProxyService() ProxyService
	GetTriggerService() TriggerService
	GetActionService() ActionService
	GetMonitoringService() MonitoringService
	GetRateLimitingService() RateLimitingService
	GetSecurityService() SecurityService
	GetPluginService() PluginService
	GetStrategyManager() interface{} // 避免导入strategy包
	
	// 新增的高级服务
	GetTracingService() TracingService
	GetPerformanceService() PerformanceService
	GetDebugService() DebugService
	GetProfilingService() ProfilingService
	GetAdvancedConfigService() AdvancedConfigService
	
	// 生命周期管理
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

// ProxyService 代理管理服务接口
type ProxyService interface {
	GetNextProxy() (string, error)
	MarkProxyFailed(proxy string)
	MarkProxySuccess(proxy string)
	GetProxyCount() int
	InitBanSystem(config interface{}) // 使用interface{}避免导入common包
	StartBanCleaner(ctx context.Context)
	BanIP(ip string, duration int) error
	BanDomain(domain string, duration int) error
	IsIPBanned(ip string) bool
	IsDomainBanned(domain string) bool
	UnbanIP(ip string) error
	UnbanDomain(domain string) error
	UpdateProxyList(proxies []string)
	GetBanStats() map[string]interface{}
	GetProxyStats() map[string]interface{}
	ResetFailedProxies()
	Stop()
}

// TriggerService 触发器服务接口
type TriggerService interface {
	// 基本触发器方法
	EvaluateTriggers(req *http.Request, resp *http.Response, data map[string]interface{}) ([]string, error)
	RegisterTrigger(name string, trigger interface{}) error
	GetTrigger(name string) (interface{}, bool)
	
	// 生命周期管理
	Initialize(config interface{}) error // 使用interface{}避免导入common包
	Start() error
	Stop()
	
	// 触发器处理
	ProcessTriggers(req interface{}) ([]string, error)
	
	// 触发器管理
	AddTrigger(name string, config interface{}) error // 使用interface{}避免导入common包
	EnableTrigger(name string) error
	DisableTrigger(name string) error
	GetAllTriggers() map[string]interface{}
}

// ActionService 动作执行服务接口
type ActionService interface {
	ExecuteAction(actionType string, params map[string]interface{}) error
	ExecuteActionByName(actionName string, params map[string]interface{}) error
	RegisterAction(name string, action interface{}) error
	GetAction(name string) (interface{}, bool)
	Start() error
	Stop() error
}

// RateLimitingService 限流服务接口
type RateLimitingService interface {
	Start() error
	Stop() error
	IsAllowed(key string, limit int, window time.Duration) bool
	GetStats() map[string]interface{}
	ResetLimiter(key string) error
	SetGlobalLimit(limit int, window time.Duration) error
	GetRemainingRequests(key string) int
	GetLimiterType() string
	SetLimiterType(limiterType string) error
}

// AdvancedConfigService 高级配置管理服务接口
type AdvancedConfigService interface {
	Start() error
	Stop() error
	GetConfig() map[string]interface{}
	UpdateConfig(config map[string]interface{}) error
	GetConfigValue(key string) (interface{}, error)
	SetConfigValue(key string, value interface{}) error
	AddWatcher(id string, pattern string, callback func(string, interface{}, interface{})) error
	RemoveWatcher(id string) error
	AddValidator(key string, validator func(interface{}) error) error
	GetHistory(limit int) []interface{}
	GetStats() map[string]interface{}
	ResetConfig() error
	ExportConfig(format string) ([]byte, error)
	ImportConfig(data []byte, format string) error
}