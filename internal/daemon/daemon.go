package daemon

import (
	"runtime"

	"github.com/kardianos/service"
	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/common/logger"
)

var daemonLogger = logger.GetDaemonLogger()

// New 在守护进程中初始化flexp
func New(opt *common.Options) error {
	// 复制用户提供的参数
	args := []string{
		"-f", opt.File,
		"-a", opt.Address,
		"-A", opt.Auth,
		"-t", opt.Timeout.String(),
		"-o", opt.Output,
	}

	if opt.Sync {
		args = append(args, "-s")
	}

	if opt.Verbose {
		args = append(args, "-v")
	}

	if opt.Watch {
		args = append(args, "-w")
	}

	o := make(service.KeyValue)
	o["Restart"] = "on-success"
	o["SuccessExitStatus"] = "1 2 8 SIGKILL"

	cfg.Arguments = args
	cfg.Option = o

	p := &program{opt: opt}
	s, err := service.New(p, &cfg)
	if err != nil {
		return err
	}

	// 停止并卸载当前的flexp服务，然后重新安装并启动
	_ = service.Control(s, "stop")
	_ = service.Control(s, "uninstall")

	if runtime.GOOS == "windows" {
		err = service.Control(s, "install")
		if err != nil {
			return err
		}

		daemonLogger.Info("服务已安装!")
		daemonLogger.Info("输入 'net start flexp' 以守护进程模式启动。")
	} else {
		_ = service.Control(s, "install")

		daemonLogger.Info("正在以守护进程模式运行...")
		return service.Control(s, "start")
	}

	return nil
}
