package server

import (
	"path/filepath"
	"regexp"
	"strings"
)

// ParseExcludedPatternsString 解析排除规则字符串，通过竖线(|)分割多个排除项
func ParseExcludedPatternsString(excludedPatternsStr string) []string {
	if excludedPatternsStr == "" {
		return nil
	}

	// 通过竖线分割字符串
	patterns := strings.Split(excludedPatternsStr, "|")

	// 清理每个模式
	result := make([]string, 0, len(patterns))
	for _, pattern := range patterns {
		pattern = strings.TrimSpace(pattern)
		if pattern != "" {
			result = append(result, pattern)
		}
	}

	return result
}

// isExcluded 检查URL是否应该被排除
// excluded_patterns: 排除规则列表
// scope: 排除范围，可以是 "global"(全局), "domain"(域名), "extension"(扩展名)
// isExcluded 检查URL是否被排除
func isExcluded(url string, patterns []string, scope string) bool {
	if len(patterns) == 0 {
		return false
	}

	for _, pattern := range patterns {
		pattern = strings.TrimSpace(pattern)
		if pattern == "" {
			continue
		}

		switch strings.ToLower(scope) {
		case "extension":
			if isExtensionExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [扩展名匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			}
		case "domain":
			if isDomainExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [域名匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			}
		case "url":
			if isURLExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [URL匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			}
		case "all":
			if isURLExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [URL匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			} else if isDomainExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [域名匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			} else if isExtensionExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [扩展名匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			}
		default:
			// 默认尝试所有匹配
			if isURLExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [URL匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			} else if isDomainExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [域名匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			} else if isExtensionExcluded(url, pattern) {
				serverLogger.GetRawLogger().Infof("排除规则命中 [扩展名匹配]: URL=%s, 规则=%s, 范围=%s", url, pattern, scope)
				return true
			}
		}
	}

	return false
}

// extractDomain 从URL中提取域名
func extractDomain(url string) string {
	// 移除协议部分
	domainStart := strings.Index(url, "://")
	if domainStart != -1 {
		url = url[domainStart+3:]
	}

	// 提取域名部分（到第一个/或?或#为止）
	domainEnd := strings.IndexAny(url, "/?#")
	if domainEnd != -1 {
		return url[:domainEnd]
	}

	return url
}

// isDomainExcluded 检查URL的域名是否匹配排除模式
func isDomainExcluded(urlStr, pattern string) bool {
	// 从URL中提取域名
	parts := strings.Split(urlStr, "/")
	if len(parts) < 3 {
		return false
	}

	domain := parts[2]

	// 精确匹配
	if domain == pattern {
		serverLogger.GetRawLogger().Debugf("域名精确匹配: 域名=%s, 规则=%s", domain, pattern)
		return true
	}

	// 子域名匹配（以.开头的模式）
	if strings.HasPrefix(pattern, ".") && (domain == pattern[1:] || strings.HasSuffix(domain, pattern)) {
		serverLogger.GetRawLogger().Debugf("域名后缀匹配: 域名=%s, 规则=%s", domain, pattern)
		return true
	}

	// 尝试正则表达式匹配
	regex, err := regexp.Compile(pattern)
	if err == nil && regex.MatchString(domain) {
		serverLogger.GetRawLogger().Debugf("域名正则匹配: 域名=%s, 规则=%s", domain, pattern)
		return true
	}

	return false
}

// isExtensionExcluded 检查URL的文件扩展名是否匹配排除模式
func isExtensionExcluded(urlStr, pattern string) bool {
	// 从URL中提取文件扩展名
	ext := filepath.Ext(urlStr)
	if ext == "" {
		return false
	}

	// 移除开头的点
	ext = strings.TrimPrefix(ext, ".")

	// 如果模式以.开头，移除它以便比较
	pattern = strings.TrimPrefix(pattern, ".")

	// 不区分大小写比较
	if strings.EqualFold(ext, pattern) {
		serverLogger.GetRawLogger().Debugf("扩展名匹配: URL=%s, 扩展名=%s, 规则=%s", urlStr, ext, pattern)
		return true
	}

	// 兼容pattern不带点时的匹配
	if strings.EqualFold("."+ext, pattern) {
		serverLogger.GetRawLogger().Debugf("扩展名匹配(带点): URL=%s, 扩展名=%s, 规则=%s", urlStr, ext, pattern)
		return true
	}

	return false
}

// isURLExcluded 检查URL是否匹配排除模式
func isURLExcluded(urlStr, pattern string) bool {
	// 精确匹配
	if urlStr == pattern {
		serverLogger.GetRawLogger().Debugf("URL精确匹配: URL=%s, 规则=%s", urlStr, pattern)
		return true
	}

	// 前缀匹配
	if strings.HasPrefix(urlStr, pattern) {
		serverLogger.GetRawLogger().Debugf("URL前缀匹配: URL=%s, 规则=%s", urlStr, pattern)
		return true
	}

	// 尝试正则表达式匹配
	regex, err := regexp.Compile(pattern)
	if err == nil && regex.MatchString(urlStr) {
		serverLogger.GetRawLogger().Debugf("URL正则匹配: URL=%s, 规则=%s", urlStr, pattern)
		return true
	}

	return false
}
