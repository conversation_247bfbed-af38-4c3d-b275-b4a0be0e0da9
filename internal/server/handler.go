package server

import (
	"bytes"
	"context"
	"encoding/base64"
	stderrors "errors" // 标准库errors包
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"path/filepath"

	// "strconv" // Removed unused import
	"strings"
	"time"

	"github.com/elazarl/goproxy"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/flexp/flexp/common" // 添加action包导入
	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/internal/proxygateway"
	"github.com/flexp/flexp/internal/trigger"

	// "github.com/flexp/flexp/pkg/config" // Old config
	"github.com/flexp/flexp/pkg/flexproxy"
	"github.com/flexp/flexp/pkg/helper/awsurl"
)

// 模块级别的日志器

// getGatewayKey 根据baseURL和region生成网关映射的唯一键
func getGatewayKey(baseURL, region string) string {
	return baseURL + "|" + region
}

// onRequest 处理客户端请求
func (p *Proxy) onRequest(req *http.Request, ctx *goproxy.ProxyCtx) (*http.Request, *http.Response) {
	// 使用直接的日志函数，无需获取logger实例
	
	// 打印进入 onRequest 时 RuleConfig 和 Global.Enable 的状态
	if p.Options.RuleConfig != nil {
		serverLogger.Debug(fmt.Sprintf("[onRequest入口] p.Options.RuleConfig.Global.Enable 的值为: %v", p.Options.RuleConfig.Global.Enable))
	} else {
		serverLogger.Debug("[onRequest入口] p.Options.RuleConfig 为 nil")
	}

	// 检查全局配置是否启用
	if p.Options.RuleConfig == nil || !p.Options.RuleConfig.Global.Enable {
		serverLogger.Debug("全局规则未启用，跳过规则检查")
	} else {
		serverLogger.Debug("全局规则已启用，检查请求是否符合规则")
	}

	// 创建请求上下文，用于传递信息
	reqCtx := req.Context()

	// 设置DNS缓存参数到上下文
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		// 使用全局标志控制配置日志只输出一次
		globalConfigMutex.Lock()
		shouldLogConfig := !globalConfigLogged
		if !globalConfigLogged {
			globalConfigLogged = true
		}
		globalConfigMutex.Unlock()
		
		// 设置DNS缓存TTL
		if p.Options.RuleConfig.Global.DNSCacheTTL > 0 {
			// 检查上下文中是否已经设置了DNS缓存TTL，避免重复设置
			if reqCtx.Value("dns_cache_ttl") == nil {
				cacheTTL := time.Duration(p.Options.RuleConfig.Global.DNSCacheTTL) * time.Second
				reqCtx = context.WithValue(reqCtx, "dns_cache_ttl", cacheTTL)
				if shouldLogConfig {
					serverLogger.GetRawLogger().Debugf("设置全局DNS缓存TTL到上下文: %s", cacheTTL)
				}
			}
		}

		// 设置是否禁用DNS缓存
		if reqCtx.Value("dns_cache_enabled") == nil {
			reqCtx = context.WithValue(reqCtx, "dns_cache_enabled", !p.Options.RuleConfig.Global.DNSNoCache)
			if shouldLogConfig {
				serverLogger.GetRawLogger().Debugf("设置全局DNS缓存启用状态到上下文: %v", !p.Options.RuleConfig.Global.DNSNoCache)
			}
		}

		// 设置IP版本优先级
		if reqCtx.Value("ip_version_priority") == nil {
			ipVersionPriority := p.Options.RuleConfig.Global.IPVersionPriority
			if ipVersionPriority == "" {
				ipVersionPriority = "ipv4" // 默认使用IPv4优先
			}
			reqCtx = context.WithValue(reqCtx, "ip_version_priority", ipVersionPriority)
			if shouldLogConfig {
				serverLogger.GetRawLogger().Debugf("设置全局IP版本优先级到上下文: %s", ipVersionPriority)
			}
		}
	}

	// 使用更新后的上下文
	req = req.WithContext(reqCtx)

	// 1. 最高优先级：全局配置检查
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		urlString := req.URL.String()
		serverLogger.GetRawLogger().Debugf("检查排除规则: URL=%s, 规则数量=%d, 范围=%s", urlString, len(p.Options.RuleConfig.Global.ExcludedPatterns), p.Options.RuleConfig.Global.ExcludedScope)
		if isExcluded(urlString, p.Options.RuleConfig.Global.ExcludedPatterns, p.Options.RuleConfig.Global.ExcludedScope) {
			serverLogger.GetRawLogger().Infof("%s URL被排除规则阻止: %s", req.RemoteAddr, urlString)
			// 创建空响应
			resp := &http.Response{
				StatusCode: http.StatusOK,
				Status:     "200 OK",
				Body:       io.NopCloser(bytes.NewBufferString("")),
				Header:     make(http.Header),
				Request:    req,
			}
			// 删除 hop-by-hop headers
			for _, h := range flexproxy.HopHeaders {
				resp.Header.Del(h)
			}
			// 直接返回响应，不再进入后续流程
			resp.Header.Set("X-FlexProxy-Excluded", "1")
			return req, resp
		}
	}

	// 添加请求开始时间到上下文
	reqCtx = context.WithValue(req.Context(), "startTime", time.Now())

	// 执行请求前处理阶段触发器
	// if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
	// 	// 执行请求头触发器
	// 	resp := p.executeTriggers(trigger.PreRequest, req, nil, 0)
	// 	if resp != nil {
	// 		return req, resp
	// 	}
	// }

	req = req.WithContext(reqCtx)
	ctx.Req = req // 修复：确保 ctx.Req 也更新为带 startTime 的 context
	if p.Options.Sync {
		mutex.Lock()
		defer mutex.Unlock()
	}

	if (req.URL.Scheme != "http") && (req.URL.Scheme != "https") {
		return req, serverErr(req)
	}

	// 新增：请求前检测域名/IP是否被永久封禁
	// if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
	// 	domain := req.URL.Hostname()
	// 	if p.Options.ProxyManager.IsIPPermanentlyBlocked(domain) {
	// 		log.Warnf("域名 %s 被永久封禁，直接丢弃请求", domain)
	// 		return req, serverErr(req) // 立即返回，后续不再处理
	// 	}
	// 	ip := net.ParseIP(domain)
	// 	if ip != nil && p.Options.ProxyManager.IsIPPermanentlyBlocked(ip.String()) {
	// 		log.Warnf("IP %s 被永久封禁，直接丢弃请求", ip.String())
	// 		return req, serverErr(req) // 立即返回，后续不再处理
	// 	}
	// }

	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		domain := req.URL.Hostname()
		if p.Options.ProxyManager.IsIPPermanentlyBlocked(domain) {
			serverLogger.GetRawLogger().Warnf("域名 %s 被永久封禁，直接丢弃请求", domain)
			resp := &http.Response{
				StatusCode: http.StatusForbidden,
				Status:     "403 Forbidden",
				Body:       io.NopCloser(bytes.NewBufferString("Blocked by FlexProxy")),
				Header:     make(http.Header),
				Request:    req,
			}
			resp.Header.Set("X-FlexProxy-Blocked", "1")
			return req, resp
		}
		ip := net.ParseIP(domain)
		if p.Options.ProxyManager.IsIPPermanentlyBlocked(ip.String()) {
			serverLogger.GetRawLogger().Warnf("IP %s 被永久封禁，直接丢弃请求", ip)
			resp := &http.Response{
				StatusCode: http.StatusForbidden,
				Status:     "403 Forbidden",
				Body:       io.NopCloser(bytes.NewBufferString("Blocked by FlexProxy")),
				Header:     make(http.Header),
				Request:    req,
			}
			resp.Header.Set("X-FlexProxy-Blocked", "1")
			return req, resp
		}

		// // 新增临时封禁检查
		// proxyURL, err := url.Parse(p.currentProxy)
		// if err == nil && proxyURL != nil {
		// 	proxyIP := proxyURL.Hostname() // 从代理URL中提取主机名（IP地址）

		// 	// 检查代理IP是否被临时封禁（全局）
		// 	banned, expire := p.Options.ProxyManager.IsIPTemporarilyBanned(proxyIP, "")

		// 	// 检查代理IP是否被临时封禁（针对特定域名）
		// 	if !banned {
		// 		domain := req.URL.Hostname()
		// 		banned, expire = p.Options.ProxyManager.IsIPTemporarilyBanned(proxyIP, domain)
		// 	}

		// 	if banned {
		// 		log.Warnf("代理 %s 被临时封禁，解封时间: %v，直接丢弃请求", proxyIP, expire)
		// 		resp := &http.Response{
		// 			StatusCode: http.StatusForbidden,
		// 			Status:     "403 Forbidden",
		// 			Body:       io.NopCloser(bytes.NewBufferString("Temporarily banned by FlexProxy")),
		// 			Header:     make(http.Header),
		// 			Request:    req,
		// 		}
		// 		resp.Header.Set("X-FlexProxy-TempBanned", "1")
		// 		//resp.Header.Set("X-FlexProxy-BanExpire", expire.Format(time.RFC3339))
		// 		return req, resp
		// 	}
		// }

	}

	resChan := make(chan interface{})

	// 在goroutine外部预先创建DNS解析器
	// 获取请求的实际域名，用于DNS解析
	requestHost := req.URL.Hostname()

	// 检查是否是IP地址，如果是则跳过DNS解析
	isIP := false
	if net.ParseIP(requestHost) != nil {
		serverLogger.GetRawLogger().Debugf("主机名 %s 已经是IP地址，跳过DNS解析", requestHost)
		isIP = true
	}

	// 创建DNS解析器并预先解析域名
	if !isIP && p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		// 确定DNS解析模式和配置
		dnsMode := "local" // 默认值
		if p.Options.RuleConfig.Global.DNSLookupMode != "" {
			dnsMode = p.Options.RuleConfig.Global.DNSLookupMode
		}
		customDNSServers := p.Options.RuleConfig.Global.CustomDNSServers // 更改了变量名和字段
		httpProxyDNS := p.Options.RuleConfig.Global.HTTPProxyDNS

		// 创建DNS上下文
		dnsCtx := req.Context()
		if p.Options.RuleConfig.Global.DNSCacheTTL > 0 {
			cacheTTL := time.Duration(p.Options.RuleConfig.Global.DNSCacheTTL) * time.Second
			dnsCtx = context.WithValue(dnsCtx, "dns_cache_ttl", cacheTTL)
		}
		// 设置DNS超时时间
		if p.Options.RuleConfig.Global.DefaultDNSTimeout > 0 {
			dnsTimeout := time.Duration(p.Options.RuleConfig.Global.DefaultDNSTimeout) * time.Millisecond
			dnsCtx = context.WithValue(dnsCtx, "dns_timeout", dnsTimeout)
		}
		dnsCtx = context.WithValue(dnsCtx, "dns_cache_enabled", !p.Options.RuleConfig.Global.DNSNoCache)

		// 检查DNS缓存中是否已有结果
		dnsCacheLock.RLock()
		cached, exists := dnsCache[requestHost]
		dnsCacheLock.RUnlock()

		// 如果缓存中没有结果或已过期，则创建解析器并解析
		if !exists || time.Now().After(cached.ExpireAt) {
			// 根据配置创建DNS解析器
			var dnsResolver DNSResolver
			if dnsMode == "remote" {
				// 获取代理用于远程DNS解析
				proxy, err := p.rotateProxy()
				if err == nil && proxy != "" {
					dnsResolver = NewRemoteDNSResolver(proxy, httpProxyDNS)
				}
			} else if len(customDNSServers) > 0 { // 检查切片是否有元素
				// Use the first custom DNS server's address
				firstCustomDNSServer := customDNSServers[0]
				customDNSResolver := NewCustomDNSResolver(firstCustomDNSServer.Server) // 假设NewCustomDNSResolver接受服务器地址字符串

				// 设置DNS缓存参数
				customDNSResolver.NoCache = p.Options.RuleConfig.Global.DNSNoCache
				if p.Options.RuleConfig.Global.DNSCacheTTL > 0 {
					customDNSResolver.CacheTTL = time.Duration(p.Options.RuleConfig.Global.DNSCacheTTL) * time.Second
				}

				dnsResolver = customDNSResolver
			} else {
				dnsResolver = &LocalDNSResolver{}
			}

			// 如果创建了DNS解析器，预先解析域名并存储结果
			if dnsResolver != nil {
				serverLogger.GetRawLogger().Debugf("预先解析域名 %s 并存入缓存", requestHost)
				ips, err := dnsResolver.LookupIP(dnsCtx, requestHost)
				if err == nil && len(ips) > 0 {
					// 将DNS解析结果存储到上下文中
					req = req.WithContext(context.WithValue(req.Context(), "dns_result_"+requestHost, ips))
					// 将DNS解析器存储到上下文中
					req = req.WithContext(context.WithValue(req.Context(), "dns_resolver", dnsResolver))
					serverLogger.GetRawLogger().Debugf("成功预解析域名 %s，找到 %d 个IP地址", requestHost, len(ips))
				} else if err != nil {
					serverLogger.GetRawLogger().Warnf("预解析域名 %s 失败: %v", requestHost, err)
				}
			}
		} else {
			serverLogger.GetRawLogger().Debugf("域名 %s 已在DNS缓存中，跳过预解析", requestHost)
			// 从缓存中获取结果并存入上下文
			req = req.WithContext(context.WithValue(req.Context(), "dns_result_"+requestHost, cached.IPs))
			// 确保DNS解析器也存储到上下文中
			if p.dnsResolver != nil {
				req = req.WithContext(context.WithValue(req.Context(), "dns_resolver", p.dnsResolver))
			}
		}
	}

	go func(r *http.Request) {
		serverLogger.GetRawLogger().Debugf("%s %s %s", r.RemoteAddr, r.Method, r.URL)

		// 执行请求前处理阶段触发器
		//if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		if p.triggerManager != nil && p.triggerManager.HasPreRequestTriggers {
			resp := p.executeTriggers(trigger.PreRequest, r, nil, 0)
			if resp != nil {
				resChan <- resp
				return
			}
		}

		// 初始化已尝试的代理计数器
		var triedCount int = 0

		for {
			serverLogger.GetRawLogger().Infof("当前代理池数量: %d", p.Options.ProxyManager.Count())
			domain := r.URL.Hostname()
			url := r.URL.String()

			// 检查上下文中是否已有代理信息
			proxyFromCtx := r.Context().Value("current_proxy")
			var proxy string
			var rotateErr error

			if proxyFromCtx != nil {
				// 使用上下文中的代理
				proxy = proxyFromCtx.(string)
				serverLogger.GetRawLogger().Debugf("从上下文获取代理: %s", proxy)
			} else {
				// 获取新代理并存储到上下文
				proxy, rotateErr = p.rotateProxy(domain, url)
				if rotateErr != nil || proxy == "" {
					resChan <- errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理池无可用代理")
					return
				}
				// 将代理信息存储到上下文中
				r = r.WithContext(context.WithValue(r.Context(), "current_proxy", proxy))
				serverLogger.GetRawLogger().Debugf("将代理 %s 存储到请求上下文", proxy)
			}
			// reqWithProxy := r.WithContext(context.WithValue(r.Context(), "current_proxy", proxy))

			// if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
			// 	// 全局封禁
			// 	if p.Options.ProxyManager.IsIPBanned(proxy, "", "") {
			// 		log.Debugf("代理 %s 被全局封禁，跳过", proxy)
			// 		continue
			// 	}
			// 	// 域名级封禁
			// 	if p.Options.ProxyManager.IsIPBanned(proxy, r.URL.Hostname(), "domain") {
			// 		log.Debugf("禁止 %s 域名使用代理 %s ", r.URL.Hostname(), proxy)
			// 		continue
			// 	}
			// 	// URL级封禁（如有需要）
			// 	if p.Options.ProxyManager.IsIPBanned(proxy, r.URL.String(), "url") {
			// 		log.Debugf("禁止 URL %s 使用代理 %s ", r.URL.String(), proxy)
			// 		continue
			// 	}

			// }

			//// 新增：检查代理IP是否被封禁
			//if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
			//	if p.Options.ProxyManager.IsIPBanned(proxy, r.URL.Hostname(), "domain") {
			//		// 选择新的代理
			//		continue
			//	}
			//}

			// // 检查是否应该排除此请求
			// if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
			// 	urlString := r.URL.String()
			// 	if isExcluded(urlString, p.Options.RuleConfig.Global.ExcludedPatterns, p.Options.RuleConfig.Global.ExcludedScope) {
			// 		log.Debugf("%s URL匹配排除规则: %s", r.RemoteAddr, urlString)
			// 		// // 标记 context
			// 		// newCtx := context.WithValue(r.Context(), "excluded", true)
			// 		// r = r.WithContext(newCtx)
			// 		resChan <- &http.Response{
			// 			StatusCode: http.StatusOK,
			// 			Status:     "200 OK",
			// 			Body:       io.NopCloser(bytes.NewBufferString("")),
			// 			Header:     make(http.Header),
			// 			//Request:    r,
			// 		}
			// 		return
			// 	}
			// }

			// 检查是否还有可用代理，避免死循环
			availableProxies := p.Options.ProxyManager.Count()
			if availableProxies <= 0 {
				serverLogger.GetRawLogger().Warnf("代理池中没有可用代理，放弃请求")
				resChan <- errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理池中没有可用代理")
				return
			}

			// 检查URL级别的IP封禁
			if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
				if p.Options.ProxyManager.IsIPBanned(proxy, r.URL.String(), "url") {
					serverLogger.GetRawLogger().Debugf("禁止 URL %s 使用代理 %s ", r.URL.String(), proxy)
					// 记录已尝试的代理数量，避免死循环
					triedCount++
					if triedCount >= availableProxies {
						serverLogger.GetRawLogger().Warnf("所有代理都被URL %s 封禁，放弃请求", r.URL.String())
						resChan <- errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, "所有代理都被URL封禁")
						return
					}
					continue
				}
			}

			// 检查域名级别的IP封禁
			if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
				domain := r.URL.Hostname()
				if p.Options.ProxyManager.IsIPBanned(proxy, domain, "domain") {
					serverLogger.GetRawLogger().Debugf("禁止 %s 域名使用代理 %s ", domain, proxy)
					// 记录已尝试的代理数量，避免死循环
					triedCount++
					if triedCount >= availableProxies {
						serverLogger.GetRawLogger().Warnf("所有代理都被域名 %s 封禁，放弃请求", domain)
						resChan <- errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, "所有代理都被域名封禁")
						return
					}
					continue
				}
			}

			// //检查域名级别的IP封禁
			// if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
			// 	domain := r.URL.Hostname()
			// 	if p.Options.ProxyManager.IsIPBanned(proxy, domain) {
			// 		// 检查全局永久封禁
			// 		if banInfo, ok := p.Options.ProxyManager.GlobalBannedIPs[proxy]; ok && banInfo != nil && banInfo.IsPermanent {
			// 			log.Warnf("代理 %s 被永久封禁，直接丢弃请求", proxy)
			// 			resChan <- errors.New("proxy permanently banned, drop request")
			// 			return
			// 		}
			// 		// 检查域名级别永久封禁
			// 		if domainBanMap, ok := p.Options.ProxyManager.DomainBannedIPs[domain]; ok {
			// 			if banInfo, ok := domainBanMap[proxy]; ok && banInfo != nil && banInfo.IsPermanent {
			// 				log.Warnf("代理 %s 在域 %s 下被永久封禁，直接丢弃请求", proxy, domain)
			// 				resChan <- errors.New("proxy domain permanently banned, drop request")
			// 				return
			// 			}
			// 			// 也要检查整个域名本身被永久封禁的情况
			// 			if banInfo, ok := domainBanMap[domain]; ok && banInfo != nil && banInfo.IsPermanent {
			// 				log.Warnf("域名 %s 被永久封禁，直接丢弃请求", domain)
			// 				resChan <- errors.New("domain permanently banned, drop request")
			// 				return
			// 			}
			// 		}
			// 		log.Debugf("跳过 %s 域禁止使用的代理 %s", proxy, domain)
			// 		continue
			// 	}
			// }

			retryablehttpClient, err := p.getClient(r, domain)
			if err != nil {
				resChan <- err

				return
			}

			retryablehttpRequest, err := retryablehttp.FromRequest(r)
			if err != nil {
				resChan <- err

				return
			}

			resp, err := retryablehttpClient.Do(retryablehttpRequest)
			if err != nil {
				resChan <- err
				return
			}
			defer resp.Body.Close()

			buf, err := io.ReadAll(resp.Body)
			if err != nil {
				resChan <- err

				return
			}
			resp.Body = io.NopCloser(bytes.NewBuffer(buf))

			resChan <- resp

		}
	}(req)

	var resp *http.Response

	res := <-resChan
	switch res := res.(type) {
	case *http.Response:
		// // 检查 context 标记
		// if resp.Request != nil && resp.Request.Context().Value("excluded") == true {
		// 	return req, resp
		// }
		resp = res
		serverLogger.Debug(req.RemoteAddr + " " + resp.Status)

	case error:
		err := res
		serverLogger.GetRawLogger().Errorf("%s %s", req.RemoteAddr, err)
		//resp = serverErr(req)
		// 新增：代理池耗尽时返回503
		resp = &http.Response{
			StatusCode: http.StatusServiceUnavailable,
			Status:     "503 Service Unavailable",
			Body:       io.NopCloser(bytes.NewBufferString("Proxy pool is empty or there are no available proxies, please retry later")),
			Header:     make(http.Header),
			Request:    req,
		}
		resp.Header.Set("X-FlexProxy-ProxyPool", "exhausted")
	}

	// // 新增：响应头处理阶段
	// if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable && resp != nil {
	// 	// 计算请求时间
	// 	requestTime := time.Since(req.Context().Value("startTime").(time.Time))

	// 	// 执行响应头处理阶段触发器
	// 	newResp := p.executeTriggers(trigger.PostHeader, req, resp, requestTime)
	// 	if newResp != nil {
	// 		resp = newResp
	// 	}
	// }

	return req, resp
}

// onConnect 处理CONNECT方法
func (p *Proxy) onConnect(host string, ctx *goproxy.ProxyCtx) (*goproxy.ConnectAction, string) {
	if p.Options.Auth != "" {
		auth := ctx.Req.Header.Get("Proxy-Authorization")
		if auth != "" {
			creds := strings.SplitN(auth, " ", 2)
			if len(creds) != 2 {
				return goproxy.RejectConnect, host
			}

			auth, err := base64.StdEncoding.DecodeString(creds[1])
			if err != nil {
				serverLogger.GetRawLogger().Warnf("%s: 解码代理认证失败", ctx.Req.RemoteAddr)
				return goproxy.RejectConnect, host
			}

			if string(auth) != p.Options.Auth {
				serverLogger.GetRawLogger().Errorf("%s: 代理认证无效", ctx.Req.RemoteAddr)
				return goproxy.RejectConnect, host
			}
		} else {
			serverLogger.GetRawLogger().Warnf("%s: %s 代理请求认证失败", ctx.Req.RemoteAddr, host)
			return goproxy.RejectConnect, host
		}
	}

	return goproxy.MitmConnect, host
}

// onResponse handles backend responses, and removing hop-by-hop headers
func (p *Proxy) onResponse(resp *http.Response, ctx *goproxy.ProxyCtx) *http.Response {
	// // 记录请求信息
	// if ctx.Req != nil {
	// 	log.Debugf("处理响应: %s %s, 状态码: %d", ctx.Req.Method, ctx.Req.URL, resp.StatusCode)

	// 	// 记录DNS解析信息
	// 	host := ctx.Req.URL.Hostname()
	// 	startTime := time.Now()
	// 	if ips, err := net.LookupIP(host); err == nil {
	// 		log.Debugf("DNS解析 %s 成功，IP: %v，耗时: %v", host, ips, time.Since(startTime))
	// 	} else {
	// 		log.Warnf("DNS解析 %s 失败: %v", host, err)
	// 	}

	// 	// 记录自定义DNS服务器使用情况
	// 	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.CustomDNSServer != "" {
	// 		log.Debugf("尝试使用自定义DNS服务器: %s", p.Options.RuleConfig.Global.CustomDNSServer)
	// 	}
	// }

	// 处理特殊响应头
	if resp != nil && resp.Header.Get("X-FlexProxy-Excluded") == "1" {
		serverLogger.GetRawLogger().Debugf("处理被排除的请求响应")
		for _, h := range flexproxy.HopHeaders {
			resp.Header.Del(h)
		}
		resp.Header.Del("X-FlexProxy-Excluded")
		return resp
	}
	if resp != nil && resp.Header.Get("X-FlexProxy-Blocked") == "1" {
		serverLogger.GetRawLogger().Debugf("处理被阻止的请求响应")
		for _, h := range flexproxy.HopHeaders {
			resp.Header.Del(h)
		}
		resp.Header.Del("X-FlexProxy-Blocked")
		return resp
	}
	if resp != nil && resp.Header.Get("X-FlexProxy-TempBanned") == "1" {
		serverLogger.GetRawLogger().Debugf("处理临时封禁的请求响应")
		for _, h := range flexproxy.HopHeaders {
			resp.Header.Del(h)
		}
		resp.Header.Del("X-FlexProxy-TempBanned")
		return resp
	}
	if resp != nil && resp.Header.Get("X-FlexProxy-ProxyPool") == "exhausted" {
		serverLogger.GetRawLogger().Debugf("处理代理池耗尽的请求响应")
		for _, h := range flexproxy.HopHeaders {
			resp.Header.Del(h)
		}
		resp.Header.Del("X-FlexProxy-ProxyPool")
		return resp
	}

	// 删除hop-by-hop headers
	for _, h := range flexproxy.HopHeaders {
		resp.Header.Del(h)
	}

	// 添加响应体处理阶段触发器执行
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable && resp != nil {
		// 计算请求时间
		// 健壮性处理，防止 context 没有 startTime 时 panic
		startTimeVal := ctx.Req.Context().Value("startTime")
		var requestTime time.Duration
		if t, ok := startTimeVal.(time.Time); ok {
			requestTime = time.Since(t)
			serverLogger.GetRawLogger().Debugf("请求总耗时: %v", requestTime)
		} else {
			requestTime = 0
			serverLogger.Warn("请求上下文缺少 startTime，设置 requestTime=0")
		}

		// 执行post_header阶段触发器
		if p.triggerManager != nil && p.triggerManager.HasPostHeaderTriggers {
			serverLogger.GetRawLogger().Debugf("开始执行post_header阶段触发器，URL: %s", ctx.Req.URL)
			triggerStartTime := time.Now()
			newResp := p.executeTriggers(trigger.PostHeader, ctx.Req, resp, requestTime)
			serverLogger.GetRawLogger().Debugf("post_header阶段触发器执行完成，耗时: %v", time.Since(triggerStartTime))
			if newResp != nil {
				serverLogger.GetRawLogger().Infof("post_header阶段触发器生成了新的响应，状态码: %d", newResp.StatusCode)
				return newResp
			}
		} else {
			serverLogger.Debug("没有post_header阶段触发器或触发器管理器为空")
		}

		// 新增：触发器后判断代理池数量
		minPool := 1
		if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
			minPool = p.Options.RuleConfig.Global.MinProxyPoolSize
		}
		if p.Options.ProxyManager.Count() < minPool {
			serverLogger.GetRawLogger().Warnf("代理池数量(%d)已小于代理池最小可用数量(%d)，中断后续阶段触发器", p.Options.ProxyManager.Count(), minPool)
			return resp
		}

		// 执行post_body阶段触发器
		if p.triggerManager != nil && p.triggerManager.HasPostBodyTriggers {
			serverLogger.GetRawLogger().Debugf("开始执行post_body阶段触发器，URL: %s", ctx.Req.URL)
			triggerStartTime := time.Now()
			newResp := p.executeTriggers(trigger.PostBody, ctx.Req, resp, requestTime)
			serverLogger.GetRawLogger().Debugf("post_body阶段触发器执行完成，耗时: %v", time.Since(triggerStartTime))
			if newResp != nil {
				serverLogger.GetRawLogger().Infof("post_body阶段触发器生成了新的响应，状态码: %d", newResp.StatusCode)
				return newResp
			}
		} else {
			serverLogger.Debug("没有post_body阶段触发器或触发器管理器为空")
		}

	}

	return resp
}

// func (p *Proxy) rotateProxy() (string, error) {
// 	p.mu.Lock()
// 	defer p.mu.Unlock()
// 	if currentProxy == "" {
// 		var proxy string
// 		var err error
//
// 		// 读取全局IP轮询方式，默认sequent
// 		ipRotationMode := "sequent"
// 		if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.IPRotationMode != "" {
// 			ipRotationMode = p.Options.RuleConfig.Global.IPRotationMode
// 		}
//
// 		// 新增：读取最大尝试次数配置
// 		maxAttempts := 10
// 		if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MaxProxyFetchAttempts > 0 {
// 			maxAttempts = p.Options.RuleConfig.Global.MaxProxyFetchAttempts
// 		}
//
// 		// 新增：代理池最小可用数量判断
// 		minPool := 1
// 		if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
// 			minPool = p.Options.RuleConfig.Global.MinProxyPoolSize
// 		}
// 		if p.Options.ProxyManager.Count() < minPool {
// 			serverLogger.Warn("代理池可用数量不足，暂停所有请求")
// 			return "", errors.New("proxy pool exhausted")
// 		}
// 		found := false
// 		// 尝试获取未被封禁的代理
// 		for i := 0; i < maxAttempts; i++ { // 最多尝试10次
// 			if ipRotationMode == "random" {
// 				proxy, err = p.Options.ProxyManager.RandomProxy()
// 			} else {
// 				proxy, err = p.Options.ProxyManager.NextProxy()
// 			}
// 			if err != nil {
// 				log.Fatalf("无法旋转代理 IP：%s", err)
// 				continue
// 			}
//
// 			// 如果启用了规则系统，检查代理IP是否被封禁
// 			if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
// 				// 这里使用空字符串作为域名，只检查全局封禁
// 				if p.Options.ProxyManager.IsIPBanned(proxy, "") {
// 					serverLogger.GetRawLogger().Debugf("跳过禁止的代理：%s", proxy)
// 					continue
// 				}
// 			}
// 			found = true
// 			// 找到未被封禁的代理
// 			break
// 		}
// 		if !found {
// 			log.Warn("多次尝试获取未被封禁代理失败，暂停请求")
// 			return "", errors.New("proxy pool exhausted")
// 		}
//
// 		currentProxy = proxy
// 		ok = 1
// 	} else {
// 		ok++
// 	}
//
// 	return currentProxy, nil
// }

func (p *Proxy) rotateProxy(domain ...string) (string, error) {
	serverLogger.Debug("开始执行rotateProxy函数")

	// 获取锁
	serverLogger.Debug("尝试获取互斥锁")
	p.mu.Lock()
	defer func() {
		serverLogger.Debug("释放互斥锁")
		p.mu.Unlock()
	}()
	serverLogger.Debug("已获取互斥锁，开始处理代理选择")

	// 读取全局IP轮询方式，默认sequential
	ipRotationMode := "sequential"
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.IPRotationMode != "" {
		ipRotationMode = p.Options.RuleConfig.Global.IPRotationMode
	}
	serverLogger.GetRawLogger().Debugf("IP轮询模式: %s", ipRotationMode)

	// 新增：读取最大尝试次数配置
	maxAttempts := 10
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Options.RuleConfig.Global.MaxProxyFetchAttempts
	}
	serverLogger.GetRawLogger().Debugf("最大尝试次数: %d", maxAttempts)

	// 新增：代理池最小可用数量判断
	minPool := 1
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
		minPool = p.Options.RuleConfig.Global.MinProxyPoolSize
	}
	serverLogger.GetRawLogger().Debugf("代理池最小可用数量: %d, 当前代理池数量: %d", minPool, p.Options.ProxyManager.Count())

	if p.Options.ProxyManager.Count() < minPool {
		serverLogger.Warn("代理池可用数量不足，暂停所有请求")
		return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeNoProxyAvailable, "代理池无可用代理")
	}

	// 如果当前没有代理，获取新代理
	if p.currentProxy == "" {
		serverLogger.Debug("当前没有代理，需要获取新代理")

		// 使用新的GetProxy方法获取代理，该方法内部已实现缓存机制
		proxy, err := p.Options.ProxyManager.GetProxy(ipRotationMode)
		if err != nil {
			serverLogger.GetRawLogger().Warnf("无法获取代理 IP：%s", err)
			return "", err
		}
		serverLogger.GetRawLogger().Debugf("获取到代理: %s", proxy)

		// 如果启用了规则系统，检查代理IP是否被封禁
		if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
			serverLogger.Debug("规则系统已启用，检查代理是否被封禁")
			// 这里使用空字符串作为域名，只检查全局封禁
			isBanned := p.Options.ProxyManager.IsIPBanned(proxy, "", "")
			serverLogger.GetRawLogger().Debugf("代理 %s 是否被封禁: %v", proxy, isBanned)

			if isBanned {
				serverLogger.GetRawLogger().Debugf("跳过禁止的代理：%s", proxy)
				return "", errors.NewError(errors.ErrTypeProxy, errors.ErrCodeProxyBanned, "获取到的代理被封禁")
			}
		}

		serverLogger.GetRawLogger().Infof("找到可用代理: %s", proxy)
		p.currentProxy = proxy
		p.proxyOkCount = 1
	} else {
		serverLogger.GetRawLogger().Debugf("继续使用当前代理: %s", p.currentProxy)
		p.proxyOkCount++
		serverLogger.GetRawLogger().Debugf("当前代理使用次数增加到: %d", p.proxyOkCount)
	}

	serverLogger.GetRawLogger().Debugf("rotateProxy函数执行完成，返回代理: %s", p.currentProxy)
	return p.currentProxy, nil
}

func (p *Proxy) removeProxy(target string) {
	err := p.Options.ProxyManager.RemoveProxy(target)
	if err != nil {
		serverLogger.Error(err.Error())
	}
}

func (p *Proxy) getClient(req *http.Request, proxyAddr string) (*retryablehttp.Client, error) {
	// 从请求上下文中获取代理信息
	proxyFromCtx := req.Context().Value("current_proxy")
	//var proxyAddr string
	proxyAddr = proxyFromCtx.(string)

	// 获取请求的实际域名，用于DNS解析
	requestHost := ""
	if req != nil && req.URL != nil {
		requestHost = req.URL.Hostname()
		serverLogger.GetRawLogger().Debugf("获取请求的实际域名用于DNS解析: %s", requestHost)
	}

	// 从上下文中获取DNS解析器（如果已经创建）
	dnsResolverFromCtx := req.Context().Value("dns_resolver")
	var dnsResolver DNSResolver
	if dnsResolverFromCtx != nil {
		dnsResolver = dnsResolverFromCtx.(DNSResolver)
		serverLogger.Debug("从上下文中获取DNS解析器")
	}

	// 获取DNS模式
	dnsMode := "local" // 默认值
	httpProxyDNS := ""
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		if p.Options.RuleConfig.Global.DNSLookupMode != "" {
			dnsMode = p.Options.RuleConfig.Global.DNSLookupMode
		}
		httpProxyDNS = p.Options.RuleConfig.Global.HTTPProxyDNS
	}

	// 使用DNS解析器创建Transport
	tr, err := flexproxy.Transport(proxyAddr, dnsMode, p.reverseDNSResolver, httpProxyDNS)
	if err != nil && !stderrors.Is(err, flexproxy.ErrSwitchTransportAWSProtocolScheme) {
		return nil, err
	}

	// 如果使用本地模式和自定义DNS服务器，覆盖Transport的DialContext
	if dnsMode == "local" && dnsResolver != nil {
		// 设置HTTP代理
		if proxyAddr != "" && strings.HasPrefix(proxyAddr, "http") {
			proxyURL, err := url.Parse(proxyAddr)
			if err == nil {
				tr.Proxy = http.ProxyURL(proxyURL)
				serverLogger.GetRawLogger().Debugf("设置HTTP代理: %s", proxyAddr)
			}
		}

		// 保存原始的DialContext函数，用于直接连接
		originalDialContext := (&net.Dialer{}).DialContext

		tr.DialContext = func(ctx context.Context, network, address string) (net.Conn, error) {
			// 解析主机名和端口
			host, port, err := net.SplitHostPort(address)
			if err != nil {
				return nil, err
			}

			// 获取请求的实际域名
			var requestHost string
			if req != nil && req.URL != nil {
				requestHost = req.URL.Hostname()
			}

			// 从上下文中获取当前代理信息
			proxyFromCtx := ctx.Value("current_proxy")
			if proxyFromCtx != nil {
				proxyStr := proxyFromCtx.(string)
				// 解析代理URL获取主机名
				proxyURL, err := url.Parse(proxyStr)
				if err == nil {
					proxyHost := proxyURL.Hostname()
					// 如果当前连接的是代理服务器本身，直接使用原始拨号器连接
					if host == proxyHost {
						serverLogger.GetRawLogger().Debugf("检测到直接连接到代理服务器 %s，跳过DNS解析", host)
						return originalDialContext(ctx, network, address)
					}
				}
			}

			// 检查是否是IP地址，如果是则跳过DNS解析
			if net.ParseIP(host) != nil {
				serverLogger.GetRawLogger().Debugf("主机名 %s 已经是IP地址，跳过DNS解析", host)
				return originalDialContext(ctx, network, address)
			}

			// 如果host不是请求的实际域名，可能是代理地址，直接连接
			if requestHost != "" && host != requestHost {
				serverLogger.GetRawLogger().Debugf("检测到地址 %s 不是请求的目标域名 %s，可能是代理地址，直接连接", host, requestHost)
				return originalDialContext(ctx, network, address)
			}

			// 确定要解析的主机名
			resolveHost := requestHost
			if resolveHost == "" {
				resolveHost = host
			}

			// 从上下文中获取DNS解析结果缓存
			dnsResultFromCtx := ctx.Value("dns_result_" + resolveHost)
			var ips []net.IP

			if dnsResultFromCtx != nil {
				// 使用缓存的DNS解析结果
				ips = dnsResultFromCtx.([]net.IP)
				serverLogger.GetRawLogger().Debugf("使用上下文中缓存的DNS解析结果: %s -> %v", resolveHost, ips)
			} else {
				// 使用自定义DNS服务器解析主机名
				var err error
				serverLogger.GetRawLogger().Debugf("使用DNS解析器解析主机名: %s", resolveHost)
				ips, err = dnsResolver.LookupIP(ctx, resolveHost)
				if err != nil {
					serverLogger.GetRawLogger().Warnf("使用自定义DNS服务器解析失败: %v，尝试使用系统默认解析器", err)
					// 尝试使用系统默认解析器作为备选
					ips, err = net.DefaultResolver.LookupIP(ctx, "ip", resolveHost)
					if err != nil {
						return nil, errors.WrapErrorWithDetails(err, errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "无法解析主机名", fmt.Sprintf("主机名: %s", resolveHost))
					}
				}

				if len(ips) > 0 {
					// 将DNS解析结果存储到上下文中
					if req != nil {
						req = req.WithContext(context.WithValue(req.Context(), "dns_result_"+resolveHost, ips))
					}
				}
			}

			if len(ips) == 0 {
				return nil, errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSNoResult, "无法解析主机名: 未找到IP地址", fmt.Sprintf("主机名: %s", resolveHost))
			}
			// 使用解析得到的第一个IP地址连接
			dialer := &net.Dialer{}
			return dialer.DialContext(ctx, network, net.JoinHostPort(ips[0].String(), port))
		}
	}

	proxy := &flexproxy.Proxy{
		Address:      proxyAddr,
		MaxRedirects: constants.DefaultMaxRedirects,
		Timeout:      p.Options.Timeout,
		Transport:    tr,
	}

	client, err := proxy.New(req)
	if err != nil {
		return nil, err
	}

	if awsurl.IsURL(proxyAddr) {
		var pg *proxygateway.ProxyGateway

		awsURL, err := awsurl.Parse(proxyAddr)
		if err != nil {
			return nil, err
		}

		_, err = awsURL.Credentials("")
		if err != nil {
			return nil, err
		}

		accessKeyID := awsURL.AccessKeyID
		secretAccessKey := awsURL.SecretAccessKey
		region := awsURL.Region

		baseURL, _, err := proxygateway.GetBaseURL(req.URL.String())
		if err != nil {
			return nil, err
		}

		gatewayKey := getGatewayKey(baseURL, region)

		if p.Gateways[gatewayKey] == nil {
			ctx := context.Background()
			gateway, err := proxygateway.New(ctx, accessKeyID, secretAccessKey, region)
			if err != nil {
				return nil, err
			}

			err = gateway.SetBaseURL(baseURL)
			if err != nil {
				return nil, err
			}

			err = gateway.Start(ctx)
			if err != nil {
				return nil, err
			}

			pg = gateway

			p.mu.Lock()
			p.Gateways[gatewayKey] = pg
			p.mu.Unlock()
		} else {
			pg = p.Gateways[gatewayKey]
		}

		// 将请求URL重写为API Gateway端点URL
		gatewayEndpoint := pg.GetEndpoint()
		req.URL.Path = filepath.Join("/", proxygateway.StageName, req.URL.Path)
		req.URL.Host = gatewayEndpoint.Host
		req.URL.Scheme = gatewayEndpoint.Scheme
		req.Host = gatewayEndpoint.Host
	}

	if p.Options.Verbose {
		client.Transport = dump.RoundTripper(tr)
	}

	retryablehttpClient := flexproxy.ToRetryableHTTPClient(client)
	retryablehttpClient.RetryMax = 0
	retryablehttpClient.RetryWaitMin = client.Timeout
	retryablehttpClient.RetryWaitMax = client.Timeout
	retryablehttpClient.Logger = ReleveledLogo{
		Logger:  logger.GetServerLogger().GetRawLogger(),
		Request: req,
		Verbose: p.Options.Verbose,
	}

	return retryablehttpClient, nil
}

// nonProxy handles non-proxy requests
func nonProxy(w http.ResponseWriter, req *http.Request) {
	if common.Version != "" {
		w.Header().Add("X-Flexp-Version", common.Version)
	}

	if req.URL.Path == "/cert" {
		w.Header().Add("Content-Type", "application/octet-stream")
		w.Header().Add("Content-Disposition", fmt.Sprint("attachment; filename=", "goproxy-cacert.der"))
		w.WriteHeader(http.StatusOK)

		if _, err := w.Write(goproxy.GoproxyCa.Certificate[0]); err != nil {
			http.Error(w, "获取代理证书授权失败。", 500)
			serverLogger.GetRawLogger().Errorf("%s %s %s %s", req.RemoteAddr, req.Method, req.URL, err.Error())
		}

		return
	}

	http.Error(w, "This is a FlexProxy proxy server. It does not respond to non-proxy requests.", 500)
}

func serverErr(req *http.Request) *http.Response {
	return goproxy.NewResponse(req, mime, http.StatusBadGateway, "Proxy server error")
}

// 添加createNullResponse函数
func createNullResponse(req *http.Request) *http.Response {
	return &http.Response{
		StatusCode: http.StatusOK,
		Status:     "200 OK",
		Body:       http.NoBody,
		Request:    req,
		Header:     make(http.Header),
	}
}

func (p *Proxy) executeTriggers(stage trigger.ProcessStage, req *http.Request, resp *http.Response, reqTime time.Duration) *http.Response {
	if p.triggerManager == nil {
		serverLogger.Warn("触发器管理器为空，跳过触发器执行")
		return nil
	}

	serverLogger.GetRawLogger().Debugf("执行%s阶段触发器，请求URL: %s，请求时间: %dms", stage, req.URL.String(), reqTime.Milliseconds())

	var finalResp *http.Response
	triggers := p.triggerManager.GetTriggersForStage(stage)
	serverLogger.GetRawLogger().Debugf("找到%d个%s阶段触发器", len(triggers), stage)

	// 保存原始DNS解析器，以便后续恢复
	originalDNSResolver := p.dnsResolver
	defer func() {
		// 恢复原始DNS解析器
		p.dnsResolver = originalDNSResolver
	}()

	// 创建一个新的上下文，用于存储DNS解析结果
	// 检查全局配置中的DNSNoCache参数
	dnsNoCache := false
	var cacheTTL time.Duration = constants.DefaultDNSCacheTTL // 默认值为5分钟
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
		dnsNoCache = p.Options.RuleConfig.Global.DNSNoCache
		// 使用配置文件中的DNSCacheTTL值
		if p.Options.RuleConfig.Global.DNSCacheTTL > 0 {
			cacheTTL = time.Duration(p.Options.RuleConfig.Global.DNSCacheTTL) * time.Second
		}
	}

	// 保留原始上下文中的代理信息
	proxyFromCtx := req.Context().Value("current_proxy")

	dnsCtx := context.WithValue(req.Context(), "dns_cache_enabled", !dnsNoCache)
	// 设置DNS缓存TTL为配置文件中的值
	dnsCtx = context.WithValue(dnsCtx, "dns_cache_ttl", cacheTTL)

	// 如果原始上下文中有代理信息，保留到新上下文
	if proxyFromCtx != nil {
		dnsCtx = context.WithValue(dnsCtx, "current_proxy", proxyFromCtx)
	}

	// 将上下文附加到请求
	reqWithDNSCtx := req.WithContext(dnsCtx)

	// 如果请求中包含主机名，且未禁用DNS缓存，则预先解析并存入缓存
	if req.URL != nil && req.URL.Hostname() != "" {
		hostname := req.URL.Hostname()

		// 当DNSNoCache=yes时，完全跳过预解析
		if dnsNoCache {
			serverLogger.GetRawLogger().Debugf("DNS缓存已禁用，跳过预解析主机名: %s", hostname)
		} else {
			// 检查是否已经有缓存的DNS结果
			dnsCacheLock.RLock()
			cached, exists := dnsCache[hostname]
			dnsCacheLock.RUnlock()

			// 只有当没有缓存结果或缓存已过期时，才进行预解析
			if (!exists || time.Now().After(cached.ExpireAt)) && p.dnsResolver != nil {
				serverLogger.GetRawLogger().Debugf("预先解析主机名: %s 并存入缓存", hostname)
				// 使用当前DNS解析器解析主机名
				ips, err := p.dnsResolver.LookupIP(dnsCtx, hostname)
				if err != nil {
					serverLogger.GetRawLogger().Warnf("预解析主机名失败: %s, 错误: %v", hostname, err)
				} else if len(ips) > 0 {
					serverLogger.GetRawLogger().Debugf("成功预解析主机名: %s, 找到 %d 个IP地址", hostname, len(ips))
				}
			} else if exists {
				serverLogger.GetRawLogger().Debugf("主机名 %s 已在缓存中，跳过预解析", hostname)
			}
		}
	}

	for _, t := range triggers {
		// 添加错误恢复机制
		func() {
			defer func() {
				if r := recover(); r != nil {
					serverLogger.GetRawLogger().Errorf("触发器执行崩溃: %v", r)
				}
			}()

			// 获取事件配置并设置事件级别DNS解析器
			eventConfig := p.triggerManager.GetEventConfig(t)
			if eventConfig == nil {
				serverLogger.GetRawLogger().Warnf("无法获取触发器 %T 的事件配置，跳过此触发器的动作执行", t)
				return // 从匿名函数返回，相当于 continue 外层循环的下一次迭代
			}
			// 从这里开始，eventConfig 保证不是 nil
			serverLogger.GetRawLogger().Debugf("处理触发器类型: %s, 处理阶段: %s, 事件名称: %s", eventConfig.TriggerType, stage, eventConfig.Name)

			// 如果是状态码触发器，记录更详细的信息
			if eventConfig.TriggerType == "status" && resp != nil { // 与字符串比较
				serverLogger.GetRawLogger().Debugf("状态码触发器检查 - 当前状态码: %d", resp.StatusCode)
			}

			// 如果事件配置了特定的DNS查询模式，则临时切换DNS解析器
			if eventConfig.DNSLookupMode != "" {
				// 根据事件配置创建临时DNS解析器
				tempDNSResolver := p.createDNSResolverForEvent(eventConfig, reqWithDNSCtx)
				if tempDNSResolver != nil {
					p.dnsResolver = tempDNSResolver
					serverLogger.GetRawLogger().Debugf("为事件临时切换DNS解析器模式: %s", eventConfig.DNSLookupMode)
				}
			}

			serverLogger.GetRawLogger().Debugf("尝试匹配触发器，类型: %T", t)
			if t.Match(reqWithDNSCtx, resp, reqTime) {
				serverLogger.GetRawLogger().Infof("触发器匹配成功，阶段: %s，优先级: %d", stage, t.GetPriority())

				// 获取触发器类型用于日志记录
				triggerType := ""
				if eventConfig != nil {
					triggerType = eventConfig.TriggerType
				}

				serverLogger.GetRawLogger().Infof("触发器匹配成功 - 类型: %s", triggerType)

				// 执行触发器动作
				actionConfigs := t.GetActions() // This is now []common.ActionConfig
				serverLogger.GetRawLogger().Debugf("触发器有 %d 个动作配置需要执行", len(actionConfigs))

				for _, actionCfg := range actionConfigs {
					serverLogger.GetRawLogger().Debugf("准备执行动作类型: %s, 参数: %v", actionCfg.Type, actionCfg.Params)
					actCtx := context.WithValue(dnsCtx, "current_proxy", p.currentProxy)
					actCtx = context.WithValue(actCtx, "proxy_resetter", p)

					var actionResp *http.Response
					var execErr error
					stopProcessing := false // 初始化stopProcessing

					if p.actionManager == nil {
						serverLogger.GetRawLogger().Errorf("ActionManager is nil in Proxy.executeTriggers for event %s", eventConfig.Name)
						execErr = errors.NewError(errors.ErrTypeAction, errors.ErrCodeActionManagerNotInitialized, "ActionManager not initialized")
					} else {
						actionInstance, buildErr := p.actionManager.BuildActionFromConfig(actionCfg)

						// 新增日志: 检查 BuildActionFromConfig 的返回值
						if actionInstance == nil {
							serverLogger.GetRawLogger().Errorf("executeTriggers: actionInstance is nil AFTER BuildActionFromConfig for type %s. buildErr: %v", actionCfg.Type, buildErr)
						} else {
							serverLogger.GetRawLogger().Debugf("executeTriggers: actionInstance is NOT nil AFTER BuildActionFromConfig for type %s. Type: %T. buildErr: %v", actionCfg.Type, actionInstance, buildErr)
						}

						if buildErr != nil {
					serverLogger.GetRawLogger().Errorf("Failed to build action %s for event %s: %v", actionCfg.Type, eventConfig.Name, buildErr)
					execErr = buildErr
				} else {
					serverLogger.GetRawLogger().Debugf("Executing action %T for event %s", actionInstance, eventConfig.Name)

							// 新增日志，检查 actCtx 和 reqWithDNSCtx
							if actCtx == nil {
						serverLogger.GetRawLogger().Errorf("executeTriggers: actCtx is nil before calling actionInstance.Execute for action type %s!", actionCfg.Type)
					} else {
						serverLogger.GetRawLogger().Debugf("executeTriggers: actCtx is NOT nil before calling actionInstance.Execute. Type: %T", actCtx)
					}
							if reqWithDNSCtx == nil {
						serverLogger.GetRawLogger().Errorf("executeTriggers: reqWithDNSCtx is nil before calling actionInstance.Execute for action type %s!", actionCfg.Type)
					} else {
						serverLogger.GetRawLogger().Debugf("executeTriggers: reqWithDNSCtx is NOT nil before calling actionInstance.Execute. Method: %s, URL: %s", reqWithDNSCtx.Method, reqWithDNSCtx.URL)
					}

							actionResp, execErr = actionInstance.Execute(actCtx, reqWithDNSCtx, resp, p.Options.ProxyManager)

							// Check context for stopProcessing signal, if actions set it.
							// Example: if val, ok := actCtx.Value("stop_processing").(bool); ok && val { stopProcessing = true }
							// A common pattern is for actions like BanIP (on context cancel) to return a specific error
							// or modify the response (e.g. nil response) to signal this.
							// If context.Canceled is the error, we might want to stop.
							if stderrors.Is(execErr, context.Canceled) {
						serverLogger.GetRawLogger().Infof("Action %s for event %s was canceled, stopping further processing in this sequence.", actionCfg.Type, eventConfig.Name)
						stopProcessing = true
						// If context is canceled, actionResp might be nil, and that's often intended.
					} else if execErr != nil {
						serverLogger.GetRawLogger().Errorf("Error executing action %s for event %s: %v", actionCfg.Type, eventConfig.Name, execErr)
					}
						}
					}

					if execErr != nil {
				serverLogger.GetRawLogger().Errorf("执行动作 %s 失败: %v", actionCfg.Type, execErr)
				// continue // Or break, depending on desired error handling for sequence
			}

					if requiresRetry, ok := reqWithDNSCtx.Context().Value("action_requires_retry").(bool); ok && requiresRetry {
						// ... (retry logic remains similar but needs to be triggered correctly)
					}

					if actionResp != nil {
						finalResp = actionResp
					} else if stopProcessing { // If an action (like BanIP on context cancel) decided to stop and returned nil
						// 可能设置占位符响应或确保在nil时处理finalResp
						serverLogger.GetRawLogger().Debugf("Action %s indicated stopProcessing, finalResp might be nil.", actionCfg.Type)
						// If finalResp is nil here, it means the action handled the response (e.g. by closing connection)
						// or a placeholder should be used. For now, if stopProcessing is true and actionResp is nil,
						// we assume the action took care of the response.
						if finalResp == nil { // 确保如果要中断，finalResp不为nil
							// This might mean we need a way for actions to signal they've "nulled" the response
							// finalResp = createNullResponse(reqWithDNSCtx) // Example placeholder
						}
					}

					if finalResp != nil { // 如果序列中的任何动作设置了最终响应
					serverLogger.GetRawLogger().Debugf("动作 %s 已设置最终响应或停止处理。", actionCfg.Type)
					if t.GetPriority() == 1 || stopProcessing { // 为最高优先级或动作要求时停止
						serverLogger.Debug("最高优先级触发器或停止信号，立即返回响应")
						return // 从此触发器的匿名函数返回
					}
						// If not highest priority but an action sequence provided a response,
						// we might want to stop processing further actions in *this* trigger's sequence.
						// However, other lower-priority triggers might still run.
						// This break will exit the loop over actionConfigs for the current trigger.
						break
					}
				}
			} else {
				serverLogger.GetRawLogger().Debugf("触发器不匹配，继续检查下一个触发器")
			}
		}()

		// 如果已经有了最终响应，不再继续处理其他触发器
		if finalResp != nil {
			break
		}
	}

	return finalResp
}

// 新增：根据事件配置创建DNS解析器
func (p *Proxy) createDNSResolverForEvent(eventConfig *common.EventConfig, req *http.Request) DNSResolver {
	serverLogger.GetRawLogger().Debugf("为事件创建DNS解析器，模式: %s, 自定义DNS服务器: %+v", eventConfig.DNSLookupMode, eventConfig.CustomDNSServers)

	// 记录请求信息，帮助调试
	if req != nil {
		serverLogger.GetRawLogger().Debugf("请求URL: %s, 主机名: %s", req.URL.String(), req.URL.Hostname())
	}

	// 检查事件配置
	if eventConfig == nil {
		serverLogger.Warn("事件配置为空，无法创建DNS解析器")
		return nil
	}

	// 从请求上下文中获取DNS缓存参数，并将这些参数传递给创建的DNS解析器
	noCache := false
	var cacheTTL time.Duration
	// var forceRefresh bool

	// 检查是否启用DNS缓存
	if ctxVal := req.Context().Value("dns_cache_enabled"); ctxVal != nil {
		if enabled, ok := ctxVal.(bool); ok && !enabled {
			noCache = true
			serverLogger.Debug("从上下文中检测到DNS缓存已禁用")
		}
	}

	// 获取DNS缓存TTL
	if ctxVal := req.Context().Value("dns_cache_ttl"); ctxVal != nil {
		if ttl, ok := ctxVal.(time.Duration); ok && ttl > 0 {
			cacheTTL = ttl
			serverLogger.GetRawLogger().Debugf("从上下文中获取DNS缓存TTL: %s", cacheTTL)
		}
	}

	// 检查是否强制刷新DNS缓存
	if ctxVal := req.Context().Value("dns_force_refresh"); ctxVal != nil {
		if refresh, ok := ctxVal.(bool); ok && refresh {
			// forceRefresh = true
			serverLogger.Debug("从上下文中检测到强制刷新DNS缓存标志")
		}
	}

	if eventConfig.DNSLookupMode == "remote" {
		serverLogger.Debug("使用远程DNS解析模式")
		// 获取当前代理地址用于远程DNS解析
		serverLogger.Debug("尝试获取代理用于远程DNS解析")
		currentProxy, err := p.rotateProxy()
		if err != nil {
			serverLogger.GetRawLogger().Warnf("代理池为空或无可用代理，暂停DNS请求: %v", err)
			return nil
		}
		serverLogger.GetRawLogger().Infof("成功获取代理用于远程DNS解析: %s", currentProxy)

		// 使用事件级别的HTTPProxyDNS选项（如果配置了），否则使用全局配置
		var httpProxyDNS string
		if eventConfig.HTTPProxyDNS != "" {
			httpProxyDNS = eventConfig.HTTPProxyDNS
			serverLogger.GetRawLogger().Debugf("为事件 '%s' 使用事件级别HTTPProxyDNS配置: %s", eventConfig.Name, httpProxyDNS)
		} else if p.Options.RuleConfig != nil {
			httpProxyDNS = p.Options.RuleConfig.Global.HTTPProxyDNS
			serverLogger.GetRawLogger().Debugf("为事件 '%s' 使用全局HTTPProxyDNS配置: %s", eventConfig.Name, httpProxyDNS)
		}

		serverLogger.GetRawLogger().Debugf("为事件 '%s' 创建远程DNS解析器，使用代理: %s, HTTPProxyDNS: %s", eventConfig.Name, currentProxy, httpProxyDNS)
		remoteDNSResolver := NewRemoteDNSResolver(currentProxy, httpProxyDNS)
		serverLogger.GetRawLogger().Debugf("为事件 '%s' 远程DNS解析器创建成功", eventConfig.Name)
		return remoteDNSResolver
	} else if eventConfig.DNSLookupMode == "local" || (eventConfig.DNSLookupMode == "" && p.Options.RuleConfig.Global.DNSLookupMode != "remote") {
		// 本地模式下，优先使用事件级别定义的自定义DNS服务器，然后是全局，最后是系统默认
		if len(eventConfig.CustomDNSServers) > 0 {
			// 使用事件级别的自定义DNS服务器 (取第一个)
			eventCustomServer := eventConfig.CustomDNSServers[0]
			serverLogger.GetRawLogger().Debugf("为事件 '%s' 创建自定义DNS解析器，使用事件级别服务器: %s", eventConfig.Name, eventCustomServer.Server)
			customDNSResolver := NewCustomDNSResolver(eventCustomServer.Server)

			// 设置DNS缓存参数
			// 首先检查事件级别的配置
			if eventConfig.DNSNoCache {
				noCache = true
				serverLogger.Debug("使用事件级别配置：禁用DNS缓存")
			} else if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.DNSNoCache {
				// 如果事件级别未配置，则使用全局配置
				noCache = true
				serverLogger.Debug("使用全局配置：禁用DNS缓存")
			}

			// 设置缓存TTL
			if eventConfig.DNSCacheTTL > 0 {
				// 使用事件级别的TTL
				cacheTTL = time.Duration(eventConfig.DNSCacheTTL) * time.Second
				serverLogger.GetRawLogger().Debugf("使用事件级别DNS缓存TTL: %s", cacheTTL)
			} else if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.DNSCacheTTL > 0 && cacheTTL == 0 {
				// 如果事件级别未配置且上下文中没有设置，则使用全局配置
				cacheTTL = time.Duration(p.Options.RuleConfig.Global.DNSCacheTTL) * time.Second
				serverLogger.GetRawLogger().Debugf("使用全局DNS缓存TTL: %s", cacheTTL)
			}

			// 应用缓存设置
			customDNSResolver.NoCache = noCache
			if cacheTTL > 0 {
				customDNSResolver.CacheTTL = cacheTTL
			}

			serverLogger.Debug("自定义DNS解析器创建成功")
			return customDNSResolver
		} else if p.Options.RuleConfig != nil && len(p.Options.RuleConfig.Global.CustomDNSServers) > 0 {
			// 使用全局配置的自定义DNS服务器 (取第一个)
			globalCustomServer := p.Options.RuleConfig.Global.CustomDNSServers[0]
			serverLogger.GetRawLogger().Debugf("为事件 '%s' 创建自定义DNS解析器，使用全局服务器: %s", eventConfig.Name, globalCustomServer.Server)
			customDNSResolver := NewCustomDNSResolver(globalCustomServer.Server)
			// 全局自定义DNS也需要应用事件的缓存策略（如果事件定义了）
			customDNSResolver.NoCache = eventConfig.DNSNoCache
			if !customDNSResolver.NoCache && p.Options.RuleConfig != nil { // 如果事件允许缓存，检查全局设置
				customDNSResolver.NoCache = p.Options.RuleConfig.Global.DNSNoCache
			}
			if eventConfig.DNSCacheTTL > 0 {
				customDNSResolver.CacheTTL = time.Duration(eventConfig.DNSCacheTTL) * time.Second
			} else if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.DNSCacheTTL > 0 {
				customDNSResolver.CacheTTL = time.Duration(p.Options.RuleConfig.Global.DNSCacheTTL) * time.Second
			}
			return customDNSResolver
		} else {
			// 如果事件和全局都没有配置自定义DNS服务器，使用系统默认解析器
			serverLogger.GetRawLogger().Debugf("为事件 '%s' 创建标准本地DNS解析器 (无事件或全局自定义DNS)", eventConfig.Name)
			localDNSResolver := &LocalDNSResolver{}
			// 本地解析器也应考虑事件的缓存策略
			// (LocalDNSResolver 当前没有直接的 NoCache/CacheTTL 字段，这可能需要进一步调整或假设其行为)
			serverLogger.GetRawLogger().Debugf("为事件 '%s' 标准本地DNS解析器创建成功", eventConfig.Name)
			return localDNSResolver
		}
	}

	// 如果没有特殊配置，返回nil表示使用默认解析器
	serverLogger.Debug("没有特殊DNS配置，将使用默认解析器")
	serverLogger.Debug("未找到匹配的DNS解析器配置，使用默认解析器")
	return nil
}

// handleRetrySameAction 处理使用相同IP重试的逻辑
func (p *Proxy) handleRetrySameAction(req *http.Request, originalResp *http.Response) *http.Response {
	// 从请求上下文中获取重试信息
	retryCount, ok := req.Context().Value("retry_same_count").(int)
	if !ok || retryCount <= 0 {
		serverLogger.Warn("handleRetrySameAction: 无法获取重试次数或重试次数无效")
		return nil
	}

	currentProxy, ok := req.Context().Value("retry_same_proxy").(string)
	if !ok || currentProxy == "" {
		serverLogger.Warn("handleRetrySameAction: 无法获取当前代理")
		return nil
	}

	serverLogger.GetRawLogger().Infof("handleRetrySameAction: 开始使用相同代理 %s 重试 %d 次", currentProxy, retryCount)

	var lastSuccessResp *http.Response

	// 执行重试逻辑 - 执行完整的重试次数
	for i := 0; i < retryCount; i++ {
		serverLogger.GetRawLogger().Debugf("handleRetrySameAction: 第 %d 次重试，使用代理: %s", i+1, currentProxy)

		// 创建重试请求的上下文
		retryCtx := context.WithValue(req.Context(), "current_proxy", currentProxy)
		retryCtx = context.WithValue(retryCtx, "retry_attempt", i+1)
		retryReq := req.WithContext(retryCtx)

		// 获取客户端
		retryablehttpClient, err := p.getClient(retryReq, req.URL.Hostname())
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 第 %d 次重试创建客户端失败: %v", i+1, err)
			continue
		}

		// 创建retryablehttp请求
		retryablehttpRequest, err := retryablehttp.FromRequest(retryReq)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 第 %d 次重试创建请求失败: %v", i+1, err)
			continue
		}

		// 发送重试请求
		retryResp, err := retryablehttpClient.Do(retryablehttpRequest)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 第 %d 次重试请求失败: %v", i+1, err)
			continue
		}

		// 检查响应是否成功
		if retryResp.StatusCode >= 200 && retryResp.StatusCode < 400 {
			serverLogger.GetRawLogger().Infof("handleRetrySameAction: 第 %d 次重试成功，状态码: %d", i+1, retryResp.StatusCode)

			// 读取响应体
			buf, err := io.ReadAll(retryResp.Body)
			if err != nil {
				serverLogger.GetRawLogger().Errorf("handleRetrySameAction: 读取重试响应体失败: %v", err)
				retryResp.Body.Close()
				continue
			}
			retryResp.Body.Close()
			retryResp.Body = io.NopCloser(bytes.NewBuffer(buf))

			// 保存最后一次成功的响应，但继续执行剩余重试
			if lastSuccessResp != nil && lastSuccessResp.Body != nil {
				lastSuccessResp.Body.Close()
			}
			lastSuccessResp = retryResp
		} else {
			serverLogger.GetRawLogger().Warnf("handleRetrySameAction: 第 %d 次重试返回错误状态码: %d", i+1, retryResp.StatusCode)
			retryResp.Body.Close()
		}
	}

	if lastSuccessResp != nil {
		serverLogger.GetRawLogger().Infof("handleRetrySameAction: 完成所有 %d 次重试，返回最后一次成功的响应", retryCount)
		return lastSuccessResp
	}

	serverLogger.GetRawLogger().Warnf("handleRetrySameAction: 所有 %d 次重试都失败", retryCount)
	return nil
}

// handleRetryAction 处理使用新IP重试的逻辑
func (p *Proxy) handleRetryAction(req *http.Request, originalResp *http.Response) *http.Response {
	// 从请求上下文中获取重试信息
	retryCount, ok := req.Context().Value("retry_count").(int)
	if !ok || retryCount <= 0 {
		serverLogger.Warn("handleRetryAction: 无法获取重试次数或重试次数无效")
		return nil
	}

	// 检查代理池最小可用数量
	minPool := 1
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MinProxyPoolSize > 0 {
		minPool = p.Options.RuleConfig.Global.MinProxyPoolSize
	}

	// 检查代理池总数量
	totalProxies := p.Options.ProxyManager.Count()
	serverLogger.GetRawLogger().Debugf("handleRetryAction: 代理池总数量: %d, 最小要求: %d, 需要重试次数: %d", totalProxies, minPool, retryCount)

	// 预先检查：如果代理池数量不足以支持重试需求，直接返回
	if totalProxies <= minPool {
		serverLogger.GetRawLogger().Warnf("handleRetryAction: 代理池数量(%d)等于或小于最小要求(%d)，无法进行新IP重试", totalProxies, minPool)
		return nil
	}

	// 检查是否有足够的不同IP来满足重试需求
	// 考虑到当前已经使用了一个代理，剩余可用代理数量应该足够支持重试
	availableForRetry := totalProxies - 1 // 减去当前正在使用的代理
	if availableForRetry < retryCount {
		serverLogger.GetRawLogger().Warnf("handleRetryAction: 可用于重试的代理数量(%d)不足以满足重试需求(%d次)，停止重试", availableForRetry, retryCount)
		return nil
	}

	serverLogger.GetRawLogger().Infof("handleRetryAction: 开始使用新IP重试 %d 次", retryCount)

	// 获取最大尝试次数配置
	maxAttempts := 10
	if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.MaxProxyFetchAttempts > 0 {
		maxAttempts = p.Options.RuleConfig.Global.MaxProxyFetchAttempts
	}

	var lastSuccessResp *http.Response
	var usedProxies []string // 记录已使用的代理，避免重复

	// 将当前代理添加到已使用列表中
	if p.currentProxy != "" {
		usedProxies = append(usedProxies, p.currentProxy)
		serverLogger.GetRawLogger().Debugf("handleRetryAction: 将当前代理 %s 添加到已使用列表", p.currentProxy)
	}

	// 执行重试逻辑 - 每次使用不同的IP
	for i := 0; i < retryCount; i++ {
		serverLogger.GetRawLogger().Debugf("handleRetryAction: 第 %d 次重试，尝试获取新代理", i+1)

		// 再次检查代理池数量是否足够（动态检查）
		currentAvailable := p.Options.ProxyManager.Count()
		if currentAvailable <= minPool {
			serverLogger.GetRawLogger().Warnf("handleRetryAction: 代理池数量(%d)已降至最小要求(%d)或以下，停止重试", currentAvailable, minPool)
			break
		}

		// 获取新的代理IP
		var newProxy string
		var err error
		attemptCount := 0

		// 尝试获取未被封禁且未使用过的代理
		for attemptCount < maxAttempts {
			// 读取全局IP轮询方式
			ipRotationMode := "sequential"
			if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.IPRotationMode != "" {
				ipRotationMode = p.Options.RuleConfig.Global.IPRotationMode
			}

			// 获取新代理 - 在重试时直接调用底层方法避免缓存
			// 获取新代理 - 使用 GetProxy 接口方法
			newProxy, err = p.Options.ProxyManager.GetProxy(ipRotationMode)
			if err != nil {
				serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试获取代理失败: %v", i+1, err)
				break
			}

			// 检查是否已经使用过这个代理（本次重试会话）
			sessionUsed := false
			for _, used := range usedProxies {
				if used == newProxy {
					sessionUsed = true
					break
				}
			}

			// 检查是否在全局冷却期内被使用过
			globalRecentlyUsed := globalProxyUsage.IsRecentlyUsed(newProxy)

			if sessionUsed {
				serverLogger.GetRawLogger().Debugf("handleRetryAction: 代理 %s 在本次重试会话中已使用过，尝试获取其他代理", newProxy)
				attemptCount++
				continue
			}

			if globalRecentlyUsed {
				serverLogger.GetRawLogger().Debugf("handleRetryAction: 代理 %s 在全局冷却期内被使用过，尝试获取其他代理", newProxy)
				attemptCount++

				// 如果尝试次数过多，说明可能没有足够的不同代理
				if attemptCount >= maxAttempts/2 {
					serverLogger.GetRawLogger().Warnf("handleRetryAction: 尝试获取新代理次数过多，可能代理池中没有足够的不同代理")
				}
				continue
			}

			// 检查代理是否被封禁
			if p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.Enable {
				domain := req.URL.Hostname()
				url := req.URL.String()

				// 检查全局封禁
				if p.Options.ProxyManager.IsIPBanned(newProxy, "", "global") {
					serverLogger.GetRawLogger().Debugf("handleRetryAction: 代理 %s 被全局封禁，跳过", newProxy)
					attemptCount++
					continue
				}

				// 检查域名级封禁
				if p.Options.ProxyManager.IsIPBanned(newProxy, domain, "domain") {
					serverLogger.GetRawLogger().Debugf("handleRetryAction: 代理 %s 被域名 %s 封禁，跳过", newProxy, domain)
					attemptCount++
					continue
				}

				// 检查URL级封禁
				if p.Options.ProxyManager.IsIPBanned(newProxy, url, "url") {
					serverLogger.GetRawLogger().Debugf("handleRetryAction: 代理 %s 被URL %s 封禁，跳过", newProxy, url)
					attemptCount++
					continue
				}

				// 检查永久封禁
				if p.Options.ProxyManager.IsIPPermanentlyBlocked(newProxy) {
					serverLogger.GetRawLogger().Debugf("handleRetryAction: 代理 %s 被永久封禁，跳过", newProxy)
					attemptCount++
					continue
				}
			}

			// 找到可用的代理
			break
		}

		if attemptCount >= maxAttempts {
			serverLogger.GetRawLogger().Warnf("handleRetryAction: 第 %d 次重试达到最大尝试次数(%d)，无法找到可用代理，停止后续重试", i+1, maxAttempts)
			break
		}

		if err != nil || newProxy == "" {
			serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试无法获取新代理: %v，停止后续重试", i+1, err)
			break
		}

		serverLogger.GetRawLogger().Debugf("handleRetryAction: 第 %d 次重试使用新代理: %s", i+1, newProxy)
		usedProxies = append(usedProxies, newProxy)

		// 标记代理为全局已使用
		globalProxyUsage.MarkAsUsed(newProxy)

		// 创建重试请求的上下文
		retryCtx := context.WithValue(req.Context(), "current_proxy", newProxy)
		retryCtx = context.WithValue(retryCtx, "retry_attempt", i+1)
		retryReq := req.WithContext(retryCtx)

		// 获取客户端
		retryablehttpClient, err := p.getClient(retryReq, req.URL.Hostname())
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试创建客户端失败: %v", i+1, err)
			continue
		}

		// 创建retryablehttp请求
		retryablehttpRequest, err := retryablehttp.FromRequest(retryReq)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试创建请求失败: %v", i+1, err)
			continue
		}

		// 发送重试请求
		retryResp, err := retryablehttpClient.Do(retryablehttpRequest)
		if err != nil {
			serverLogger.GetRawLogger().Errorf("handleRetryAction: 第 %d 次重试请求失败: %v", i+1, err)
			continue
		}

		// 检查响应是否成功
		if retryResp.StatusCode >= 200 && retryResp.StatusCode < 400 {
			serverLogger.GetRawLogger().Infof("handleRetryAction: 第 %d 次重试成功，状态码: %d，使用代理: %s", i+1, retryResp.StatusCode, newProxy)

			// 读取响应体
			buf, err := io.ReadAll(retryResp.Body)
			if err != nil {
				serverLogger.GetRawLogger().Errorf("handleRetryAction: 读取重试响应体失败: %v", err)
				retryResp.Body.Close()
				continue
			}
			retryResp.Body.Close()
			retryResp.Body = io.NopCloser(bytes.NewBuffer(buf))

			// 保存最后一次成功的响应，但继续执行剩余重试
			if lastSuccessResp != nil && lastSuccessResp.Body != nil {
				lastSuccessResp.Body.Close()
			}
			lastSuccessResp = retryResp

			// 更新当前代理为成功的代理
			p.currentProxy = newProxy
		} else {
			serverLogger.GetRawLogger().Warnf("handleRetryAction: 第 %d 次重试返回错误状态码: %d，使用代理: %s", i+1, retryResp.StatusCode, newProxy)
			retryResp.Body.Close()
		}
	}

	if lastSuccessResp != nil {
			serverLogger.GetRawLogger().Infof("handleRetryAction: 完成重试，返回最后一次成功的响应")
		return lastSuccessResp
	}

	serverLogger.GetRawLogger().Warnf("handleRetryAction: 所有重试都失败")
	return nil
}
