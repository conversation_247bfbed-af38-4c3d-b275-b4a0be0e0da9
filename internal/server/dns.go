// dns.go
package server

import (
	"bufio"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/miekg/dns"
	"golang.org/x/net/proxy"
)

var serverLogger = logger.GetServerLogger()

// DNSResolver 定义DNS解析器接口
type DNSResolver interface {
	LookupIP(ctx context.Context, host string) ([]net.IP, error)
	LookupAddr(ctx context.Context, addr string) ([]string, error)
}

// LocalDNSResolver 本地DNS解析器
type LocalDNSResolver struct{}

// RemoteDNSResolver 远程DNS解析器
type RemoteDNSResolver struct {
	ProxyAddr string // 代理地址
	DNSServer string // HTTP/HTTPS代理DNS解析专用选项
}

// NewRemoteDNSResolver 创建远程DNS解析器
func NewRemoteDNSResolver(proxyAddr string, dnsServer string) *RemoteDNSResolver {
	return &RemoteDNSResolver{
		ProxyAddr: proxyAddr,
		DNSServer: dnsServer,
	}
}

// ReverseDNSResolver 反向DNS解析器
type ReverseDNSResolver struct {
	Mode    string            // "no", "dns", "file", "values"
	Mapping map[string]string // IP到域名的映射
	mu      sync.RWMutex
}

func (r *ReverseDNSResolver) LookupIP(ctx context.Context, host string) ([]net.IP, error) {
	// 反向DNS解析器主要关注IP到域名的映射
	// 但为了完整实现DNSResolver接口，我们需要提供LookupIP方法

	// 根据模式处理
	switch r.Mode {
	case "no":
		// 不进行反向DNS解析时，直接使用系统默认解析器
		return net.DefaultResolver.LookupIP(ctx, "ip", host)

	case "dns":
		// 使用系统DNS进行解析
		return net.DefaultResolver.LookupIP(ctx, "ip", host)

	case "file", "values":
		// 检查是否有自定义映射（反向查找）
		r.mu.RLock()
		defer r.mu.RUnlock()

		// 检查是否有域名到IP的反向映射
		for ip, domain := range r.Mapping {
			if domain == host {
				parsedIP := net.ParseIP(ip)
				if parsedIP != nil {
					return []net.IP{parsedIP}, nil
				}
			}
		}
	}

	// 如果没有找到映射或模式不匹配，使用系统默认解析器
	return net.DefaultResolver.LookupIP(ctx, "ip", host)
}

// 创建ReverseDNSResolver
func NewReverseDNSResolver(mode string, config string) (*ReverseDNSResolver, error) {
	resolver := &ReverseDNSResolver{
		Mode:    mode,
		Mapping: make(map[string]string),
	}

	switch mode {
	case "no":
		// 不做任何操作
	case "dns":
		// 使用标准DNS
	case "file":
		// 从文件加载
		if strings.HasPrefix(config, "file:") {
			filePath := strings.TrimPrefix(config, "file:")
			err := resolver.loadFromFile(filePath)
			if err != nil {
				return nil, err
			}
		}
	default:
		// 直接值模式
		mappings := strings.Split(config, ",")
		for _, mapping := range mappings {
			parts := strings.Fields(mapping)
			if len(parts) >= 2 {
				ip := parts[0]
				domain := parts[1]
				resolver.Mapping[ip] = domain
			}
		}
	}

	return resolver, nil
}

// 从文件加载IP到域名的映射
func (r *ReverseDNSResolver) loadFromFile(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		parts := strings.Fields(line)
		if len(parts) >= 2 {
			ip := parts[0]
			domain := parts[1]
			r.Mapping[ip] = domain
		}
	}

	return scanner.Err()
}

// GetMode 返回反向DNS解析器的模式
func (r *ReverseDNSResolver) GetMode() string {
	// 安全检查，确保r不为nil
	if r == nil {
		// 如果r为nil，返回"no"作为默认模式
		serverLogger.Debug("反向DNS解析器为nil，返回默认模式'no'")
		return "no"
	}
	return r.Mode
}

// 查询IP对应的域名
func (r *ReverseDNSResolver) LookupAddr(ctx context.Context, addr string) ([]string, error) {
	// 添加日志记录解析开始
	serverLogger.GetRawLogger().Debugf("开始反向DNS解析，模式: %s, IP: %s", r.Mode, addr)

	r.mu.RLock()
	defer r.mu.RUnlock()

	switch r.Mode {
	case "no":
		// 不进行反向DNS解析，直接返回空结果
		serverLogger.GetRawLogger().Debugf("反向DNS解析模式为'no'，跳过解析")
		return nil, nil
	case "dns":
		// 增加超时控制，避免阻塞
		timeoutCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
		defer cancel()
		serverLogger.GetRawLogger().Debugf("使用系统DNS进行反向解析: %s", addr)
		result, err := net.DefaultResolver.LookupAddr(timeoutCtx, addr)
		if err != nil {
			serverLogger.GetRawLogger().Debugf("系统DNS反向解析失败: %v", err)
			return nil, err
		}
		return result, nil
	default:
		// 查找映射
		if domain, ok := r.Mapping[addr]; ok {
			serverLogger.GetRawLogger().Debugf("从映射中找到域名: %s -> %s", addr, domain)
			return []string{domain}, nil
		} else {
			serverLogger.GetRawLogger().Debugf("在映射中未找到IP %s 的域名", addr)
		}
	}

	return nil, nil
}

// LookupIP 实现本地DNS解析
func (r *LocalDNSResolver) LookupIP(ctx context.Context, host string) ([]net.IP, error) {
	// 检查是否是本地IP地址，如果是则直接返回，避免不必要的DNS查询
	if ip := net.ParseIP(host); ip != nil {
		serverLogger.GetRawLogger().Debugf("主机名 %s 已经是IP地址，跳过DNS解析", host)
		return []net.IP{ip}, nil
	}

	// 记录解析开始时间
	dnsStartTime := time.Now()
	defer func() {
		// 记录解析耗时
		dnsElapsed := time.Since(dnsStartTime)
		serverLogger.GetRawLogger().Debugf("DNS解析耗时: %s 用于主机: %s", dnsElapsed, host)
	}()

	// 使用系统默认解析器在本地解析
	return net.DefaultResolver.LookupIP(ctx, "ip", host)
}

// LookupAddr 实现本地DNS反向解析，增加超时控制

// LookupIP 实现远程DNS解析，通过代理地址解析
func (r *RemoteDNSResolver) LookupIP(ctx context.Context, host string) ([]net.IP, error) {

	// 检查是否是本地IP地址，如果是则直接返回，避免不必要的DNS查询
	if ip := net.ParseIP(host); ip != nil {
		serverLogger.GetRawLogger().Debugf("主机名 %s 已经是IP地址，跳过DNS解析", host)
		return []net.IP{ip}, nil
	}

	// 检查是否启用了缓存，并且不是强制刷新
	forceRefresh := false
	if ctxVal := ctx.Value("dns_force_refresh"); ctxVal != nil {
		if forceRefreshVal, ok := ctxVal.(bool); ok && forceRefreshVal {
			forceRefresh = true
			serverLogger.GetRawLogger().Debugf("强制刷新DNS缓存: %s", host)
		}
	}

	// 尝试从缓存获取结果
	if !forceRefresh {
		dnsCacheLock.RLock()
		cached, exists := dnsCache[host]
		dnsCacheLock.RUnlock()

		// 如果有有效缓存，直接返回
		if exists && time.Now().Before(cached.ExpireAt) {
			serverLogger.GetRawLogger().Debugf("使用DNS缓存结果: %s, IP数量: %d", host, len(cached.IPs))
			return cached.IPs, nil
		} else if exists {
			serverLogger.GetRawLogger().Debugf("DNS缓存已过期: %s", host)
		}
	}

	// 如果没有设置代理地址，返回错误
	if r.ProxyAddr == "" {
		return nil, errors.NewError(errors.ErrTypeDNS, errors.ErrCodeDNSConfigInvalid, "远程DNS解析需要设置代理地址")
	}
	// 如果配置了HTTPProxyDNS，优先使用它进行解析
	if r.DNSServer != "" {
		// 记录日志
		serverLogger.GetRawLogger().Debugf("使用HTTPProxyDNS服务器: %s 解析主机: %s", r.DNSServer, host)
		// 创建自定义DNS解析器
		// 确保DNSServer是完整URL格式，如果不是则添加https://前缀和/dns-query后缀
		dnsServerURL := r.DNSServer
		if !strings.HasPrefix(dnsServerURL, "http") {
			dnsServerURL = "https://" + dnsServerURL + "/dns-query"
		}

		customResolver := NewCustomDNSResolver(dnsServerURL + "@doh")
		if customResolver != nil && len(customResolver.Servers) > 0 {
			ips, err := customResolver.LookupIP(ctx, host)
			if err == nil && len(ips) > 0 {
				// 缓存结果
				cacheTTL := defaultDNSCacheTTL
				if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
					if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
						cacheTTL = customTTL
						serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
					}
				}

				dnsCacheLock.Lock()
				dnsCache[host] = &CachedDNSResult{
					IPs:      ips,
					ExpireAt: time.Now().Add(cacheTTL),
				}
				dnsCacheLock.Unlock()

				serverLogger.GetRawLogger().Debugf("DNS结果已缓存: %s, 过期时间: %s", host, time.Now().Add(cacheTTL).Format(time.RFC3339))
				return ips, nil
			}
			// 如果自定义DNS解析失败，记录日志并继续使用代理解析
			// 记录日志
			serverLogger.GetRawLogger().Debugf("使用HTTPProxyDNS解析失败: %v，尝试使用代理解析", err)
		} else {
			// 记录日志
			serverLogger.GetRawLogger().Debugf("HTTPProxyDNS配置无效或解析失败: %s", r.DNSServer)
		}
	}
	// 解析代理地址
	proxyURL, err := url.Parse(r.ProxyAddr)
	if err != nil {
		return nil, errors.WrapError(err, errors.ErrTypeDNS, errors.ErrCodeDNSConfigInvalid, "解析代理地址失败")
	}

	// 根据代理类型创建不同的解析器
	switch proxyURL.Scheme {
	case "http", "https":
		// 创建HTTP客户端，使用代理
		transport := &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}
		client := &http.Client{Transport: transport}

		// 如果设置了自定义DNS服务器，使用DoH
		if r.DNSServer != "" {
			// 确定DNS服务器URL
			dnsServerURL := r.DNSServer
			if !strings.HasPrefix(dnsServerURL, "http") {
				dnsServerURL = "https://" + dnsServerURL + "/dns-query"
			}

			// 构建DoH请求URL
			requestURL := dnsServerURL
			if !strings.Contains(requestURL, "?") {
				requestURL += "?"
			} else if !strings.HasSuffix(requestURL, "&") {
				requestURL += "&"
			}
			requestURL += "name=" + url.QueryEscape(host) + "&type=A"

			// 发送请求
			req, err := http.NewRequestWithContext(ctx, "GET", requestURL, nil)
			if err != nil {
				return nil, err
			}
			req.Header.Set("Accept", "application/dns-json")

			resp, err := client.Do(req)
			if err != nil {
				return nil, err
			}
			defer resp.Body.Close()

			// 解析响应
			if resp.StatusCode != http.StatusOK {
				return nil, errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "DoH请求失败", fmt.Sprintf("状态: %s", resp.Status))
			}

			body, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, err
			}

			// 解析JSON响应
			var result struct {
				Status int `json:"Status"`
				Answer []struct {
					Type int    `json:"type"`
					Data string `json:"data"`
				} `json:"Answer"`
			}

			if err := json.Unmarshal(body, &result); err != nil {
				return nil, err
			}

			// 提取IP地址 (同时支持A和AAAA记录)
			var ips []net.IP
			for _, answer := range result.Answer {
				// Type 1 = A记录, Type 28 = AAAA记录
				if answer.Type == 1 || answer.Type == 28 {
					ip := net.ParseIP(answer.Data)
					if ip != nil {
						ips = append(ips, ip)
					}
				}
			}

			if len(ips) == 0 {
				return nil, errors.NewError(errors.ErrTypeDNS, errors.ErrCodeDNSNoResult, "未找到有效的IP地址")
			}

			return ips, nil
		} else {
			// 直接通过HTTP代理解析DNS
			// 创建一个测试连接来解析主机名
			hostWithPort := host
			if !strings.Contains(host, ":") {
				hostWithPort = host + ":80" // 默认HTTP端口
			}

			// 创建一个临时请求来解析主机名
			req, err := http.NewRequestWithContext(ctx, "HEAD", "http://"+hostWithPort, nil)
			if err != nil {
				return nil, err
			}

			// 发送请求，让代理解析DNS
			resp, err := client.Do(req)
			if err != nil {
				// 尝试解析错误中的IP地址
				ipStr := extractIPFromError(err)
				if ipStr != "" {
					ip := net.ParseIP(ipStr)
					if ip != nil {
						// 缓存结果
						cacheTTL := defaultDNSCacheTTL
						if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
							if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
								cacheTTL = customTTL
								serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
							}
						}

						dnsCacheLock.Lock()
						dnsCache[host] = &CachedDNSResult{
							IPs:      []net.IP{ip},
							ExpireAt: time.Now().Add(cacheTTL),
						}
						dnsCacheLock.Unlock()

						serverLogger.GetRawLogger().Debugf("DNS结果已缓存: %s, 过期时间: %s", host, time.Now().Add(cacheTTL).Format(time.RFC3339))
						return []net.IP{ip}, nil
					}
				}
				return nil, err
			}
			defer resp.Body.Close()

			// 如果成功连接，尝试从响应中获取IP
			if resp.StatusCode != http.StatusOK {
				// 即使状态码不是200，我们也成功解析了DNS
				// 尝试从Transport中获取IP
				if tcpAddr, ok := resp.Request.Context().Value(http.LocalAddrContextKey).(*net.TCPAddr); ok {
					// 缓存结果
					cacheTTL := defaultDNSCacheTTL
					if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
						if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
							cacheTTL = customTTL
							serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
						}
					}

					dnsCacheLock.Lock()
					dnsCache[host] = &CachedDNSResult{
						IPs:      []net.IP{tcpAddr.IP},
						ExpireAt: time.Now().Add(cacheTTL),
					}
					dnsCacheLock.Unlock()

					serverLogger.GetRawLogger().Debugf("DNS结果已缓存: %s, 过期时间: %s", host, time.Now().Add(cacheTTL).Format(time.RFC3339))
					return []net.IP{tcpAddr.IP}, nil
				}
			}

			// 如果无法从响应中获取IP，使用系统解析器作为后备
			ips, err := net.DefaultResolver.LookupIP(ctx, "ip", host)
			if err == nil && len(ips) > 0 {
				// 缓存结果
				cacheTTL := defaultDNSCacheTTL
				if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
					if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
						cacheTTL = customTTL
						serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
					}
				}

				dnsCacheLock.Lock()
				dnsCache[host] = &CachedDNSResult{
					IPs:      ips,
					ExpireAt: time.Now().Add(cacheTTL),
				}
				dnsCacheLock.Unlock()

				serverLogger.GetRawLogger().Debugf("DNS结果已缓存: %s, 过期时间: %s", host, time.Now().Add(cacheTTL).Format(time.RFC3339))
			}
			return ips, err
		}

	case "socks4", "socks4a", "socks5":
		// 使用SOCKS代理进行DNS解析
		// 获取默认DNS超时时间
		timeout := getDefaultDNSTimeout(ctx)

		dialer, err := proxy.FromURL(proxyURL, &net.Dialer{
			Timeout: timeout,
		})
		if err != nil {
			return nil, errors.WrapError(err, errors.ErrTypeProxy, errors.ErrCodeProxyConnectionFailed, "创建SOCKS代理拨号器失败")
		}

		// 通过SOCKS代理连接目标主机，让代理处理DNS解析
		hostWithPort := host
		if !strings.Contains(host, ":") {
			hostWithPort = host + ":80" // 默认HTTP端口
		}

		conn, err := dialer.Dial("tcp", hostWithPort)
		if err != nil {
			return nil, errors.WrapError(err, errors.ErrTypeProxy, errors.ErrCodeProxyConnectionFailed, "通过SOCKS代理连接失败")
		}
		defer conn.Close()

		// 获取远程地址的IP
		remoteAddr := conn.RemoteAddr().String()
		ipStr, _, err := net.SplitHostPort(remoteAddr)
		if err != nil {
			return nil, errors.WrapError(err, errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "解析远程地址失败")
		}

		ip := net.ParseIP(ipStr)
		if ip == nil {
			return nil, errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "无效的IP地址", fmt.Sprintf("IP: %s", ipStr))
		}

		// 缓存结果
		cacheTTL := defaultDNSCacheTTL
		if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
			if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
				cacheTTL = customTTL
				serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
			}
		}

		dnsCacheLock.Lock()
		dnsCache[host] = &CachedDNSResult{
			IPs:      []net.IP{ip},
			ExpireAt: time.Now().Add(cacheTTL),
		}
		dnsCacheLock.Unlock()

		serverLogger.GetRawLogger().Debugf("DNS结果已缓存: %s, 过期时间: %s", host, time.Now().Add(cacheTTL).Format(time.RFC3339))

		return []net.IP{ip}, nil

	default:
		return nil, errors.NewErrorWithDetails(errors.ErrTypeProxy, errors.ErrCodeProxyTypeUnsupported, "不支持的代理类型", fmt.Sprintf("类型: %s", proxyURL.Scheme))
	}
}

// 从错误信息中提取IP地址
func extractIPFromError(err error) string {
	if err == nil {
		return ""
	}

	// 尝试匹配错误信息中的IP地址
	errStr := err.Error()
	ipRegex := regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`)
	matches := ipRegex.FindStringSubmatch(errStr)
	if len(matches) > 0 {
		return matches[0]
	}

	return ""
}

// LookupAddr 实现远程DNS反向解析
func (r *RemoteDNSResolver) LookupAddr(ctx context.Context, addr string) ([]string, error) {
	// 如果没有设置代理地址，返回错误
	if r.ProxyAddr == "" {
		return nil, errors.NewError(errors.ErrTypeDNS, errors.ErrCodeDNSConfigInvalid, "远程DNS反向解析需要设置代理地址")
	}

	// 如果配置了HTTPProxyDNS，优先使用它进行反向解析
	if r.DNSServer != "" {
		// 创建自定义DNS解析器
		customResolver := NewCustomDNSResolver(r.DNSServer)
		if customResolver != nil && len(customResolver.Servers) > 0 {
			names, err := customResolver.LookupAddr(ctx, addr)
			if err == nil && len(names) > 0 {
				return names, nil
			}
			// 如果自定义DNS解析失败，记录日志并继续使用系统默认解析
			// 记录日志
			serverLogger.GetRawLogger().Debugf("使用HTTPProxyDNS反向解析失败: %v，尝试使用系统默认解析", err)
		} else {
			// 记录日志
			serverLogger.GetRawLogger().Debugf("HTTPProxyDNS配置无效或解析失败: %s", r.DNSServer)
		}
	}

	// 对于远程DNS解析，我们可以回退到系统默认的反向DNS解析
	return net.DefaultResolver.LookupAddr(ctx, addr)
}

// LookupAddr 实现本地反向DNS解析
func (r *LocalDNSResolver) LookupAddr(ctx context.Context, addr string) ([]string, error) {
	// 使用系统默认解析器进行本地反向解析，增加超时控制
	timeoutCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()
	return net.DefaultResolver.LookupAddr(timeoutCtx, addr)
}

// CustomDNSResolver 自定义DNS解析器
type CustomDNSResolver struct {
	Servers  []DNSServerConfig // 支持多个DNS服务器配置
	CacheTTL time.Duration     // 缓存生存时间
	NoCache  bool              // 是否禁用缓存
}

// 扩展DNSServerConfig结构，基于common.DNSServerConfig
type DNSServerConfig struct {
	Address  string        // 服务器地址
	Protocol string        // 协议类型：udp, tcp, tls, https, doh
	URL      string        // DoH URL (仅用于doh协议)
	Timeout  time.Duration // 解析超时时间
	Priority int           // 服务器优先级
	Tags     []string      // 服务器标签，用于按域名选择
}

// NewCustomDNSResolver 创建自定义DNS解析器
// 支持通过配置文件设置CacheTTL和NoCache参数
func NewCustomDNSResolver(serverConfig string) *CustomDNSResolver {
	servers := parseDNSServerConfig(serverConfig)
	return &CustomDNSResolver{
		Servers:  servers,
		CacheTTL: defaultDNSCacheTTL,
		NoCache:  false,
	}
}

// parseDNSServerConfig 解析DNS服务器配置字符串
// 格式：server1:port1@protocol1|server2:port2@protocol2|https://doh.server/dns-query@doh
func parseDNSServerConfig(config string) []DNSServerConfig {
	var servers []DNSServerConfig

	// 分割多个服务器配置
	serverConfigs := strings.Split(config, "|")
	for _, serverConfig := range serverConfigs {
		serverConfig = strings.TrimSpace(serverConfig)
		if serverConfig == "" {
			continue
		}

		// 解析协议
		protocol := "udp" // 默认协议
		address := serverConfig
		url := ""
		timeout := getDefaultDNSTimeout(context.Background()) // 默认超时时间
		priority := 0                                         // 默认优先级
		var tags []string                                     // 默认无标签

		if strings.Contains(serverConfig, "@") {
			parts := strings.Split(serverConfig, "@")
			if len(parts) >= 2 {
				address = parts[0]

				// 检查协议部分是否包含额外参数
				protocolParts := strings.Split(parts[1], ",")
				protocol = strings.ToLower(protocolParts[0])

				// 解析额外参数
				for i := 1; i < len(protocolParts); i++ {
					param := strings.TrimSpace(protocolParts[i])

					if strings.HasPrefix(param, "timeout=") {
						timeoutStr := strings.TrimPrefix(param, "timeout=")
						timeoutMs, err := strconv.Atoi(timeoutStr)
						if err == nil && timeoutMs > 0 {
							timeout = time.Duration(timeoutMs) * time.Millisecond
						}
					} else if strings.HasPrefix(param, "priority=") {
						priorityStr := strings.TrimPrefix(param, "priority=")
						pri, err := strconv.Atoi(priorityStr)
						if err == nil {
							priority = pri
						}
					} else if strings.HasPrefix(param, "tags=") {
						tagsStr := strings.TrimPrefix(param, "tags=")
						tags = strings.Split(tagsStr, "+")
						// 清理每个标签
						for j := range tags {
							tags[j] = strings.TrimSpace(tags[j])
						}
					}
				}
			}
		}

		// 处理DoH协议
		if protocol == "doh" || strings.HasPrefix(address, "https://") {
			protocol = "doh"
			url = address
			// 如果URL不包含路径，添加默认路径
			if !strings.Contains(url, "/dns-query") && !strings.HasSuffix(url, "/") {
				url += "/dns-query"
			}
		}

		servers = append(servers, DNSServerConfig{
			Address:  address,
			Protocol: protocol,
			URL:      url,
			Timeout:  timeout,
			Priority: priority,
			Tags:     tags,
		})
	}

	// 根据优先级排序
	sort.Slice(servers, func(i, j int) bool {
		return servers[i].Priority < servers[j].Priority
	})

	return servers
}

// LookupIP 实现自定义DNS解析
// 全局DNS缓存存储
var (
	dnsCache     = make(map[string]*CachedDNSResult)
	dnsCacheLock sync.RWMutex
	// 默认缓存时间为60秒
	defaultDNSCacheTTL = 60 * time.Second
)

// getDefaultDNSTimeout 获取默认DNS超时时间
// 首先尝试从上下文中获取，如果没有则从全局配置中获取，如果全局配置也没有则使用默认值5秒
func getDefaultDNSTimeout(ctx context.Context) time.Duration {
	// 从上下文中获取自定义超时时间
	if ctxVal := ctx.Value("dns_timeout"); ctxVal != nil {
		if customTimeout, ok := ctxVal.(time.Duration); ok && customTimeout > 0 {
			return customTimeout
		}
	}

	// 从全局配置中获取默认超时时间
	for _, p := range []*Proxy{handler} {
		if p != nil && p.Options.RuleConfig != nil && p.Options.RuleConfig.Global.DefaultDNSTimeout > 0 {
			return time.Duration(p.Options.RuleConfig.Global.DefaultDNSTimeout) * time.Millisecond
		}
	}

	// 默认超时时间（5秒）
	return 5 * time.Second
}

// sortIPsByVersionPriority 根据IP版本优先级对IP地址进行排序
func sortIPsByVersionPriority(ips []net.IP, priority string) []net.IP {
	if len(ips) <= 1 || priority == "" {
		return ips
	}

	// 创建IPv4和IPv6地址的分组
	var ipv4s, ipv6s []net.IP
	for _, ip := range ips {
		if ip.To4() != nil {
			ipv4s = append(ipv4s, ip)
		} else {
			ipv6s = append(ipv6s, ip)
		}
	}

	// 根据优先级合并结果
	var result []net.IP
	if priority == "ipv4" {
		result = append(ipv4s, ipv6s...)
		if len(ips) > 0 {
			serverLogger.GetRawLogger().Debugf("根据IPv4优先级排序IP地址: %v", formatIPsForLog(result))
		}
	} else if priority == "ipv6" {
		result = append(ipv6s, ipv4s...)
		if len(ips) > 0 {
			serverLogger.GetRawLogger().Debugf("根据IPv6优先级排序IP地址: %v", formatIPsForLog(result))
		}
	} else {
		// 默认不改变顺序
		result = ips
	}

	return result
}

// formatIPsForLog 格式化IP地址列表用于日志输出
func formatIPsForLog(ips []net.IP) []string {
	ipStrings := make([]string, 0, len(ips))
	for _, ip := range ips {
		ipStrings = append(ipStrings, ip.String())
	}
	return ipStrings
}

// CachedDNSResult 存储缓存的DNS解析结果
type CachedDNSResult struct {
	IPs      []net.IP
	ExpireAt time.Time
}

func (r *CustomDNSResolver) LookupIP(ctx context.Context, host string) ([]net.IP, error) {
	// 检查是否是本地IP地址，如果是则直接返回，避免不必要的DNS查询
	if ip := net.ParseIP(host); ip != nil {
		serverLogger.GetRawLogger().Debugf("主机名 %s 已经是IP地址，跳过DNS解析", host)
		return []net.IP{ip}, nil
	}

	startTime := time.Now()
	defer func() {
		elapsed := time.Since(startTime)
		// 记录解析时间
		// 记录日志
		serverLogger.GetRawLogger().Debugf("DNS解析耗时: %s 用于主机: %s", elapsed, host)
	}()

	// 从上下文中获取IP版本优先级
	ipVersionPriority := ""
	if ctxVal := ctx.Value("ip_version_priority"); ctxVal != nil {
		if priority, ok := ctxVal.(string); ok && (priority == "ipv4" || priority == "ipv6") {
			ipVersionPriority = priority
			serverLogger.GetRawLogger().Debugf("从上下文获取IP版本优先级: %s", ipVersionPriority)
		}
	}

	// 如果没有配置服务器，使用系统默认解析器
	if len(r.Servers) == 0 {
		// 记录日志
		serverLogger.GetRawLogger().Debugf("未配置自定义DNS服务器，使用系统默认解析器")
		ips, err := net.DefaultResolver.LookupIP(ctx, "ip", host)
		if err != nil {
			return nil, err
		}
		// 应用IP版本优先级排序
		return sortIPsByVersionPriority(ips, ipVersionPriority), nil
	}

	// 检查是否禁用缓存或是否有强制刷新标记
	forceRefresh := false
	if ctxVal := ctx.Value("dns_force_refresh"); ctxVal != nil {
		if forceRefreshVal, ok := ctxVal.(bool); ok && forceRefreshVal {
			forceRefresh = true
			serverLogger.GetRawLogger().Debugf("强制刷新DNS缓存: %s", host)
		}
	}

	// 如果启用了缓存且不是强制刷新，尝试从缓存获取结果
	if !r.NoCache && !forceRefresh {
		dnsCacheLock.RLock()
		cached, exists := dnsCache[host]
		dnsCacheLock.RUnlock()

		// 如果有有效缓存，直接返回
		if exists && time.Now().Before(cached.ExpireAt) {
			serverLogger.GetRawLogger().Debugf("使用DNS缓存结果: %s, IP数量: %d", host, len(cached.IPs))
			// 应用IP版本优先级排序
			return sortIPsByVersionPriority(cached.IPs, ipVersionPriority), nil
		} else if exists {
			serverLogger.GetRawLogger().Debugf("DNS缓存已过期: %s", host)
		}
	}

	// 尝试使用配置的每个服务器解析
	var lastErr error
	var errorMessages []string
	var allIPs []net.IP

	// 按优先级排序服务器
	sort.Slice(r.Servers, func(i, j int) bool {
		return r.Servers[i].Priority < r.Servers[j].Priority
	})

	for _, server := range r.Servers {
		// 记录日志
		serverLogger.GetRawLogger().Debugf("尝试使用DNS服务器 %s@%s 解析 %s", server.Address, server.Protocol, host)
		var ips []net.IP
		var err error

		// 创建带超时的上下文
		timeoutCtx, cancel := context.WithTimeout(ctx, server.Timeout)
		defer cancel()

		switch server.Protocol {
		case "udp", "tcp":
			// 使用标准DNS解析
			resolver := &net.Resolver{
				PreferGo: true,
				Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
					d := net.Dialer{Timeout: server.Timeout}
					return d.DialContext(ctx, server.Protocol, server.Address)
				},
			}
			ips, err = resolver.LookupIP(timeoutCtx, "ip", host)

		case "tls":
			// 使用DNS-over-TLS
			ips, err = resolveDNSOverTLS(ctx, host, server.Address, server.Timeout)

		case "https", "doh":
			// 使用DNS-over-HTTPS
			serverURL := server.URL
			if serverURL == "" {
				serverURL = "https://" + server.Address + "/dns-query"
			}
			ips, err = resolveDNSOverHTTPS(timeoutCtx, host, serverURL, server.Timeout)

		default:
			err = errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSProtocolUnsupported, "不支持的DNS协议", fmt.Sprintf("协议: %s", server.Protocol))
		}

		if err != nil {
			lastErr = err
			errorMessages = append(errorMessages, fmt.Sprintf("%s@%s: %v", server.Address, server.Protocol, err))
			continue
		}

		if len(ips) > 0 {
			// 找到结果后立即返回，不再查询其他服务器
			ipStrings := make([]string, 0, len(ips))
			for _, ip := range ips {
				ipStrings = append(ipStrings, ip.String())
			}
			serverLogger.GetRawLogger().Debugf("DNS解析 %s 成功，找到 %d 个IP地址: %v", host, len(ips), ipStrings)

			// 如果启用了缓存，将结果存入缓存
			if !r.NoCache {
				cacheTTL := r.CacheTTL
				if cacheTTL <= 0 {
					cacheTTL = defaultDNSCacheTTL
				}

				// 从上下文中获取自定义TTL
				if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
					if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
						cacheTTL = customTTL
						serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
					}
				}

				dnsCacheLock.Lock()
				dnsCache[host] = &CachedDNSResult{
					IPs:      ips,
					ExpireAt: time.Now().Add(cacheTTL),
				}
				dnsCacheLock.Unlock()

				serverLogger.GetRawLogger().Debugf("DNS结果已缓存: %s, 过期时间: %s", host, time.Now().Add(cacheTTL).Format(time.RFC3339))
			}

			// 应用IP版本优先级排序
			return sortIPsByVersionPriority(ips, ipVersionPriority), nil
		}
	}

	// 如果收集了一些IP，记录日志并返回它们
	if len(allIPs) > 0 {
		ipStrings := make([]string, 0, len(allIPs))
	for _, ip := range allIPs {
		ipStrings = append(ipStrings, ip.String())
	}
	serverLogger.GetRawLogger().Debugf("DNS解析 %s 成功，找到 %d 个IP地址: %v", host, len(allIPs), ipStrings)
		return allIPs, nil
	}

	// 所有服务器都失败，尝试回退到系统默认解析器
	serverLogger.GetRawLogger().Warnf("所有自定义DNS服务器解析失败: %v，尝试回退到系统默认解析器", lastErr)

	// 获取默认DNS超时时间
	timeout := getDefaultDNSTimeout(ctx)

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 使用系统默认解析器作为最后的回退选项
	defaultIPs, defaultErr := net.DefaultResolver.LookupIP(timeoutCtx, "ip", host)
	if defaultErr != nil {
		// 如果系统默认解析器也失败，返回所有错误信息
		serverLogger.GetRawLogger().Errorf("系统默认DNS解析器也失败: %v", defaultErr)
		return nil, errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "DNS解析失败: 所有DNS服务器都无法解析", fmt.Sprintf("主机: %s, 自定义DNS错误: %v, 系统DNS错误: %v", host, lastErr, defaultErr))
	}

	// 系统默认解析器成功
	ipStrings := make([]string, 0, len(defaultIPs))
	for _, ip := range defaultIPs {
		ipStrings = append(ipStrings, ip.String())
	}
	serverLogger.GetRawLogger().Infof("回退到系统默认DNS解析器成功: %s, 找到 %d 个IP地址: %v", host, len(defaultIPs), ipStrings)

	// 如果启用了缓存，将结果存入缓存
	if !r.NoCache {
		cacheTTL := r.CacheTTL
		if cacheTTL <= 0 {
			cacheTTL = defaultDNSCacheTTL
		}

		// 从上下文中获取自定义TTL
		if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
			if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
				cacheTTL = customTTL
				serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
			}
		}

		dnsCacheLock.Lock()
		dnsCache[host] = &CachedDNSResult{
			IPs:      defaultIPs,
			ExpireAt: time.Now().Add(cacheTTL),
		}
		dnsCacheLock.Unlock()

		serverLogger.GetRawLogger().Debugf("系统默认DNS解析器结果已缓存: %s, 过期时间: %s", host, time.Now().Add(cacheTTL).Format(time.RFC3339))
	}

	// 应用IP版本优先级排序
	return sortIPsByVersionPriority(defaultIPs, ipVersionPriority), nil
}

func resolveDNSOverTLS(ctx context.Context, host, serverAddr string, timeout time.Duration) ([]net.IP, error) {
	if !strings.Contains(serverAddr, ":") {
		serverAddr += ":853"
	}
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true, // 跳过证书验证
	}
	client := new(dns.Client)
	client.Net = "tcp-tls"
	client.TLSConfig = tlsConfig
	client.Timeout = timeout
	// 或如下分开定制
	// client.ReadTimeout = timeout
	// client.WriteTimeout = timeout
	var results []net.IP
	var lastErr error

	// 查询IPv4地址 (A记录)
	m4 := new(dns.Msg)
	m4.SetQuestion(dns.Fqdn(host), dns.TypeA)
	resp4, _, err4 := client.ExchangeContext(ctx, m4, serverAddr)
	if err4 != nil {
		lastErr = err4
		serverLogger.GetRawLogger().Debugf("DoT IPv4解析失败: %s, err: %v", host, err4)
	} else if resp4 != nil && resp4.Rcode == dns.RcodeSuccess {
		for _, answer := range resp4.Answer {
			if a, ok := answer.(*dns.A); ok {
				results = append(results, a.A)
			}
		}
	}

	// 查询IPv6地址 (AAAA记录)
	m6 := new(dns.Msg)
	m6.SetQuestion(dns.Fqdn(host), dns.TypeAAAA)
	resp6, _, err6 := client.ExchangeContext(ctx, m6, serverAddr)
	if err6 != nil {
		if lastErr == nil {
			lastErr = err6
		}
		serverLogger.GetRawLogger().Debugf("DoT IPv6解析失败: %s, err: %v", host, err6)
	} else if resp6 != nil && resp6.Rcode == dns.RcodeSuccess {
		for _, answer := range resp6.Answer {
			if aaaa, ok := answer.(*dns.AAAA); ok {
				results = append(results, aaaa.AAAA)
			}
		}
	}

	// 记录找到的IP地址
	if len(results) > 0 {
		ipStrings := make([]string, 0, len(results))
		for _, ip := range results {
			ipStrings = append(ipStrings, ip.String())
		}
		serverLogger.GetRawLogger().Debugf("DoT解析 %s 成功，找到 %d 个IP地址: %v", host, len(results), ipStrings)
	}

	if len(results) == 0 {
		return nil, lastErr
	}
	return results, nil
}

// resolveDNSOverHTTPS 使用DNS-over-HTTPS解析域名
func resolveDNSOverHTTPS(ctx context.Context, host, serverURL string, timeout time.Duration) ([]net.IP, error) {
	client := &http.Client{
		Timeout: timeout,
	}

	var allIPs []net.IP
	var lastErr error

	// 查询IPv4地址 (A记录)
	ipv4URL := serverURL
	if strings.Contains(ipv4URL, "?") {
		ipv4URL += "&"
	} else {
		ipv4URL += "?"
	}
	ipv4URL += "name=" + url.QueryEscape(host) + "&type=A"

	// 发送IPv4请求
	req4, err := http.NewRequestWithContext(ctx, "GET", ipv4URL, nil)
	if err != nil {
		lastErr = err
	} else {
		req4.Header.Set("Accept", "application/dns-json")
		resp4, err := client.Do(req4)
		if err != nil {
			lastErr = err
			serverLogger.GetRawLogger().Debugf("DoH IPv4请求失败: %v", err)
		} else {
			defer resp4.Body.Close()
			if resp4.StatusCode == http.StatusOK {
				body4, err := io.ReadAll(resp4.Body)
				if err != nil {
					lastErr = err
				} else {
					// 解析JSON响应
					var result4 struct {
						Status int `json:"Status"`
						Answer []struct {
							Type int    `json:"type"`
							Data string `json:"data"`
						} `json:"Answer"`
					}

					if err := json.Unmarshal(body4, &result4); err != nil {
						lastErr = err
					} else {
						// 提取IPv4地址
						for _, answer := range result4.Answer {
							// Type 1 = A记录
							if answer.Type == 1 {
								ip := net.ParseIP(answer.Data)
								if ip != nil {
									allIPs = append(allIPs, ip)
								}
							}
						}
					}
				}
			} else {
				lastErr = errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "DoH IPv4请求失败", fmt.Sprintf("状态: %s", resp4.Status))
			}
		}
	}

	// 查询IPv6地址 (AAAA记录)
	ipv6URL := serverURL
	if strings.Contains(ipv6URL, "?") {
		ipv6URL += "&"
	} else {
		ipv6URL += "?"
	}
	ipv6URL += "name=" + url.QueryEscape(host) + "&type=AAAA"

	// 发送IPv6请求
	req6, err := http.NewRequestWithContext(ctx, "GET", ipv6URL, nil)
	if err != nil {
		if lastErr == nil {
			lastErr = err
		}
	} else {
		req6.Header.Set("Accept", "application/dns-json")
		resp6, err := client.Do(req6)
		if err != nil {
			if lastErr == nil {
				lastErr = err
			}
			serverLogger.GetRawLogger().Debugf("DoH IPv6请求失败: %v", err)
		} else {
			defer resp6.Body.Close()
			if resp6.StatusCode == http.StatusOK {
				body6, err := io.ReadAll(resp6.Body)
				if err != nil {
					if lastErr == nil {
						lastErr = err
					}
				} else {
					// 解析JSON响应
					var result6 struct {
						Status int `json:"Status"`
						Answer []struct {
							Type int    `json:"type"`
							Data string `json:"data"`
						} `json:"Answer"`
					}

					if err := json.Unmarshal(body6, &result6); err != nil {
						if lastErr == nil {
							lastErr = err
						}
					} else {
						// 提取IPv6地址
						for _, answer := range result6.Answer {
							// Type 28 = AAAA记录
							if answer.Type == 28 {
								ip := net.ParseIP(answer.Data)
								if ip != nil {
									allIPs = append(allIPs, ip)
								}
							}
						}
					}
				}
			} else {
				if lastErr == nil {
					lastErr = errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "DoH IPv6请求失败", fmt.Sprintf("状态: %s", resp6.Status))
				}
			}
		}
	}

	// 记录找到的IP地址
	if len(allIPs) > 0 {
		ipStrings := make([]string, 0, len(allIPs))
		for _, ip := range allIPs {
			ipStrings = append(ipStrings, ip.String())
		}
		serverLogger.GetRawLogger().Debugf("DoH解析 %s 成功，找到 %d 个IP地址: %v", host, len(allIPs), ipStrings)
	}

	if len(allIPs) == 0 {
		return nil, lastErr
	}

	return allIPs, nil
}

// LookupAddr 实现自定义反向DNS解析
// 修改LookupAddr方法，移除回退到系统默认解析器的逻辑，并添加缓存机制
func (r *CustomDNSResolver) LookupAddr(ctx context.Context, addr string) ([]string, error) {
	startTime := time.Now()
	defer func() {
		elapsed := time.Since(startTime)
		// 记录日志
		serverLogger.GetRawLogger().Debugf("反向DNS解析耗时: %s 用于IP: %s", elapsed, addr)
	}()

	// 如果没有配置服务器，使用系统默认解析器
	if len(r.Servers) == 0 {
		// 记录日志
		serverLogger.GetRawLogger().Debugf("未配置自定义DNS服务器，使用系统默认解析器进行反向查询")
		return net.DefaultResolver.LookupAddr(ctx, addr)
	}

	// 生成缓存键
	cacheKey := "reverse:" + addr

	// 检查是否禁用缓存或是否有强制刷新标记
	forceRefresh := false
	if ctxVal := ctx.Value("dns_force_refresh"); ctxVal != nil {
		if forceRefreshVal, ok := ctxVal.(bool); ok && forceRefreshVal {
			forceRefresh = true
			serverLogger.GetRawLogger().Debugf("强制刷新DNS缓存: %s", cacheKey)
		}
	}

	// 如果启用了缓存且不是强制刷新，尝试从缓存获取结果
	if !r.NoCache && !forceRefresh {
		dnsCacheLock.RLock()
		cached, exists := dnsCache[cacheKey]
		dnsCacheLock.RUnlock()

		// 如果有有效缓存，直接返回
		if exists && time.Now().Before(cached.ExpireAt) {
			// 反向查询缓存需要特殊处理，因为缓存的是域名而不是IP
			if len(cached.IPs) > 0 {
				// 对于反向DNS查询，我们在缓存中存储的是域名字符串
				// 由于cached.IPs是[]net.IP类型,不能直接转换为string,需要先转换为字符串
				if len(cached.IPs) > 0 {
					cachedName := cached.IPs[0].String()
					names := []string{cachedName}
					serverLogger.GetRawLogger().Debugf("使用DNS缓存结果: %s, 域名: %v", cacheKey, names)
					return names, nil
				} else if len(cached.IPs) > 0 {
					// 如果缓存中存储的是IP，则转换为字符串返回
					ipStr := cached.IPs[0].String()
					names := []string{ipStr}
					serverLogger.GetRawLogger().Debugf("使用DNS缓存结果(IP): %s, IP字符串: %v", cacheKey, names)
					return names, nil
				}
			}
		} else if exists {
			serverLogger.GetRawLogger().Debugf("DNS缓存已过期: %s", cacheKey)
		}
	}

	// 尝试使用配置的每个服务器进行反向解析
	var lastErr error
	var errorMessages []string
	var allNames []string

	// 按优先级排序服务器
	sort.Slice(r.Servers, func(i, j int) bool {
		return r.Servers[i].Priority < r.Servers[j].Priority
	})

	for _, server := range r.Servers {
		// 记录日志
		serverLogger.GetRawLogger().Debugf("尝试使用DNS服务器 %s@%s 进行反向解析 %s", server.Address, server.Protocol, addr)
		var names []string
		var err error

		// 创建带超时的上下文
		timeoutCtx, cancel := context.WithTimeout(ctx, server.Timeout)
		defer cancel()

		switch server.Protocol {
		case "udp", "tcp":
			// 使用标准DNS解析
			resolver := &net.Resolver{
				PreferGo: true,
				Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
					d := net.Dialer{Timeout: server.Timeout}
					return d.DialContext(ctx, server.Protocol, server.Address)
				},
			}
			names, err = resolver.LookupAddr(timeoutCtx, addr)

		case "tls", "https", "doh":
			// 对于这些协议，我们需要使用DNS库进行PTR查询
			// 使用 github.com/miekg/dns 进行 PTR 查询，支持 TLS/HTTPS/DoH
			ptrName, perr := dns.ReverseAddr(addr)
			if perr != nil {
				err = perr
				break
			}
			msg := new(dns.Msg)
			msg.SetQuestion(ptrName, dns.TypePTR)

			var client *dns.Client
			var target string

			switch server.Protocol {
			case "tls":
				client = &dns.Client{Net: "tcp-tls", Timeout: server.Timeout}
				target = server.Address
			case "https", "doh":
				client = &dns.Client{Net: "https", Timeout: server.Timeout}
				// server.Address 需为完整 DoH URL，如 https://dns.google/dns-query
				target = server.Address
			default:
				err = errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSProtocolUnsupported, "不支持的DNS协议", fmt.Sprintf("协议: %s", server.Protocol))
				break
			}

			var resp *dns.Msg
			if server.Protocol == "https" || server.Protocol == "doh" {
				resp, _, err = client.ExchangeContext(timeoutCtx, msg, target)
			} else {
				resp, _, err = client.ExchangeContext(timeoutCtx, msg, target)
			}
			if err != nil {
				break
			}
			var results []string
			for _, ans := range resp.Answer {
				if ptr, ok := ans.(*dns.PTR); ok {
					results = append(results, ptr.Ptr)
				}
			}
			names = results

		default:
			err = errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSProtocolUnsupported, "不支持的DNS协议", fmt.Sprintf("协议: %s", server.Protocol))
		}

		if err != nil {
			lastErr = err
			errorMessages = append(errorMessages, fmt.Sprintf("%s@%s: %v", server.Address, server.Protocol, err))
			continue
		}

		if len(names) > 0 {
			// 合并结果，避免重复
			for _, name := range names {
				found := false
				for _, existingName := range allNames {
					if name == existingName {
						found = true
						break
					}
				}
				if !found {
					allNames = append(allNames, name)
				}
			}

			// 如果已经找到域名，可以提前返回
			if len(allNames) > 0 {
				// 如果启用了缓存，将结果存入缓存
				if !r.NoCache {
					cacheTTL := r.CacheTTL
					if cacheTTL <= 0 {
						cacheTTL = defaultDNSCacheTTL
					}

					// 从上下文中获取自定义TTL
					if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
						if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
					cacheTTL = customTTL
					serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
				}
					}

					// 为反向查询创建缓存
					dnsCacheLock.Lock()
					// 使用特殊结构存储域名结果
					// 注意：这里我们使用一个特殊的方式来存储域名，因为CachedDNSResult结构设计用于存储IP
					// 在实际项目中，应该扩展CachedDNSResult结构以更好地支持域名缓存
					dnsCache["reverse:"+addr] = &CachedDNSResult{
						// 临时使用IPs字段的第一个元素存储第一个域名
						// 这不是最佳实践，但在当前结构下是一种变通方法
						IPs:      []net.IP{net.ParseIP(allNames[0])},
						ExpireAt: time.Now().Add(cacheTTL),
					}
					dnsCacheLock.Unlock()

					serverLogger.GetRawLogger().Debugf("反向DNS结果已缓存: %s, 过期时间: %s", addr, time.Now().Add(cacheTTL).Format(time.RFC3339))
				}

				return allNames, nil
			}
		}
	}

	// 如果所有服务器都失败，但我们收集了一些域名，返回它们
	if len(allNames) > 0 {
		return allNames, nil
	}

	// 所有服务器都失败，尝试回退到系统默认解析器
	serverLogger.GetRawLogger().Warnf("所有自定义DNS服务器反向解析失败: %v，尝试回退到系统默认解析器", errorMessages)

	// 获取默认DNS超时时间
	timeout := getDefaultDNSTimeout(ctx)

	// 创建带超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 使用系统默认解析器作为最后的回退选项
	defaultNames, defaultErr := net.DefaultResolver.LookupAddr(timeoutCtx, addr)
	if defaultErr != nil {
		// 如果系统默认解析器也失败，返回所有错误信息
		serverLogger.GetRawLogger().Errorf("系统默认DNS解析器反向查询也失败: %v", defaultErr)
		return nil, errors.NewErrorWithDetails(errors.ErrTypeDNS, errors.ErrCodeDNSQueryFailed, "反向DNS解析失败: 所有DNS服务器都无法解析", fmt.Sprintf("地址: %s, 自定义DNS错误: %v, 系统DNS错误: %v", addr, lastErr, defaultErr))
	}

	// 系统默认解析器成功
	serverLogger.GetRawLogger().Infof("回退到系统默认DNS解析器反向查询成功: %s, 找到 %d 个域名: %v", addr, len(defaultNames), defaultNames)

	// 如果启用了缓存，将结果存入缓存
	if !r.NoCache && len(defaultNames) > 0 {
		cacheTTL := r.CacheTTL
		if cacheTTL <= 0 {
			cacheTTL = defaultDNSCacheTTL
		}

		// 从上下文中获取自定义TTL
		if ctxVal := ctx.Value("dns_cache_ttl"); ctxVal != nil {
			if customTTL, ok := ctxVal.(time.Duration); ok && customTTL > 0 {
				cacheTTL = customTTL
				serverLogger.GetRawLogger().Debugf("使用自定义DNS缓存TTL: %s", cacheTTL)
			}
		}

		// 为反向查询创建缓存
		dnsCacheLock.Lock()
		// 使用特殊结构存储域名结果
		// 注意：这里我们使用一个特殊的方式来存储域名，因为CachedDNSResult结构设计用于存储IP
		// 在实际项目中，应该扩展CachedDNSResult结构以更好地支持域名缓存
		dnsCache["reverse:"+addr] = &CachedDNSResult{
			// 临时使用IPs字段的第一个元素存储第一个域名
			// 这不是最佳实践，但在当前结构下是一种变通方法
			IPs:      []net.IP{net.ParseIP(defaultNames[0])},
			ExpireAt: time.Now().Add(cacheTTL),
		}
		dnsCacheLock.Unlock()

		serverLogger.GetRawLogger().Debugf("系统默认DNS解析器反向查询结果已缓存: %s, 过期时间: %s", addr, time.Now().Add(cacheTTL).Format(time.RFC3339))
	}

	return defaultNames, nil
}
