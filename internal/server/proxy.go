package server

import (
	"context"
	"sync"
	"time"

	"github.com/elazarl/goproxy"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common"
	"github.com/flexp/flexp/internal/action"
	"github.com/flexp/flexp/internal/proxygateway"
	"github.com/flexp/flexp/internal/trigger"
)



// Proxy as ServeMux in proxy server handler.
type Proxy struct {
	HTTPProxy          *goproxy.ProxyHttpServer
	Options            *common.Options
	Gateways           map[string]*proxygateway.ProxyGateway
	triggerManager     *trigger.TriggerManager // 触发器管理器
	actionManager      *action.ActionManager   // 动作管理器
	mu                 sync.RWMutex
	dnsResolver        DNSResolver
	reverseDNSResolver *ReverseDNSResolver
	currentProxy       string // 当前使用的代理
	proxyOkCount       int    // 当前代理成功次数
}

// ResetProxyState 重置当前代理状态
func (p *Proxy) ResetProxyState() {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.currentProxy = ""
	p.proxyOkCount = 0
	serverLogger.GetRawLogger().Debugf("Proxy state (currentProxy, proxyOkCount) reset.")
}

func (p *Proxy) Close() {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	p.mu.Lock()
	defer p.mu.Unlock()

	for _, gateway := range p.Gateways {
		if err := gateway.Close(ctx); err != nil {
			wrappedErr := errors.WrapError(err, errors.ErrTypeProxy, errors.ErrCodeProxyShutdownFailed, "无法关闭网关")
			serverLogger.Error(wrappedErr.Error())
		}
	}
}
