package trigger

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"
)

// TestStatsPersistenceManagerCreation 测试持久化管理器创建
func TestStatsPersistenceManagerCreation(t *testing.T) {
	tempDir := t.TempDir()
	
	options := StatsPersistenceOptions{
		DataDir:          tempDir,
		SaveInterval:     1 * time.Minute,
		MaxHistoryDays:   7,
		CompressionLevel: 6,
		EnableAutoSave:   true,
	}
	
	spm := NewStatsPersistenceManager(options)
	
	if spm == nil {
		t.Error("持久化管理器创建失败")
	}
	
	if spm.dataDir != tempDir {
		t.Errorf("期望数据目录为%s，实际为%s", tempDir, spm.dataDir)
	}
	
	if spm.saveInterval != 1*time.Minute {
		t.Errorf("期望保存间隔为1分钟，实际为%v", spm.saveInterval)
	}
	
	// 验证数据目录是否创建
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		t.Error("数据目录未创建")
	}
}

// TestStatsSaveAndLoad 测试统计数据保存和加载
func TestStatsSaveAndLoad(t *testing.T) {
	tempDir := t.TempDir()
	
	spm := NewStatsPersistenceManager(StatsPersistenceOptions{
		DataDir:        tempDir,
		SaveInterval:   1 * time.Minute,
		MaxHistoryDays: 7,
	})
	
	// 创建测试统计数据
	testStats := map[string]*TriggerStats{
		"trigger1": {
			TotalExecutions: 100,
			TotalMatches:    80,
			ErrorCount:      5,
			RetryCount:      2,
			LastExecuted:    time.Now(),
			LastMatched:     time.Now(),
			ExecutionTime:   500 * time.Millisecond,
			AverageExecTime: 5 * time.Millisecond,
			MaxExecTime:     20 * time.Millisecond,
			MinExecTime:     1 * time.Millisecond,
			MatchRate:       0.8,
			ErrorRate:       0.05,
		},
		"trigger2": {
			TotalExecutions: 50,
			TotalMatches:    45,
			ErrorCount:      1,
			RetryCount:      0,
			LastExecuted:    time.Now(),
			LastMatched:     time.Now(),
			ExecutionTime:   200 * time.Millisecond,
			AverageExecTime: 4 * time.Millisecond,
			MaxExecTime:     15 * time.Millisecond,
			MinExecTime:     2 * time.Millisecond,
			MatchRate:       0.9,
			ErrorRate:       0.02,
		},
	}
	
	// 保存统计数据
	err := spm.SaveStats(testStats)
	if err != nil {
		t.Fatalf("保存统计数据失败: %v", err)
	}
	
	// 加载统计数据
	loadedStats, err := spm.LoadStats()
	if err != nil {
		t.Fatalf("加载统计数据失败: %v", err)
	}
	
	// 验证加载的数据
	if len(loadedStats) != len(testStats) {
		t.Errorf("期望加载%d个触发器统计，实际加载%d个", len(testStats), len(loadedStats))
	}
	
	for name, originalStats := range testStats {
		loadedStat, exists := loadedStats[name]
		if !exists {
			t.Errorf("触发器%s的统计数据未加载", name)
			continue
		}
		
		if loadedStat.TotalExecutions != originalStats.TotalExecutions {
			t.Errorf("触发器%s的执行次数不匹配，期望%d，实际%d", 
				name, originalStats.TotalExecutions, loadedStat.TotalExecutions)
		}
		
		if loadedStat.TotalMatches != originalStats.TotalMatches {
			t.Errorf("触发器%s的匹配次数不匹配，期望%d，实际%d", 
				name, originalStats.TotalMatches, loadedStat.TotalMatches)
		}
		
		if loadedStat.ErrorCount != originalStats.ErrorCount {
			t.Errorf("触发器%s的错误次数不匹配，期望%d，实际%d", 
				name, originalStats.ErrorCount, loadedStat.ErrorCount)
		}
	}
}

// TestStatsExportAndImport 测试统计数据导出和导入
func TestStatsExportAndImport(t *testing.T) {
	tempDir := t.TempDir()
	exportPath := filepath.Join(tempDir, "exported_stats.json")
	
	spm := NewStatsPersistenceManager(StatsPersistenceOptions{
		DataDir:        tempDir,
		SaveInterval:   1 * time.Minute,
		MaxHistoryDays: 7,
	})
	
	// 创建测试统计数据
	testStats := map[string]*TriggerStats{
		"export_trigger": {
			TotalExecutions: 200,
			TotalMatches:    180,
			ErrorCount:      10,
			RetryCount:      5,
			LastExecuted:    time.Now(),
			LastMatched:     time.Now(),
			ExecutionTime:   1 * time.Second,
			MatchRate:       0.9,
			ErrorRate:       0.05,
		},
	}
	
	// 导出统计数据
	err := spm.ExportStats(testStats, exportPath)
	if err != nil {
		t.Fatalf("导出统计数据失败: %v", err)
	}
	
	// 验证导出文件存在
	if _, err := os.Stat(exportPath); os.IsNotExist(err) {
		t.Error("导出文件不存在")
	}
	
	// 导入统计数据
	importedStats, err := spm.ImportStats(exportPath)
	if err != nil {
		t.Fatalf("导入统计数据失败: %v", err)
	}
	
	// 验证导入的数据
	if len(importedStats) != len(testStats) {
		t.Errorf("期望导入%d个触发器统计，实际导入%d个", len(testStats), len(importedStats))
	}
	
	for name, originalStats := range testStats {
		importedStat, exists := importedStats[name]
		if !exists {
			t.Errorf("触发器%s的统计数据未导入", name)
			continue
		}
		
		if importedStat.TotalExecutions != originalStats.TotalExecutions {
			t.Errorf("触发器%s的执行次数不匹配，期望%d，实际%d", 
				name, originalStats.TotalExecutions, importedStat.TotalExecutions)
		}
	}
}

// TestStatsCompression 测试压缩功能
func TestStatsCompression(t *testing.T) {
	tempDir := t.TempDir()
	compressedPath := filepath.Join(tempDir, "compressed_stats.json.gz")
	
	spm := NewStatsPersistenceManager(StatsPersistenceOptions{
		DataDir:          tempDir,
		SaveInterval:     1 * time.Minute,
		MaxHistoryDays:   7,
		CompressionLevel: 9, // 最高压缩级别
	})
	
	// 创建大量测试数据
	testStats := make(map[string]*TriggerStats)
	for i := 0; i < 100; i++ {
		triggerName := fmt.Sprintf("trigger_%d", i)
		testStats[triggerName] = &TriggerStats{
			TotalExecutions: i * 10,
			TotalMatches:    i * 8,
			ErrorCount:      i,
			RetryCount:      i / 2,
			LastExecuted:    time.Now(),
			LastMatched:     time.Now(),
			ExecutionTime:   time.Duration(i) * time.Millisecond,
			MatchRate:       0.8,
			ErrorRate:       0.1,
		}
	}
	
	// 导出压缩数据
	err := spm.ExportStats(testStats, compressedPath)
	if err != nil {
		t.Fatalf("导出压缩数据失败: %v", err)
	}
	
	// 验证压缩文件存在
	info, err := os.Stat(compressedPath)
	if err != nil {
		t.Fatalf("压缩文件不存在: %v", err)
	}
	
	// 验证文件大小（压缩后应该比较小）
	if info.Size() == 0 {
		t.Error("压缩文件大小为0")
	}
	
	// 导入压缩数据
	importedStats, err := spm.ImportStats(compressedPath)
	if err != nil {
		t.Fatalf("导入压缩数据失败: %v", err)
	}
	
	// 验证数据完整性
	if len(importedStats) != len(testStats) {
		t.Errorf("期望导入%d个触发器统计，实际导入%d个", len(testStats), len(importedStats))
	}
}

// TestStatsHistoryInfo 测试历史信息获取
func TestStatsHistoryInfo(t *testing.T) {
	tempDir := t.TempDir()
	
	spm := NewStatsPersistenceManager(StatsPersistenceOptions{
		DataDir:        tempDir,
		SaveInterval:   1 * time.Minute,
		MaxHistoryDays: 7,
	})
	
	// 创建多个统计文件
	testStats := map[string]*TriggerStats{
		"history_trigger": {
			TotalExecutions: 100,
			TotalMatches:    90,
			ErrorCount:      5,
		},
	}
	
	// 保存多次以创建历史记录
	for i := 0; i < 3; i++ {
		time.Sleep(10 * time.Millisecond) // 确保时间戳不同
		err := spm.SaveStats(testStats)
		if err != nil {
			t.Fatalf("保存统计数据失败 (第%d次): %v", i+1, err)
		}
	}
	
	// 获取历史信息
	history, err := spm.GetHistoryInfo()
	if err != nil {
		t.Fatalf("获取历史信息失败: %v", err)
	}
	
	if len(history) != 3 {
		t.Errorf("期望3个历史记录，实际%d个", len(history))
	}
	
	// 验证历史记录按时间排序（最新的在前）
	for i := 1; i < len(history); i++ {
		if history[i-1].Date < history[i].Date {
			t.Error("历史记录未按时间降序排列")
		}
	}
}

// TestStatsCleanup 测试数据清理
func TestStatsCleanup(t *testing.T) {
	tempDir := t.TempDir()
	
	spm := NewStatsPersistenceManager(StatsPersistenceOptions{
		DataDir:        tempDir,
		SaveInterval:   1 * time.Minute,
		MaxHistoryDays: 1, // 只保留1天
	})
	
	// 创建测试文件
	testStats := map[string]*TriggerStats{
		"cleanup_trigger": {
			TotalExecutions: 50,
			TotalMatches:    45,
		},
	}
	
	// 保存统计数据
	err := spm.SaveStats(testStats)
	if err != nil {
		t.Fatalf("保存统计数据失败: %v", err)
	}
	
	// 获取保存前的文件数量
	files, _ := filepath.Glob(filepath.Join(tempDir, "stats_*.json.gz"))
	initialCount := len(files)
	
	if initialCount == 0 {
		t.Error("没有创建统计文件")
	}
	
	// 执行清理（由于文件是刚创建的，不会被清理）
	err = spm.CleanupOldData()
	if err != nil {
		t.Fatalf("清理数据失败: %v", err)
	}
	
	// 验证文件仍然存在
	files, _ = filepath.Glob(filepath.Join(tempDir, "stats_*.json.gz"))
	afterCleanupCount := len(files)
	
	if afterCleanupCount != initialCount {
		t.Errorf("清理后文件数量不正确，期望%d，实际%d", initialCount, afterCleanupCount)
	}
}
