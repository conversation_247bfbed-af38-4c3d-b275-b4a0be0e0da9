package trigger

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common" // 引入新的配置类型
	"github.com/mubeng/mubeng/common/constants"
	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/interfaces"
	// "github.com/mubeng/mubeng/pkg/actionparser" // 将不再直接使用
	// "github.com/mubeng/mubeng/pkg/config" // 旧的配置包
)

// 模块级别的日志器
var triggerLogger = logger.GetTriggerLogger()

// LoggerAdapter 适配器，将ModuleLogger适配为LogService接口
type LoggerAdapter struct {
	logger *logger.ModuleLogger
}

func (la *LoggerAdapter) Debug(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.GetRawLogger().Debugf(msg, args...)
	} else {
		la.logger.Debug(msg)
	}
}

func (la *LoggerAdapter) Info(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.GetRawLogger().Infof(msg, args...)
	} else {
		la.logger.Info(msg)
	}
}

func (la *LoggerAdapter) Warn(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.GetRawLogger().Warnf(msg, args...)
	} else {
		la.logger.Warn(msg)
	}
}

func (la *LoggerAdapter) Error(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.GetRawLogger().Errorf(msg, args...)
	} else {
		la.logger.Error(msg)
	}
}

func (la *LoggerAdapter) Fatal(msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.GetRawLogger().Fatalf(msg, args...)
	} else {
		la.logger.GetRawLogger().Fatal(msg)
	}
}

func (la *LoggerAdapter) GetLogger() interface{} {
	return la.logger
}

func (la *LoggerAdapter) WithTraceID(traceID string) interfaces.LogService {
	// 简单实现，返回自身
	return la
}

func (la *LoggerAdapter) WithFields(fields map[string]interface{}) interfaces.LogService {
	// 简单实现，返回自身
	return la
}

func (la *LoggerAdapter) LogError(err error, msg string, args ...interface{}) {
	if len(args) > 0 {
		la.logger.GetRawLogger().Errorf(msg+": %v", append(args, err)...)
	} else {
		la.logger.GetRawLogger().Errorf(msg+": %v", err)
	}
}

// 新的依赖注入架构相关结构

// TriggerDefinition 新的触发器定义（用于依赖注入架构）
type TriggerDefinition struct {
	Name              string                 `json:"name"`
	Type              string                 `json:"type"`
	Enabled           bool                   `json:"enabled"`
	Priority          int                    `json:"priority"`
	Stage             ProcessStage           `json:"stage"`
	Conditions        []ConditionDefinition  `json:"conditions"`
	ConditionRelation string                 `json:"condition_relation"` // AND, OR, NOT - 条件之间的逻辑关系
	Actions           []string               `json:"actions"` // 动作名称列表
	Description       string                 `json:"description"`
	Matcher           Matcher                `json:"-"`
	CreatedAt         time.Time              `json:"created_at"`
	LastTriggered     time.Time              `json:"last_triggered"`
	TriggerCount      int                    `json:"trigger_count"`
	MatchCount        int                    `json:"match_count"`
}

// ConditionDefinition 条件定义
type ConditionDefinition struct {
	Type       string                 `json:"type"`       // url, header, method, ip, time, custom
	Field      string                 `json:"field"`      // 字段名（如header名称）
	Operator   string                 `json:"operator"`   // equals, contains, regex, gt, lt, in
	Value      interface{}            `json:"value"`      // 比较值
	Parameters map[string]interface{} `json:"parameters"` // 额外参数
}

// Matcher 匹配器接口
type Matcher interface {
	// Match 检查请求是否匹配触发器条件
	Match(ctx context.Context, req *http.Request, metadata map[string]interface{}) (bool, error)
	
	// GetType 获取匹配器类型
	GetType() string
	
	// GetDescription 获取匹配器描述
	GetDescription() string
	
	// Validate 验证匹配器配置
	Validate() error
}

// MatchContext 匹配上下文
type MatchContext struct {
	Request   *http.Request
	Metadata  map[string]interface{}
	Timestamp time.Time
	ClientIP  string
	UserAgent string
}

// MatchResult 匹配结果
type MatchResult struct {
	Matched     bool
	TriggerName string
	Actions     []string
	Metadata    map[string]interface{}
	Timestamp   time.Time
}

// 内置匹配器实现

// URLMatcher URL匹配器
type URLMatcher struct {
	Logger     interfaces.LogService
	Conditions []ConditionDefinition
}

func (m *URLMatcher) Match(ctx context.Context, req *http.Request, metadata map[string]interface{}) (bool, error) {
	for _, condition := range m.Conditions {
		if condition.Type != "url" {
			continue
		}
		
		var value string
		switch condition.Field {
		case "path":
			value = req.URL.Path
		case "query":
			value = req.URL.RawQuery
		case "full":
			value = req.URL.String()
		default:
			value = req.URL.String()
		}
		
		matched, err := m.evaluateCondition(condition, value)
		if err != nil {
			return false, err
		}
		if !matched {
			return false, nil
		}
	}
	return true, nil
}

func (m *URLMatcher) evaluateCondition(condition ConditionDefinition, value string) (bool, error) {
	expectedValue, ok := condition.Value.(string)
	if !ok {
		return false, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "条件值必须是字符串")
	}
	
	switch condition.Operator {
	case "equals":
		return value == expectedValue, nil
	case "contains":
		return strings.Contains(value, expectedValue), nil
	case "regex":
		matched, err := regexp.MatchString(expectedValue, value)
		return matched, err
	case "starts_with":
		return strings.HasPrefix(value, expectedValue), nil
	case "ends_with":
		return strings.HasSuffix(value, expectedValue), nil
	default:
		return false, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持的操作符", "操作符: "+condition.Operator)
	}
}

func (m *URLMatcher) GetType() string {
	return "url"
}

func (m *URLMatcher) GetDescription() string {
	return "URL路径和查询参数匹配器"
}

func (m *URLMatcher) Validate() error {
	for _, condition := range m.Conditions {
		if condition.Type != "url" {
			continue
		}
		if condition.Value == nil {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "URL条件值不能为空")
		}
	}
	return nil
}

// HeaderMatcher HTTP头匹配器
type HeaderMatcher struct {
	Logger     interfaces.LogService
	Conditions []ConditionDefinition
}

func (m *HeaderMatcher) Match(ctx context.Context, req *http.Request, metadata map[string]interface{}) (bool, error) {
	for _, condition := range m.Conditions {
		if condition.Type != "header" {
			continue
		}
		
		headerValue := req.Header.Get(condition.Field)
		matched, err := m.evaluateCondition(condition, headerValue)
		if err != nil {
			return false, err
		}
		if !matched {
			return false, nil
		}
	}
	return true, nil
}

func (m *HeaderMatcher) evaluateCondition(condition ConditionDefinition, value string) (bool, error) {
	expectedValue, ok := condition.Value.(string)
	if !ok {
		return false, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "条件值必须是字符串")
	}
	
	switch condition.Operator {
	case "equals":
		return value == expectedValue, nil
	case "contains":
		return strings.Contains(value, expectedValue), nil
	case "regex":
		matched, err := regexp.MatchString(expectedValue, value)
		return matched, err
	case "exists":
		return value != "", nil
	case "not_exists":
		return value == "", nil
	default:
		return false, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持的操作符", "操作符: "+condition.Operator)
	}
}

func (m *HeaderMatcher) GetType() string {
	return "header"
}

func (m *HeaderMatcher) GetDescription() string {
	return "HTTP请求头匹配器"
}

func (m *HeaderMatcher) Validate() error {
	for _, condition := range m.Conditions {
		if condition.Type != "header" {
			continue
		}
		if condition.Field == "" {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "Header条件必须指定字段名")
		}
	}
	return nil
}

// MethodMatcher HTTP方法匹配器
type MethodMatcher struct {
	Logger     interfaces.LogService
	Conditions []ConditionDefinition
}

func (m *MethodMatcher) Match(ctx context.Context, req *http.Request, metadata map[string]interface{}) (bool, error) {
	for _, condition := range m.Conditions {
		if condition.Type != "method" {
			continue
		}
		
		expectedMethod, ok := condition.Value.(string)
		if !ok {
			return false, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "方法条件值必须是字符串")
		}
		
		switch condition.Operator {
		case "equals":
			if req.Method != expectedMethod {
				return false, nil
			}
		case "in":
			methods, ok := condition.Value.([]string)
			if !ok {
				return false, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "in操作符需要字符串数组")
			}
			found := false
			for _, method := range methods {
				if req.Method == method {
					found = true
					break
				}
			}
			if !found {
				return false, nil
			}
		default:
			return false, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持的操作符", "操作符: "+condition.Operator)
		}
	}
	return true, nil
}

func (m *MethodMatcher) GetType() string {
	return "method"
}

func (m *MethodMatcher) GetDescription() string {
	return "HTTP请求方法匹配器"
}

func (m *MethodMatcher) Validate() error {
	for _, condition := range m.Conditions {
		if condition.Type != "method" {
			continue
		}
		if condition.Value == nil {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "方法条件值不能为空")
		}
	}
	return nil
}

// IPMatcher IP地址匹配器
type IPMatcher struct {
	Logger     interfaces.LogService
	Conditions []ConditionDefinition
}

func (m *IPMatcher) Match(ctx context.Context, req *http.Request, metadata map[string]interface{}) (bool, error) {
	clientIP := m.getClientIP(req)
	
	for _, condition := range m.Conditions {
		if condition.Type != "ip" {
			continue
		}
		
		expectedIP, ok := condition.Value.(string)
		if !ok {
			return false, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "IP条件值必须是字符串")
		}
		
		switch condition.Operator {
		case "equals":
			if clientIP != expectedIP {
				return false, nil
			}
		case "contains":
			if !strings.Contains(clientIP, expectedIP) {
				return false, nil
			}
		case "regex":
			matched, err := regexp.MatchString(expectedIP, clientIP)
			if err != nil {
				return false, err
			}
			if !matched {
				return false, nil
			}
		default:
			return false, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持的操作符", "operator: "+condition.Operator)
		}
	}
	return true, nil
}

func (m *IPMatcher) getClientIP(req *http.Request) string {
	// 尝试从各种头部获取真实IP
	if ip := req.Header.Get("X-Forwarded-For"); ip != "" {
		return strings.Split(ip, ",")[0]
	}
	if ip := req.Header.Get("X-Real-IP"); ip != "" {
		return ip
	}
	return req.RemoteAddr
}

func (m *IPMatcher) GetType() string {
	return "ip"
}

func (m *IPMatcher) GetDescription() string {
	return "客户端IP地址匹配器"
}

func (m *IPMatcher) Validate() error {
	for _, condition := range m.Conditions {
		if condition.Type != "ip" {
			continue
		}
		if condition.Value == nil {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "IP条件值不能为空")
		}
	}
	return nil
}

// TimeMatcher 时间匹配器
type TimeMatcher struct {
	Logger     interfaces.LogService
	Conditions []ConditionDefinition
}

func (m *TimeMatcher) Match(ctx context.Context, req *http.Request, metadata map[string]interface{}) (bool, error) {
	now := time.Now()
	
	for _, condition := range m.Conditions {
		if condition.Type != "time" {
			continue
		}
		
		switch condition.Field {
		case "hour":
			hour, ok := condition.Value.(float64)
			if !ok {
				return false, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "小时条件值必须是数字")
			}
			if !m.compareTime(condition.Operator, float64(now.Hour()), hour) {
				return false, nil
			}
		case "weekday":
			weekday, ok := condition.Value.(float64)
			if !ok {
				return false, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "星期条件值必须是数字")
			}
			if !m.compareTime(condition.Operator, float64(now.Weekday()), weekday) {
				return false, nil
			}
		default:
			return false, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "不支持的时间字段", "字段: "+condition.Field)
		}
	}
	return true, nil
}

func (m *TimeMatcher) compareTime(operator string, actual, expected float64) bool {
	switch operator {
	case "equals":
		return actual == expected
	case "gt":
		return actual > expected
	case "lt":
		return actual < expected
	case "gte":
		return actual >= expected
	case "lte":
		return actual <= expected
	default:
		return false
	}
}

func (m *TimeMatcher) GetType() string {
	return "time"
}

func (m *TimeMatcher) GetDescription() string {
	return "时间条件匹配器"
}

func (m *TimeMatcher) Validate() error {
	for _, condition := range m.Conditions {
		if condition.Type != "time" {
			continue
		}
		if condition.Field == "" {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "时间条件必须指定字段名")
		}
		if condition.Value == nil {
			return errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "时间条件值不能为空")
		}
	}
	return nil
}

// CompositeMatcher 复合匹配器（组合多个匹配器）
type CompositeMatcher struct {
	Logger   interfaces.LogService
	Matchers []Matcher
	Logic    string // "and" 或 "or"
}

func (m *CompositeMatcher) Match(ctx context.Context, req *http.Request, metadata map[string]interface{}) (bool, error) {
	if len(m.Matchers) == 0 {
		return true, nil
	}
	
	if m.Logic == "or" {
		// OR逻辑：任一匹配器匹配即可
		for _, matcher := range m.Matchers {
			matched, err := matcher.Match(ctx, req, metadata)
			if err != nil {
				return false, err
			}
			if matched {
				return true, nil
			}
		}
		return false, nil
	} else {
		// AND逻辑（默认）：所有匹配器都必须匹配
		for _, matcher := range m.Matchers {
			matched, err := matcher.Match(ctx, req, metadata)
			if err != nil {
				return false, err
			}
			if !matched {
				return false, nil
			}
		}
		return true, nil
	}
}

func (m *CompositeMatcher) GetType() string {
	return "composite"
}

func (m *CompositeMatcher) GetDescription() string {
	return "复合条件匹配器"
}

func (m *CompositeMatcher) Validate() error {
	for _, matcher := range m.Matchers {
		if err := matcher.Validate(); err != nil {
			return err
		}
	}
	return nil
}

// ProcessStage 定义处理阶段
type ProcessStage string

// 使用 constants 包中的常量
const (
	PreRequest ProcessStage = constants.PreRequest
	PostHeader ProcessStage = constants.PostHeader
	PostBody   ProcessStage = constants.PostBody
)

// Trigger 定义触发器接口
type Trigger interface {
	Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool
	GetPriority() int
	GetProcessStage() ProcessStage
	GetActions() []common.ActionConfig
	// GetMatchedActions 获取在特定上下文中匹配的动作（统一接口）
	GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig
}

// ConditionalTrigger 条件触发器（旧架构，保留兼容性）
type ConditionalTrigger struct {
	Conditions   []ConditionMatcher
	Priority     int
	ProcessStage ProcessStage
	EventName    string
	Config       *common.Config // 用于获取动作序列
}

// EnhancedConditionalTrigger 增强条件触发器（新架构）
type EnhancedConditionalTrigger struct {
	Priority     int
	ProcessStage ProcessStage
	EventName    string
	Conditions   map[string]*EnhancedCondition // 条件名称到条件的映射
	Matches      []*EnhancedMatch              // 匹配规则列表
	Config       *common.Config
}

// EnhancedCondition 增强条件
type EnhancedCondition struct {
	Name    string
	Enable  bool
	Matcher func(req *http.Request, resp *http.Response, reqTime time.Duration) bool
}

// EnhancedMatch 增强匹配规则
type EnhancedMatch struct {
	Name       string
	Enable     bool
	Conditions []string
	SubMatches []*EnhancedMatch
	Logic      string
	Actions    []common.ActionConfig
	Matcher    func(conditionResults map[string]bool) bool
}

// ConditionMatcher 条件匹配器
type ConditionMatcher struct {
	Name           string
	Enable         bool
	Matcher        func(req *http.Request, resp *http.Response, reqTime time.Duration) bool
	ActionSequenceName string
}

// Match 检查所有条件，返回匹配的条件列表
func (t *ConditionalTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	// 检查是否有任何条件匹配
	for _, condition := range t.Conditions {
		if condition.Enable && condition.Matcher(req, resp, reqTime) {
			return true
		}
	}
	return false
}

// GetMatchedConditions 获取所有匹配的条件
func (t *ConditionalTrigger) GetMatchedConditions(req *http.Request, resp *http.Response, reqTime time.Duration) []ConditionMatcher {
	var matched []ConditionMatcher
	for _, condition := range t.Conditions {
		if condition.Enable && condition.Matcher(req, resp, reqTime) {
			matched = append(matched, condition)
		}
	}
	return matched
}

// EnhancedConditionalTrigger 实现 Trigger 接口
func (ect *EnhancedConditionalTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	// 评估所有条件
	conditionResults := make(map[string]bool)
	for name, condition := range ect.Conditions {
		if condition.Enable {
			conditionResults[name] = condition.Matcher(req, resp, reqTime)
		} else {
			conditionResults[name] = false
		}
	}

	// 检查是否有任何匹配规则满足
	for _, match := range ect.Matches {
		if match.Enable {
			var matched bool
			if match.Matcher != nil {
				matched = match.Matcher(conditionResults)
			} else {
				matched = ect.evaluateLogicExpression(match.Logic, conditionResults)
			}
			if matched {
				return true
			}
		}
	}
	return false
}

func (ect *EnhancedConditionalTrigger) GetPriority() int {
	return ect.Priority
}

func (ect *EnhancedConditionalTrigger) GetProcessStage() ProcessStage {
	return ect.ProcessStage
}

func (ect *EnhancedConditionalTrigger) GetActions() []common.ActionConfig {
	// 为了接口一致性，返回所有可能的动作（用于静态分析）
	var allActions []common.ActionConfig
	for _, match := range ect.Matches {
		allActions = append(allActions, match.Actions...)
	}
	return allActions
}

// GetMatchedActions 获取所有匹配的动作（新方法）
func (ect *EnhancedConditionalTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	// 评估所有条件
	conditionResults := make(map[string]bool)
	for name, condition := range ect.Conditions {
		if condition.Enable {
			conditionResults[name] = condition.Matcher(req, resp, reqTime)
		} else {
			conditionResults[name] = false
		}
	}

	// 收集所有匹配的动作
	var allActions []common.ActionConfig
	for _, match := range ect.Matches {
		if match.Enable {
			actions := ect.collectMatchedActions(match, conditionResults)
			allActions = append(allActions, actions...)
		}
	}
	return allActions
}

// collectMatchedActions 递归收集匹配的动作
func (ect *EnhancedConditionalTrigger) collectMatchedActions(match *EnhancedMatch, conditionResults map[string]bool) []common.ActionConfig {
	var actions []common.ActionConfig

	// 检查匹配规则是否有效
	if match == nil {
		return actions
	}

	// 如果当前匹配规则满足条件
	var matched bool
	if match.Matcher != nil {
		matched = match.Matcher(conditionResults)
	} else {
		// 如果没有自定义匹配器，使用简单的逻辑表达式评估
		matched = ect.evaluateLogicExpression(match.Logic, conditionResults)
	}

	if matched {
		// 添加当前匹配规则的动作
		actions = append(actions, match.Actions...)

		// 递归检查子匹配规则
		for _, subMatch := range match.SubMatches {
			if subMatch.Enable {
				subActions := ect.collectMatchedActions(subMatch, conditionResults)
				actions = append(actions, subActions...)
			}
		}
	}

	return actions
}

// evaluateLogicExpression 简单的逻辑表达式评估
func (ect *EnhancedConditionalTrigger) evaluateLogicExpression(logic string, conditionResults map[string]bool) bool {
	if logic == "" {
		return true
	}

	// 简单实现：直接查找条件名称
	if result, exists := conditionResults[logic]; exists {
		return result
	}

	// 如果找不到对应的条件，返回false
	return false
}

// GetPriority 获取优先级
func (t *ConditionalTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *ConditionalTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表（返回第一个匹配条件的动作，实际使用时应该调用GetMatchedConditions）
func (t *ConditionalTrigger) GetActions() []common.ActionConfig {
	// 这个方法保持接口兼容性，实际处理时应该使用GetMatchedConditions
	return nil
}

// GetMatchedActions 获取匹配的动作（统一接口实现）
func (t *ConditionalTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	var allActions []common.ActionConfig
	matchedConditions := t.GetMatchedConditions(req, resp, reqTime)

	for _, condition := range matchedConditions {
		if actionSeq, ok := t.Config.Actions[condition.ActionSequenceName]; ok {
			allActions = append(allActions, actionSeq.Sequence...)
		}
	}

	return allActions
}

// StatusTrigger 状态码触发器
type StatusTrigger struct {
	Codes  []int
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断状态码是否匹配
func (t *StatusTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if resp == nil {
		return false
	}
	for _, code := range t.Codes {
		if resp.StatusCode == code {
			return true
		}
	}
	return false
}

// GetPriority 获取优先级
func (t *StatusTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *StatusTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *StatusTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作（对于简单触发器，与GetActions相同）
func (t *StatusTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// BodyTrigger 响应内容触发器
type BodyTrigger struct {
	Patterns     []*regexp.Regexp
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断响应内容是否匹配
func (t *BodyTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if resp == nil {
		triggerLogger.Debug("响应为空，跳过响应体触发器匹配")
		return false
	}
	if resp.Body == nil {
		triggerLogger.Debug("响应体为空，跳过响应体触发器匹配")
		return false
	}

	var buf bytes.Buffer
	tee := io.TeeReader(resp.Body, &buf)
	rawBodyBytes, err := io.ReadAll(tee)
	if err != nil {
		triggerLogger.Error(fmt.Sprintf("读取原始响应体失败: %v", err))
		resp.Body = io.NopCloser(bytes.NewReader(rawBodyBytes))
		return false
	}
	resp.Body = io.NopCloser(bytes.NewReader(rawBodyBytes))

	bodyBytesToMatch := rawBodyBytes
	contentEncodingFromHeader := resp.Header.Get("Content-Encoding")

	if strings.ToLower(contentEncodingFromHeader) == "gzip" {
		gzipReader, err := gzip.NewReader(bytes.NewReader(rawBodyBytes))
		if err != nil {
			triggerLogger.Error(fmt.Sprintf("创建gzip读取器失败: %v", err))
		} else {
			defer gzipReader.Close()
			decompressedBytes, err := io.ReadAll(gzipReader)
			if err != nil {
				triggerLogger.Error(fmt.Sprintf("解压gzip响应体失败: %v", err))
			} else {
				bodyBytesToMatch = decompressedBytes
			}
		}
	}

	if len(bodyBytesToMatch) == 0 {
		return false
	}

	contentTypeFromHeader := resp.Header.Get("Content-Type")
	isText := true
	if contentTypeFromHeader != "" {
		isText = strings.HasPrefix(contentTypeFromHeader, "text/") ||
			strings.Contains(contentTypeFromHeader, "json") ||
			strings.Contains(contentTypeFromHeader, "xml") ||
			strings.Contains(contentTypeFromHeader, "javascript")
	}

	if !isText {
		for _, pattern := range t.Patterns {
			if pattern.Match(bodyBytesToMatch) {
				return true
			}
		}
	} else {
		bodyStr := string(bodyBytesToMatch)
		for _, pattern := range t.Patterns {
			if pattern.MatchString(bodyStr) {
				return true
			}
		}
	}
	return false
}

// GetPriority 获取优先级
func (t *BodyTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *BodyTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *BodyTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *BodyTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// MaxRequestTimeTrigger 请求时间长于触发器
type MaxRequestTimeTrigger struct {
	MaxTime      int // 毫秒
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断请求时间是否超过最大值
func (t *MaxRequestTimeTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	return reqTime.Milliseconds() > int64(t.MaxTime)
}

// GetPriority 获取优先级
func (t *MaxRequestTimeTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *MaxRequestTimeTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *MaxRequestTimeTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *MaxRequestTimeTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// ConnTimeOutTrigger 连接超时触发器
type ConnTimeOutTrigger struct {
	TimeOut      int // 毫秒
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断是否连接超时
func (t *ConnTimeOutTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	return reqTime.Milliseconds() >= int64(t.TimeOut) && resp == nil
}

// GetPriority 获取优先级
func (t *ConnTimeOutTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *ConnTimeOutTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *ConnTimeOutTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *ConnTimeOutTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// MinRequestTimeTrigger 请求时间短于触发器
type MinRequestTimeTrigger struct {
	MinTime      int // 毫秒
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断请求时间是否小于最小值
func (t *MinRequestTimeTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	return reqTime.Milliseconds() < int64(t.MinTime) && resp != nil
}

// GetPriority 获取优先级
func (t *MinRequestTimeTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *MinRequestTimeTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *MinRequestTimeTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *MinRequestTimeTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// URLTrigger URL匹配触发器
type URLTrigger struct {
	Patterns     []*regexp.Regexp
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断URL是否匹配
func (t *URLTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if req == nil || req.URL == nil {
		return false
	}
	urlStr := req.URL.String()
	for _, pattern := range t.Patterns {
		if pattern.MatchString(urlStr) {
			return true
		}
	}
	return false
}

// GetPriority 获取优先级
func (t *URLTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *URLTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *URLTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *URLTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// DomainTrigger 域名匹配触发器
type DomainTrigger struct {
	Patterns     []*regexp.Regexp
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断域名是否匹配
func (t *DomainTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if req == nil || req.URL == nil || req.URL.Host == "" {
		return false
	}
	host := req.URL.Host
	for _, pattern := range t.Patterns {
		if pattern.MatchString(host) {
			return true
		}
	}
	return false
}

// GetPriority 获取优先级
func (t *DomainTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *DomainTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *DomainTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *DomainTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// CombinedTrigger 组合条件触发器
type CombinedTrigger struct {
	Codes  []int
	URLPatterns  []*regexp.Regexp
	IsAnd        bool // true为AND关系，false为OR关系
	Priority     int
	ProcessStage ProcessStage
	Actions      []common.ActionConfig
}

// Match 判断组合条件是否匹配
func (t *CombinedTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if req == nil || req.URL == nil {
		return false
	}
	statusMatch := false
	if resp != nil {
		for _, code := range t.Codes {
			if resp.StatusCode == code {
				statusMatch = true
				break
			}
		}
	}
	urlMatch := false
	urlStr := req.URL.String()
	for _, pattern := range t.URLPatterns {
		if pattern.MatchString(urlStr) {
			urlMatch = true
			break
		}
	}
	if t.IsAnd {
		return statusMatch && urlMatch
	}
	return statusMatch || urlMatch
}

// GetPriority 获取优先级
func (t *CombinedTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *CombinedTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *CombinedTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *CombinedTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}


// RequestHeaderTrigger 响应头触发器
type RequestHeaderTrigger struct {
	HeaderPatterns map[string][]*regexp.Regexp // HeaderName -> Patterns
	Priority       int
	ProcessStage   ProcessStage
	Actions        []common.ActionConfig
}

// Match 判断请求头是否匹配
func (t *RequestHeaderTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if req == nil || req.Header == nil {
		return false
	}
	for headerName, patterns := range t.HeaderPatterns {
		headerValues := req.Header.Values(headerName) // 使用Values获取所有出现的值
		for _, value := range headerValues {
			for _, pattern := range patterns {
				if pattern.MatchString(value) {
					return true
				}
			}
		}
	}
	return false
}

// GetPriority 获取优先级
func (t *RequestHeaderTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *RequestHeaderTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *RequestHeaderTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *RequestHeaderTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// ResponseHeaderTrigger 响应头触发器
type ResponseHeaderTrigger struct {
	HeaderPatterns map[string][]*regexp.Regexp // HeaderName -> Patterns
	Priority       int
	ProcessStage   ProcessStage
	Actions        []common.ActionConfig
}

// Match 判断响应头是否匹配
func (t *ResponseHeaderTrigger) Match(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	if resp == nil || resp.Header == nil {
		return false
	}
	for headerName, patterns := range t.HeaderPatterns {
		headerValues := resp.Header.Values(headerName)
		for _, value := range headerValues {
			for _, pattern := range patterns {
				if pattern.MatchString(value) {
					return true
				}
			}
		}
	}
	return false
}

// GetPriority 获取优先级
func (t *ResponseHeaderTrigger) GetPriority() int {
	return t.Priority
}

// GetProcessStage 获取处理阶段
func (t *ResponseHeaderTrigger) GetProcessStage() ProcessStage {
	return t.ProcessStage
}

// GetActions 获取动作列表
func (t *ResponseHeaderTrigger) GetActions() []common.ActionConfig {
	return t.Actions
}

// GetMatchedActions 获取匹配的动作
func (t *ResponseHeaderTrigger) GetMatchedActions(req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	if t.Match(req, resp, reqTime) {
		return t.Actions
	}
	return []common.ActionConfig{}
}

// CustomTrigger, NewCustomTrigger, and its methods are defined in custom.go
// Removing them from here to resolve redeclaration errors.

// TriggerStats 触发器统计信息
type TriggerStats struct {
	TotalExecutions   int           // 总执行次数
	TotalMatches      int           // 总匹配次数
	LastExecuted      time.Time     // 最后执行时间
	LastMatched       time.Time     // 最后匹配时间
	ExecutionTime     time.Duration // 累计执行时间

	// 增强性能监控指标
	ErrorCount        int           // 错误次数
	RetryCount        int           // 重试次数
	AverageExecTime   time.Duration // 平均执行时间
	MaxExecTime       time.Duration // 最大执行时间
	MinExecTime       time.Duration // 最小执行时间
	MemoryUsage       int64         // 内存使用量（字节）
	CPUTime           time.Duration // CPU时间
	MatchRate         float64       // 匹配率
	ErrorRate         float64       // 错误率
	ThroughputPerSec  float64       // 每秒处理量

	// 时间窗口统计（最近1分钟）
	RecentExecutions  []time.Time   // 最近执行时间记录
	RecentMatches     []time.Time   // 最近匹配时间记录
	RecentErrors      []time.Time   // 最近错误时间记录
}

// NewTriggerStats 创建新的触发器统计信息
func NewTriggerStats() *TriggerStats {
	return &TriggerStats{
		RecentExecutions: make([]time.Time, 0),
		RecentMatches:    make([]time.Time, 0),
		RecentErrors:     make([]time.Time, 0),
		MinExecTime:      time.Duration(0),
	}
}

// UpdateStats 更新统计信息
func (ts *TriggerStats) UpdateStats(matched bool, execTime time.Duration, hasError bool) {
	now := time.Now()

	// 基本统计更新
	ts.TotalExecutions++
	ts.ExecutionTime += execTime
	ts.LastExecuted = now

	// 更新执行时间统计
	if ts.MinExecTime == 0 || execTime < ts.MinExecTime {
		ts.MinExecTime = execTime
	}
	if execTime > ts.MaxExecTime {
		ts.MaxExecTime = execTime
	}
	if ts.TotalExecutions > 0 {
		ts.AverageExecTime = ts.ExecutionTime / time.Duration(ts.TotalExecutions)
	}

	// 匹配统计
	if matched {
		ts.TotalMatches++
		ts.LastMatched = now
		ts.RecentMatches = append(ts.RecentMatches, now)
	}

	// 错误统计
	if hasError {
		ts.ErrorCount++
		ts.RecentErrors = append(ts.RecentErrors, now)
	}

	// 更新最近执行记录
	ts.RecentExecutions = append(ts.RecentExecutions, now)

	// 清理过期的时间窗口数据（保留最近1分钟）
	ts.cleanupRecentData(now)

	// 更新计算指标
	ts.updateCalculatedMetrics()

	// 更新内存使用情况
	ts.updateMemoryUsage()
}

// cleanupRecentData 清理过期的时间窗口数据
func (ts *TriggerStats) cleanupRecentData(now time.Time) {
	cutoff := now.Add(-time.Minute)

	// 清理执行记录
	ts.RecentExecutions = filterRecentTimes(ts.RecentExecutions, cutoff)
	ts.RecentMatches = filterRecentTimes(ts.RecentMatches, cutoff)
	ts.RecentErrors = filterRecentTimes(ts.RecentErrors, cutoff)
}

// filterRecentTimes 过滤时间切片，保留指定时间之后的记录
func filterRecentTimes(times []time.Time, cutoff time.Time) []time.Time {
	var result []time.Time
	for _, t := range times {
		if t.After(cutoff) {
			result = append(result, t)
		}
	}
	return result
}

// updateCalculatedMetrics 更新计算指标
func (ts *TriggerStats) updateCalculatedMetrics() {
	// 计算匹配率
	if ts.TotalExecutions > 0 {
		ts.MatchRate = float64(ts.TotalMatches) / float64(ts.TotalExecutions)
		ts.ErrorRate = float64(ts.ErrorCount) / float64(ts.TotalExecutions)
	}

	// 计算每秒处理量（基于最近1分钟的数据）
	if len(ts.RecentExecutions) > 0 {
		ts.ThroughputPerSec = float64(len(ts.RecentExecutions)) / 60.0
	}
}

// updateMemoryUsage 更新内存使用情况
func (ts *TriggerStats) updateMemoryUsage() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	ts.MemoryUsage = int64(m.Alloc)
}

// GetPerformanceMetrics 获取性能指标
func (ts *TriggerStats) GetPerformanceMetrics() map[string]interface{} {
	return map[string]interface{}{
		"total_executions":    ts.TotalExecutions,
		"total_matches":       ts.TotalMatches,
		"error_count":         ts.ErrorCount,
		"retry_count":         ts.RetryCount,
		"match_rate":          ts.MatchRate,
		"error_rate":          ts.ErrorRate,
		"avg_exec_time_ms":    ts.AverageExecTime.Milliseconds(),
		"max_exec_time_ms":    ts.MaxExecTime.Milliseconds(),
		"min_exec_time_ms":    ts.MinExecTime.Milliseconds(),
		"throughput_per_sec":  ts.ThroughputPerSec,
		"memory_usage_mb":     ts.MemoryUsage / 1024 / 1024,
		"recent_executions":   len(ts.RecentExecutions),
		"recent_matches":      len(ts.RecentMatches),
		"recent_errors":       len(ts.RecentErrors),
		"last_executed":       ts.LastExecuted.Format(time.RFC3339),
		"last_matched":        ts.LastMatched.Format(time.RFC3339),
	}
}

// TriggerManager 管理所有触发器
type TriggerManager struct {
	Triggers              []Trigger
	Stats                 map[string]*TriggerStats
	EventNames            map[Trigger]string              // 触发器到事件名称的映射
	EventConfigs          map[Trigger]*common.EventConfig // 修改：触发器到事件配置的映射
	Config                *common.Config                  // 修改：全局配置引用
	mu                    sync.RWMutex
	HasPreRequestTriggers bool
	HasPostHeaderTriggers bool
	HasPostBodyTriggers   bool
	errorHandler          *TriggerErrorHandler            // 统一错误处理器
	persistenceManager    *StatsPersistenceManager        // 统计数据持久化管理器
}

// NewTriggerManager 创建触发器管理器
func NewTriggerManager(cfg *common.Config) *TriggerManager {
	tm := &TriggerManager{
		Stats:        make(map[string]*TriggerStats),
		EventConfigs: make(map[Trigger]*common.EventConfig),
		EventNames:   make(map[Trigger]string),
		Config:       cfg,
	}

	// 初始化错误处理器
	errorConfig := DefaultErrorHandlerConfig()
	tm.errorHandler = NewTriggerErrorHandler(
		ParseStrategy(errorConfig.Strategy),
		errorConfig.MaxRetries,
		errorConfig.RetryDelay,
		&LoggerAdapter{triggerLogger},
	)

	for i := range cfg.Events {
		eventCfg := cfg.Events[i] // 获取副本
		if !eventCfg.Enable {
			continue
		}

		// 增强架构：优先使用Conditions和Matches创建增强条件触发器
		if len(eventCfg.Conditions) > 0 && len(eventCfg.Matches) > 0 {
			trigger := tm.createEnhancedConditionalTrigger(eventCfg)
			if trigger != nil {
				tm.Triggers = append(tm.Triggers, trigger)
				tm.EventNames[trigger] = eventCfg.Name
				tm.EventConfigs[trigger] = &eventCfg
				tm.Stats[eventCfg.Name] = &TriggerStats{}
				
				// 设置处理阶段标志
				switch trigger.GetProcessStage() {
				case PreRequest:
					tm.HasPreRequestTriggers = true
				case PostHeader:
					tm.HasPostHeaderTriggers = true
				case PostBody:
					tm.HasPostBodyTriggers = true
				}
			}
			continue
		}

		// 旧架构：使用ConditionalActions创建多条件触发器（向后兼容）
		if len(eventCfg.ConditionalActions) > 0 {
			trigger := tm.createConditionalTrigger(eventCfg)
			if trigger != nil {
				tm.Triggers = append(tm.Triggers, trigger)
				tm.EventNames[trigger] = eventCfg.Name
				tm.EventConfigs[trigger] = &eventCfg
				tm.Stats[eventCfg.Name] = &TriggerStats{}
				
				// 设置处理阶段标志
				switch trigger.GetProcessStage() {
				case PreRequest:
					tm.HasPreRequestTriggers = true
				case PostHeader:
					tm.HasPostHeaderTriggers = true
				case PostBody:
					tm.HasPostBodyTriggers = true
				}
			}
			continue
		}

		// 兼容旧架构的代码（如果需要保留）
		var trigger Trigger
		switch eventCfg.TriggerType {
		default:
			triggerLogger.Warn(fmt.Sprintf("未知的触发器类型或缺少条件动作配置: %s，事件: %s", eventCfg.TriggerType, eventCfg.Name))
			continue
		}

		if trigger != nil {
			tm.Triggers = append(tm.Triggers, trigger)
			tm.EventNames[trigger] = eventCfg.Name
			tm.EventConfigs[trigger] = &cfg.Events[i] // 存储指向切片中原始项的指针

			switch trigger.GetProcessStage() {
			case PreRequest:
				tm.HasPreRequestTriggers = true
			case PostHeader:
				tm.HasPostHeaderTriggers = true
			case PostBody:
				tm.HasPostBodyTriggers = true
			}
		}
	}

	// 按优先级降序排序（高优先级先执行）
	sort.SliceStable(tm.Triggers, func(i, j int) bool {
		return tm.Triggers[i].GetPriority() > tm.Triggers[j].GetPriority()
	})

	return tm
}

// createConditionalTrigger 创建条件触发器
func (tm *TriggerManager) createConditionalTrigger(eventCfg common.EventConfig) Trigger {
	// 确定处理阶段：优先使用事件配置，如果为空则使用全局默认值
	processStage := eventCfg.ProcessStage
	if processStage == "" && tm.Config != nil && tm.Config.Global.DefaultProcessStage != "" {
		processStage = tm.Config.Global.DefaultProcessStage
		triggerLogger.Info(fmt.Sprintf("事件 %s 未指定处理阶段，使用全局默认值: %s", eventCfg.Name, processStage))
	}

	trigger := &ConditionalTrigger{
		Priority:     eventCfg.Priority,
		ProcessStage: ProcessStage(processStage),
		EventName:    eventCfg.Name,
		Config:       tm.Config,
	}

	// 为每个条件配置创建匹配器
	for _, condConfig := range eventCfg.ConditionalActions {
		if !condConfig.Enable {
			continue
		}

		matcher := ConditionMatcher{
			Name:           condConfig.ConditionName,
			Enable:         condConfig.Enable,
			ActionSequenceName: condConfig.ActionSequenceName,
		}

		// 根据条件类型创建匹配函数
		matcher.Matcher = tm.createConditionMatcher(condConfig)
		if matcher.Matcher != nil {
			trigger.Conditions = append(trigger.Conditions, matcher)
		}
	}

	if len(trigger.Conditions) == 0 {
		return nil
	}

	return trigger
}

// createConditionMatcher 根据条件配置创建匹配函数
func (tm *TriggerManager) createConditionMatcher(condConfig common.ConditionalActionConfig) func(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	return func(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
		// 收集所有条件的匹配结果
		var conditions []bool

		// 检查BodyPatterns
		if condConfig.BodyPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.BodyPatterns)
			if resp != nil && resp.Body != nil {
				body, _ := io.ReadAll(resp.Body)
				resp.Body = io.NopCloser(bytes.NewReader(body))
				conditions = append(conditions, matcher(string(body)))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查URLPatterns
		if condConfig.URLPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.URLPatterns)
			if req != nil {
				conditions = append(conditions, matcher(req.URL.String()))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查DomainPatterns
		if condConfig.DomainPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.DomainPatterns)
			if req != nil {
				conditions = append(conditions, matcher(req.Host))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查RequestBodyPatterns
		if condConfig.RequestBodyPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.RequestBodyPatterns)
			if req != nil && req.Body != nil {
				body, _ := io.ReadAll(req.Body)
				req.Body = io.NopCloser(bytes.NewReader(body))
				conditions = append(conditions, matcher(string(body)))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查RequestHeaderPatterns
		if condConfig.RequestHeaderPatterns != nil {
			matcher := tm.createEnhancedHeaderPatternMatcher(condConfig.RequestHeaderPatterns)
			if req != nil {
				conditions = append(conditions, matcher(req.Header))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查ResponseHeaderPatterns
		if condConfig.ResponseHeaderPatterns != nil {
			matcher := tm.createEnhancedHeaderPatternMatcher(condConfig.ResponseHeaderPatterns)
			if resp != nil {
				conditions = append(conditions, matcher(resp.Header))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查StatusCodes
	if condConfig.StatusCodes != nil && resp != nil {
		statusMatch := false
		// 检查Codes状态码
		for _, code := range condConfig.StatusCodes.Codes {
			if resp.StatusCode == code {
				statusMatch = true
					break
				}
			}
			// 检查状态码列表中的其他状态码
			if !statusMatch {
				// 已经在上面的循环中检查了所有状态码
			}
			conditions = append(conditions, statusMatch)
		}

		// 如果没有任何条件，返回false
		if len(conditions) == 0 {
			return false
		}

		// 根据ConditionRelation计算最终结果
		switch condConfig.ConditionRelation {
		case "AND":
			// 所有条件都必须为true
			for _, cond := range conditions {
				if !cond {
					return false
				}
			}
			return true
		case "OR":
			// 任一条件为true即可
			for _, cond := range conditions {
				if cond {
					return true
				}
			}
			return false
		default:
			// 默认使用AND逻辑
			for _, cond := range conditions {
				if !cond {
					return false
				}
			}
			return true
		}
	}
}

// createEnhancedPatternMatcher 创建增强模式匹配器
func (tm *TriggerManager) createEnhancedPatternMatcher(config *common.EnhancedPatternConfig) func(string) bool {
	if config == nil {
		return func(string) bool { return false }
	}

	return func(text string) bool {
		// 收集所有模式表达式的匹配结果
		var results []bool

		// 处理所有模式表达式
		for _, expr := range config.Patterns {
			matched := tm.matchPatternExpression(expr, text)
			results = append(results, matched)
		}

		// 如果没有任何模式，返回false
		if len(results) == 0 {
			return false
		}

		// 根据逻辑运算符计算结果
		switch config.Relation {
		case "AND":
			for _, result := range results {
				if !result {
					return false
				}
			}
			return true
		case "OR":
			for _, result := range results {
				if result {
					return true
				}
			}
			return false
		default:
			// 默认使用OR逻辑
			for _, result := range results {
				if result {
					return true
				}
			}
			return false
		}
	}
}

// createEnhancedHeaderPatternMatcher 创建增强头部模式匹配器
func (tm *TriggerManager) createEnhancedHeaderPatternMatcher(config *common.EnhancedHeaderPatternConfig) func(http.Header) bool {
	if config == nil {
		return func(http.Header) bool { return false }
	}

	return func(headers http.Header) bool {
		// 收集所有头部表达式的匹配结果
		var results []bool

		// 处理头部表达式
		for headerName, patterns := range config.Headers {
			headerValues := headers.Values(headerName)
			headerMatch := false
			for _, value := range headerValues {
				patternMatcher := tm.createEnhancedPatternMatcher(patterns)
				if patternMatcher(value) {
					headerMatch = true
					break
				}
			}
			results = append(results, headerMatch)
		}

		// 如果没有任何模式，返回false
		if len(results) == 0 {
			return false
		}

		// 根据逻辑运算符计算结果（使用默认OR逻辑）
		switch "OR" {
		case "AND":
			for _, result := range results {
				if !result {
					return false
				}
			}
			return true
		case "OR":
			for _, result := range results {
				if result {
					return true
				}
			}
			return false
		default:
			// 默认使用OR逻辑
			for _, result := range results {
				if result {
					return true
				}
			}
			return false
		}
	}
}

// matchPatternExpression 匹配单个模式表达式
func (tm *TriggerManager) matchPatternExpression(expr common.PatternExpression, text string) bool {
	var results []bool

	// 处理基本模式
	if expr.Pattern != "" {
		switch expr.Type {
		case "regex":
			regex, err := regexp.Compile(expr.Pattern)
			if err != nil {
				triggerLogger.Warn(fmt.Sprintf("正则表达式编译失败: %s，错误: %v", expr.Pattern, err))
				results = append(results, false)
			} else {
				matched := regex.MatchString(text)
				results = append(results, matched)
			}
		case "string":
			matched := strings.Contains(text, expr.Pattern)
			results = append(results, matched)
		default:
			// 默认为字符串匹配
			matched := strings.Contains(text, expr.Pattern)
			results = append(results, matched)
		}
	}

	// 处理链式模式
	for _, chain := range expr.Chain {
		chainResult := tm.matchChainedPattern(chain, text)
		results = append(results, chainResult)
	}

	// 处理子组
	if expr.SubGroup != nil {
		subGroupMatcher := tm.createEnhancedPatternMatcher(expr.SubGroup)
		subGroupResult := subGroupMatcher(text)
		results = append(results, subGroupResult)
	}

	// 如果没有任何模式，返回false
	if len(results) == 0 {
		return false
	}

	// 根据逻辑运算符计算结果
	finalResult := false
	switch expr.Logic {
	case "AND":
		finalResult = true
		for _, result := range results {
			if !result {
				finalResult = false
				break
			}
		}
	case "OR":
		for _, result := range results {
			if result {
				finalResult = true
				break
			}
		}
	default:
		// 默认OR逻辑
		for _, result := range results {
			if result {
				finalResult = true
				break
			}
		}
	}

	// 处理否定匹配
	if expr.Negate {
		return !finalResult
	}
	return finalResult
}

// matchChainedPattern 匹配链式模式
func (tm *TriggerManager) matchChainedPattern(chain common.ChainedPattern, text string) bool {
	// 编译正则表达式
	regex, err := regexp.Compile(chain.Pattern)
	if err != nil {
		triggerLogger.Warn(fmt.Sprintf("链式模式正则表达式编译失败: %s，错误: %v", chain.Pattern, err))
		return false
	}

	matched := regex.MatchString(text)
	
	// 处理否定匹配
	if chain.Negate {
		return !matched
	}
	return matched
}

// matchHeaderExpression 匹配头部表达式
func (tm *TriggerManager) matchHeaderExpression(expr common.HeaderPatternExpression, headers http.Header) bool {
	// 获取指定头部的值
	headerValues := headers.Values(expr.Header)
	if len(headerValues) == 0 {
		// 如果头部不存在，根据是否否定匹配返回结果
		return expr.Negate
	}

	// 如果没有模式配置，只检查头部是否存在
	if expr.Patterns == nil {
		// 头部存在，根据否定匹配返回结果
		return !expr.Negate
	}

	// 使用增强模式匹配器检查头部值
	patternMatcher := tm.createEnhancedPatternMatcher(expr.Patterns)
	
	// 检查所有头部值
	for _, value := range headerValues {
		matched := patternMatcher(value)
		if matched {
			// 处理否定匹配
			if expr.Negate {
				return false
			}
			return true
		}
	}

	// 没有匹配的值
	if expr.Negate {
		return true
	}
	return false
}

func (tm *TriggerManager) GetEventConfig(trigger Trigger) *common.EventConfig {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return tm.EventConfigs[trigger]
}

func (tm *TriggerManager) GetTriggersForStage(stage ProcessStage) []Trigger {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	var triggers []Trigger
	for _, t := range tm.Triggers {
		if t.GetProcessStage() == stage {
			triggers = append(triggers, t)
		}
	}
	return triggers
}

// ProcessTriggers processes triggers for a given stage and returns action strings.
// This function's return type and internal logic will need to change
// to return []common.ActionConfig and handle them appropriately.
func (tm *TriggerManager) ProcessTriggers(stage ProcessStage, req *http.Request, resp *http.Response, reqTime time.Duration) []common.ActionConfig {
	tm.mu.RLock()
	triggers := make([]Trigger, len(tm.Triggers))
	copy(triggers, tm.Triggers)
	eventNames := make(map[Trigger]string)
	for k, v := range tm.EventNames {
		eventNames[k] = v
	}
	tm.mu.RUnlock()

	var allActions []common.ActionConfig

	for _, trigger := range triggers {
		if trigger.GetProcessStage() == stage {
			triggerName := eventNames[trigger]
			retryCount := 0

			for {
				startTime := time.Now()
				var matched bool
				var err error

				// 使用defer捕获panic并转换为错误
				func() {
					defer func() {
						if r := recover(); r != nil {
							err = fmt.Errorf("触发器执行崩溃: %v", r)
						}
					}()
					matched = trigger.Match(req, resp, reqTime)
				}()

				execTime := time.Since(startTime)

				// Record execution stats with proper locking (including error information)
				tm.updateStatsWithError(triggerName, matched, execTime, err != nil)

				// 处理错误
				if err != nil {
					shouldContinue, shouldRetry := tm.errorHandler.HandleError(context.Background(), err, triggerName, retryCount)
					if shouldRetry && retryCount < tm.errorHandler.maxRetries {
						// 记录重试统计
						tm.recordRetry(triggerName)
						retryCount++
						continue
					}
					if !shouldContinue {
						return allActions
					}
					break // 跳过当前触发器
				}

				if matched {
					triggerLogger.Info(fmt.Sprintf("触发器 '%s' 匹配成功（阶段: %s）", triggerName, stage))

					// 使用统一的GetMatchedActions接口获取匹配的动作
					matchedActions := trigger.GetMatchedActions(req, resp, reqTime)
					allActions = append(allActions, matchedActions...)
				}
				break // 退出重试循环
			}
		}
	}
	return allActions
}

// updateStats 线程安全地更新触发器统计信息
func (tm *TriggerManager) updateStats(triggerName string, matched bool, execTime time.Duration) {
	tm.updateStatsWithError(triggerName, matched, execTime, false)
}

// updateStatsWithError 线程安全地更新触发器统计信息（包含错误信息）
func (tm *TriggerManager) updateStatsWithError(triggerName string, matched bool, execTime time.Duration, hasError bool) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if _, ok := tm.Stats[triggerName]; !ok {
		tm.Stats[triggerName] = NewTriggerStats()
	}
	stat := tm.Stats[triggerName]
	stat.UpdateStats(matched, execTime, hasError)
}

// recordRetry 记录重试统计
func (tm *TriggerManager) recordRetry(triggerName string) {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if stat, ok := tm.Stats[triggerName]; ok {
		stat.RetryCount++
	}
}

func (tm *TriggerManager) RecordExecution(triggerType string, matched bool, execTime time.Duration) {
	// 使用线程安全的统计更新方法
	tm.updateStats(triggerType, matched, execTime)
}

func (tm *TriggerManager) GetPerformanceReport() map[string]interface{} {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	report := make(map[string]interface{})

	// 添加系统级别的性能指标
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	report["system"] = map[string]interface{}{
		"heap_alloc_mb":    m.HeapAlloc / 1024 / 1024,
		"heap_sys_mb":      m.HeapSys / 1024 / 1024,
		"num_gc":           m.NumGC,
		"goroutines":       runtime.NumGoroutine(),
		"total_triggers":   len(tm.Stats),
	}

	// 添加每个触发器的详细性能指标
	for name, stats := range tm.Stats {
		report[name] = stats.GetPerformanceMetrics()
	}

	return report
}

// GetDetailedPerformanceReport 获取详细的性能报告
func (tm *TriggerManager) GetDetailedPerformanceReport() map[string]interface{} {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	report := tm.GetPerformanceReport()

	// 添加聚合统计
	totalExecutions := 0
	totalMatches := 0
	totalErrors := 0
	totalRetries := 0
	var totalExecTime time.Duration

	for _, stats := range tm.Stats {
		totalExecutions += stats.TotalExecutions
		totalMatches += stats.TotalMatches
		totalErrors += stats.ErrorCount
		totalRetries += stats.RetryCount
		totalExecTime += stats.ExecutionTime
	}

	report["aggregate"] = map[string]interface{}{
		"total_executions":     totalExecutions,
		"total_matches":        totalMatches,
		"total_errors":         totalErrors,
		"total_retries":        totalRetries,
		"overall_match_rate":   float64(totalMatches) / float64(totalExecutions),
		"overall_error_rate":   float64(totalErrors) / float64(totalExecutions),
		"avg_exec_time_ms":     totalExecTime.Milliseconds() / int64(totalExecutions),
		"total_exec_time_ms":   totalExecTime.Milliseconds(),
	}

	return report
}

// GetMatchedEvents is a placeholder or example, actual usage might differ


func (tm *TriggerManager) GetEventName(trigger Trigger) string {
	tm.mu.RLock()
	defer tm.mu.RUnlock()
	return tm.EventNames[trigger]
}

// createEnhancedConditionalTrigger 创建增强条件触发器
func (tm *TriggerManager) createEnhancedConditionalTrigger(eventCfg common.EventConfig) Trigger {
	// 确定处理阶段：优先使用事件配置，如果为空则使用全局默认值
	processStage := eventCfg.ProcessStage
	if processStage == "" && tm.Config != nil && tm.Config.Global.DefaultProcessStage != "" {
		processStage = tm.Config.Global.DefaultProcessStage
		triggerLogger.Info(fmt.Sprintf("事件 %s 未指定处理阶段，使用全局默认值: %s", eventCfg.Name, processStage))
	}

	trigger := &EnhancedConditionalTrigger{
		Priority:     eventCfg.Priority,
		ProcessStage: ProcessStage(processStage),
		EventName:    eventCfg.Name,
		Conditions:   make(map[string]*EnhancedCondition),
		Config:       tm.Config,
	}

	// 创建条件
	for _, condConfig := range eventCfg.Conditions {
		if !condConfig.Enable {
			continue
		}

		condition := &EnhancedCondition{
			Name:    condConfig.Name,
			Enable:  condConfig.Enable,
			Matcher: tm.createEnhancedConditionMatcher(condConfig),
		}
		trigger.Conditions[condConfig.Name] = condition
	}

	// 创建匹配规则
	for _, matchConfig := range eventCfg.Matches {
		match := tm.createEnhancedMatch(matchConfig, trigger.Conditions)
		if match != nil {
			trigger.Matches = append(trigger.Matches, match)
		}
	}

	if len(trigger.Conditions) == 0 || len(trigger.Matches) == 0 {
		return nil
	}

	return trigger
}

// createEnhancedConditionMatcher 创建增强条件匹配器
func (tm *TriggerManager) createEnhancedConditionMatcher(condConfig common.ConditionConfig) func(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
	return func(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
		// 调试日志
		triggerLogger.Info(fmt.Sprintf("调用增强条件匹配器，条件: %s, 最大请求时间: %d", condConfig.Name, condConfig.MaxRequestTime))
		// 收集所有条件的匹配结果
		var conditions []bool

		// 检查BodyPatterns
		if condConfig.BodyPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.BodyPatterns)
			if resp != nil && resp.Body != nil {
				body, _ := io.ReadAll(resp.Body)
				resp.Body = io.NopCloser(bytes.NewReader(body))
				conditions = append(conditions, matcher(string(body)))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查URLPatterns
		if condConfig.URLPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.URLPatterns)
			if req != nil {
				conditions = append(conditions, matcher(req.URL.String()))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查DomainPatterns
		if condConfig.DomainPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.DomainPatterns)
			if req != nil {
				conditions = append(conditions, matcher(req.Host))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查RequestBodyPatterns
		if condConfig.RequestBodyPatterns != nil {
			matcher := tm.createEnhancedPatternMatcher(condConfig.RequestBodyPatterns)
			if req != nil && req.Body != nil {
				body, _ := io.ReadAll(req.Body)
				req.Body = io.NopCloser(bytes.NewReader(body))
				conditions = append(conditions, matcher(string(body)))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查RequestHeaderPatterns
		if condConfig.RequestHeaderPatterns != nil {
			matcher := tm.createEnhancedHeaderPatternMatcher(condConfig.RequestHeaderPatterns)
			if req != nil {
				conditions = append(conditions, matcher(req.Header))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查ResponseHeaderPatterns
		if condConfig.ResponseHeaderPatterns != nil {
			matcher := tm.createEnhancedHeaderPatternMatcher(condConfig.ResponseHeaderPatterns)
			if resp != nil {
				conditions = append(conditions, matcher(resp.Header))
			} else {
				conditions = append(conditions, false)
			}
		}

		// 检查MaxRequestTime
		triggerLogger.Info(fmt.Sprintf("检查最大请求时间: %d > 0 = %v", condConfig.MaxRequestTime, condConfig.MaxRequestTime > 0))
		if condConfig.MaxRequestTime > 0 {
			maxDuration := time.Duration(condConfig.MaxRequestTime) * time.Millisecond
			maxResult := reqTime <= maxDuration
			// 调试日志
			triggerLogger.Info(fmt.Sprintf("最大请求时间检查: 请求时间=%v, 最大时长=%v, 结果=%v", reqTime, maxDuration, maxResult))
			conditions = append(conditions, maxResult)
		} else {
			triggerLogger.Info(fmt.Sprintf("最大请求时间条件不满足: %d", condConfig.MaxRequestTime))
		}

		// 检查MinRequestTime
		if condConfig.MinRequestTime > 0 {
			minDuration := time.Duration(condConfig.MinRequestTime) * time.Millisecond
			conditions = append(conditions, reqTime >= minDuration)
		}

		// 检查StatusCodes
		if condConfig.StatusCodes != nil && resp != nil {
			matched := false
			// 检查状态码
			for _, code := range condConfig.StatusCodes.Codes {
				if resp.StatusCode == code {
					matched = true
					break
				}
			}
			conditions = append(conditions, matched)
		}

		// 如果没有任何条件，返回false
		if len(conditions) == 0 {
			return false
		}

		// 根据ConditionRelation计算最终结果
		switch condConfig.ConditionRelation {
		case "AND":
			// 所有条件都必须为true
			for _, cond := range conditions {
				if !cond {
					return false
				}
			}
			return true
		case "OR":
			// 任一条件为true即可
			for _, cond := range conditions {
				if cond {
					return true
				}
			}
			return false
		default:
			// 默认使用AND逻辑
			for _, cond := range conditions {
				if !cond {
					return false
				}
			}
			return true
		}
	}
}

// createEnhancedMatch 创建增强匹配规则（支持多层嵌套）
func (tm *TriggerManager) createEnhancedMatch(matchConfig common.MatchConfig, conditions map[string]*EnhancedCondition) *EnhancedMatch {
	if !matchConfig.Enable {
		return nil
	}

	match := &EnhancedMatch{
		Name:       matchConfig.Name,
		Enable:     matchConfig.Enable,
		Conditions: matchConfig.Conditions,
		Logic:      matchConfig.Logic,
		Actions:    matchConfig.Actions,
	}

	// 创建嵌套的子匹配规则
	for _, subMatchConfig := range matchConfig.SubMatches {
		subMatch := tm.createEnhancedMatch(subMatchConfig, conditions)
		if subMatch != nil {
			match.SubMatches = append(match.SubMatches, subMatch)
		}
	}

	// 创建匹配器函数
	match.Matcher = tm.createMatcherFunction(match, conditions)

	return match
}

// createMatcherFunction 创建匹配器函数（支持多层嵌套和逻辑组合）
func (tm *TriggerManager) createMatcherFunction(match *EnhancedMatch, conditions map[string]*EnhancedCondition) func(conditionResults map[string]bool) bool {
	return func(conditionResults map[string]bool) bool {
		// 收集当前层级的条件结果
		var currentResults []bool
		for _, condName := range match.Conditions {
			if result, exists := conditionResults[condName]; exists {
				currentResults = append(currentResults, result)
			} else {
				currentResults = append(currentResults, false)
			}
		}

		// 收集子匹配规则的结果
		var subResults []bool
		for _, subMatch := range match.SubMatches {
			if subMatch.Enable {
				subResults = append(subResults, subMatch.Matcher(conditionResults))
			}
		}

		// 合并所有结果
		allResults := append(currentResults, subResults...)
		if len(allResults) == 0 {
			return false
		}

		// 根据逻辑关系计算最终结果
		switch strings.ToUpper(match.Logic) {
		case "OR":
			for _, result := range allResults {
				if result {
					return true
				}
			}
			return false
		case "NOT":
			// NOT逻辑：所有条件都为false时返回true
			for _, result := range allResults {
				if result {
					return false
				}
			}
			return true
		case "AND":
			fallthrough
		default:
			// 默认为AND逻辑
			for _, result := range allResults {
				if !result {
					return false
				}
			}
			return true
		}
	}
}

// EnableStatsPersistence 启用统计数据持久化
func (tm *TriggerManager) EnableStatsPersistence(options StatsPersistenceOptions) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.persistenceManager != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化已启用",
			"",
		)
	}

	tm.persistenceManager = NewStatsPersistenceManager(options)

	// 启动持久化服务
	if err := tm.persistenceManager.Start(); err != nil {
		tm.persistenceManager = nil
		return err
	}

	// 尝试加载现有统计数据
	if stats, err := tm.persistenceManager.LoadStats(); err == nil && len(stats) > 0 {
		// 合并加载的统计数据
		for name, loadedStats := range stats {
			if _, exists := tm.Stats[name]; !exists {
				tm.Stats[name] = loadedStats
			}
		}
		triggerLogger.Info(fmt.Sprintf("已加载 %d 个触发器的历史统计数据", len(stats)))
	}

	return nil
}

// DisableStatsPersistence 禁用统计数据持久化
func (tm *TriggerManager) DisableStatsPersistence() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.persistenceManager == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化未启用",
			"",
		)
	}

	// 保存当前统计数据
	if err := tm.persistenceManager.SaveStats(tm.Stats); err != nil {
		triggerLogger.Warn(fmt.Sprintf("保存统计数据失败: %v", err))
	}

	// 停止持久化服务
	if err := tm.persistenceManager.Stop(); err != nil {
		return err
	}

	tm.persistenceManager = nil
	return nil
}

// SaveStatsNow 立即保存统计数据
func (tm *TriggerManager) SaveStatsNow() error {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.persistenceManager == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化未启用",
			"",
		)
	}

	return tm.persistenceManager.SaveStats(tm.Stats)
}

// ExportStats 导出统计数据
func (tm *TriggerManager) ExportStats(exportPath string) error {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.persistenceManager == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化未启用",
			"",
		)
	}

	return tm.persistenceManager.ExportStats(tm.Stats, exportPath)
}

// ImportStats 导入统计数据
func (tm *TriggerManager) ImportStats(importPath string, merge bool) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.persistenceManager == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化未启用",
			"",
		)
	}

	importedStats, err := tm.persistenceManager.ImportStats(importPath)
	if err != nil {
		return err
	}

	if merge {
		// 合并模式：保留现有数据，只添加新的
		for name, stats := range importedStats {
			if _, exists := tm.Stats[name]; !exists {
				tm.Stats[name] = stats
			}
		}
	} else {
		// 替换模式：完全替换现有统计数据
		tm.Stats = importedStats
	}

	triggerLogger.Info(fmt.Sprintf("已导入 %d 个触发器的统计数据", len(importedStats)))
	return nil
}

// CleanupOldStats 清理旧统计数据
func (tm *TriggerManager) CleanupOldStats() error {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.persistenceManager == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化未启用",
			"",
		)
	}

	return tm.persistenceManager.CleanupOldData()
}

// GetStatsHistory 获取统计数据历史信息
func (tm *TriggerManager) GetStatsHistory() ([]StatsHistoryEntry, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if tm.persistenceManager == nil {
		return nil, errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeInvalidConfiguration,
			"统计数据持久化未启用",
			"",
		)
	}

	return tm.persistenceManager.GetHistoryInfo()
}
