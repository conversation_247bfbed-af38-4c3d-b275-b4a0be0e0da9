package trigger

import (
	"testing"
	"time"

	"github.com/mubeng/mubeng/common"
	"github.com/mubeng/mubeng/common/constants"
)

// TestTriggerPrioritySorting 测试触发器优先级排序
func TestTriggerPrioritySorting(t *testing.T) {
	// 创建测试配置
	cfg := &common.Config{
		Events: []common.EventConfig{
			{
				Name:         "low_priority",
				Enable:       true,
				Priority:     1,
				ProcessStage: constants.PreRequest,
				ConditionalActions: []common.ConditionalActionConfig{
					{
						ConditionName: "low_priority_condition",
						StatusCodes: &common.StatusCodesConfig{
							Codes: []int{404},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
			{
				Name:         "high_priority",
				Enable:       true,
				Priority:     10,
				ProcessStage: constants.PreRequest,
				ConditionalActions: []common.ConditionalActionConfig{
					{
						ConditionName: "high_priority_condition",
						StatusCodes: &common.StatusCodesConfig{
							Codes: []int{500},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
			{
				Name:         "medium_priority",
				Enable:       true,
				Priority:     5,
				ProcessStage: constants.PreRequest,
				ConditionalActions: []common.ConditionalActionConfig{
					{
						ConditionName: "medium_priority_condition",
						StatusCodes: &common.StatusCodesConfig{
							Codes: []int{403},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
		},
		Actions: map[string]common.ActionSequenceName{
			"test_action": {
				Sequence: []common.ActionConfig{
					{
						Type: "log",
						Params: map[string]interface{}{
							"message": "test",
						},
					},
				},
			},
		},
	}

	// 创建触发器管理器
	tm := NewTriggerManager(cfg)

	// 验证触发器数量
	if len(tm.Triggers) != 3 {
		t.Errorf("期望3个触发器，实际得到%d个", len(tm.Triggers))
	}

	// 验证优先级排序（高优先级在前）
	expectedPriorities := []int{10, 5, 1}
	for i, trigger := range tm.Triggers {
		actualPriority := trigger.GetPriority()
		if actualPriority != expectedPriorities[i] {
			t.Errorf("触发器%d的优先级错误：期望%d，实际%d", i, expectedPriorities[i], actualPriority)
		}
	}
}

// TestTriggerStatsConcurrency 测试触发器统计的并发安全性
func TestTriggerStatsConcurrency(t *testing.T) {
	cfg := &common.Config{
		Events: []common.EventConfig{
			{
				Name:         "test_trigger",
				Enable:       true,
				Priority:     1,
				ProcessStage: constants.PreRequest,
				ConditionalActions: []common.ConditionalActionConfig{
					{
						ConditionName: "test_condition",
						StatusCodes: &common.StatusCodesConfig{
							Codes: []int{200},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
		},
		Actions: map[string]common.ActionSequenceName{
			"test_action": {
				Sequence: []common.ActionConfig{
					{
						Type: "log",
						Params: map[string]interface{}{
							"message": "test",
						},
					},
				},
			},
		},
	}

	tm := NewTriggerManager(cfg)

	// 并发更新统计
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func() {
			for j := 0; j < 100; j++ {
				tm.updateStats("test_trigger", true, time.Millisecond)
			}
			done <- true
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		<-done
	}

	// 验证统计结果
	stats := tm.Stats["test_trigger"]
	if stats == nil {
		t.Error("统计信息不应为nil")
		return
	}

	if stats.TotalExecutions != 1000 {
		t.Errorf("期望执行次数1000，实际%d", stats.TotalExecutions)
	}

	if stats.TotalMatches != 1000 {
		t.Errorf("期望匹配次数1000，实际%d", stats.TotalMatches)
	}
}

// TestTriggerTypeConstants 测试触发器类型常量
func TestTriggerTypeConstants(t *testing.T) {
	expectedTypes := []string{
		constants.TriggerTypeStatus,
		constants.TriggerTypeBody,
		constants.TriggerTypeMaxRequestTime,
		constants.TriggerTypeConnTimeOut,
		constants.TriggerTypeMinRequestTime,
		constants.TriggerTypeURL,
		constants.TriggerTypeDomain,
		constants.TriggerTypeCombined,
		constants.TriggerTypeCustom,
		constants.TriggerTypeRequestBody,
		constants.TriggerTypeRequestHeader,
		constants.TriggerTypeResponseHeader,
	}

	// 验证常量值不为空
	for _, triggerType := range expectedTypes {
		if triggerType == "" {
			t.Errorf("触发器类型常量不应为空")
		}
	}

	// 验证常量值唯一性
	typeSet := make(map[string]bool)
	for _, triggerType := range expectedTypes {
		if typeSet[triggerType] {
			t.Errorf("触发器类型常量重复: %s", triggerType)
		}
		typeSet[triggerType] = true
	}
}
