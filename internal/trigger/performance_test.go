package trigger

import (
	"testing"
	"time"
)

// TestTriggerStatsCreation 测试触发器统计信息创建
func TestTriggerStatsCreation(t *testing.T) {
	stats := NewTriggerStats()
	
	if stats == nil {
		t.<PERSON>rror("NewTriggerStats应该返回非nil的统计对象")
	}
	
	if stats.TotalExecutions != 0 {
		t.<PERSON><PERSON><PERSON>("初始执行次数应该为0，实际为%d", stats.TotalExecutions)
	}
	
	if stats.TotalMatches != 0 {
		t.<PERSON>rro<PERSON>("初始匹配次数应该为0，实际为%d", stats.TotalMatches)
	}
	
	if len(stats.RecentExecutions) != 0 {
		t.<PERSON>rrorf("初始最近执行记录应该为空，实际长度为%d", len(stats.RecentExecutions))
	}
}

// TestTriggerStatsUpdate 测试统计信息更新
func TestTriggerStatsUpdate(t *testing.T) {
	stats := NewTriggerStats()
	
	// 测试正常执行
	execTime := 100 * time.Millisecond
	stats.UpdateStats(true, execTime, false)
	
	if stats.TotalExecutions != 1 {
		t.<PERSON><PERSON><PERSON>("执行次数应该为1，实际为%d", stats.TotalExecutions)
	}
	
	if stats.TotalMatches != 1 {
		t.Errorf("匹配次数应该为1，实际为%d", stats.TotalMatches)
	}
	
	if stats.ErrorCount != 0 {
		t.Errorf("错误次数应该为0，实际为%d", stats.ErrorCount)
	}
	
	if stats.AverageExecTime != execTime {
		t.Errorf("平均执行时间应该为%v，实际为%v", execTime, stats.AverageExecTime)
	}
	
	if stats.MaxExecTime != execTime {
		t.Errorf("最大执行时间应该为%v，实际为%v", execTime, stats.MaxExecTime)
	}
	
	if stats.MinExecTime != execTime {
		t.Errorf("最小执行时间应该为%v，实际为%v", execTime, stats.MinExecTime)
	}
	
	// 测试错误执行
	stats.UpdateStats(false, 50*time.Millisecond, true)
	
	if stats.TotalExecutions != 2 {
		t.Errorf("执行次数应该为2，实际为%d", stats.TotalExecutions)
	}
	
	if stats.TotalMatches != 1 {
		t.Errorf("匹配次数应该仍为1，实际为%d", stats.TotalMatches)
	}
	
	if stats.ErrorCount != 1 {
		t.Errorf("错误次数应该为1，实际为%d", stats.ErrorCount)
	}
	
	if stats.MinExecTime != 50*time.Millisecond {
		t.Errorf("最小执行时间应该更新为50ms，实际为%v", stats.MinExecTime)
	}
}

// TestTriggerStatsMetrics 测试性能指标计算
func TestTriggerStatsMetrics(t *testing.T) {
	stats := NewTriggerStats()
	
	// 添加多次执行记录
	stats.UpdateStats(true, 100*time.Millisecond, false)  // 匹配，无错误
	stats.UpdateStats(false, 50*time.Millisecond, false)  // 不匹配，无错误
	stats.UpdateStats(true, 200*time.Millisecond, false)  // 匹配，无错误
	stats.UpdateStats(false, 75*time.Millisecond, true)   // 不匹配，有错误
	
	// 验证基本统计
	if stats.TotalExecutions != 4 {
		t.Errorf("总执行次数应该为4，实际为%d", stats.TotalExecutions)
	}
	
	if stats.TotalMatches != 2 {
		t.Errorf("总匹配次数应该为2，实际为%d", stats.TotalMatches)
	}
	
	if stats.ErrorCount != 1 {
		t.Errorf("错误次数应该为1，实际为%d", stats.ErrorCount)
	}
	
	// 验证计算指标
	expectedMatchRate := 2.0 / 4.0
	if stats.MatchRate != expectedMatchRate {
		t.Errorf("匹配率应该为%f，实际为%f", expectedMatchRate, stats.MatchRate)
	}
	
	expectedErrorRate := 1.0 / 4.0
	if stats.ErrorRate != expectedErrorRate {
		t.Errorf("错误率应该为%f，实际为%f", expectedErrorRate, stats.ErrorRate)
	}
	
	if stats.MinExecTime != 50*time.Millisecond {
		t.Errorf("最小执行时间应该为50ms，实际为%v", stats.MinExecTime)
	}
	
	if stats.MaxExecTime != 200*time.Millisecond {
		t.Errorf("最大执行时间应该为200ms，实际为%v", stats.MaxExecTime)
	}
}

// TestTriggerStatsRecentDataCleanup 测试最近数据清理
func TestTriggerStatsRecentDataCleanup(t *testing.T) {
	stats := NewTriggerStats()
	
	// 添加一些旧的记录
	oldTime := time.Now().Add(-2 * time.Minute)
	stats.RecentExecutions = append(stats.RecentExecutions, oldTime)
	stats.RecentMatches = append(stats.RecentMatches, oldTime)
	stats.RecentErrors = append(stats.RecentErrors, oldTime)
	
	// 添加新的执行记录
	stats.UpdateStats(true, 100*time.Millisecond, false)
	
	// 验证旧数据被清理
	if len(stats.RecentExecutions) != 1 {
		t.Errorf("最近执行记录应该只有1条（旧记录被清理），实际为%d", len(stats.RecentExecutions))
	}
	
	if len(stats.RecentMatches) != 1 {
		t.Errorf("最近匹配记录应该只有1条（旧记录被清理），实际为%d", len(stats.RecentMatches))
	}
	
	if len(stats.RecentErrors) != 0 {
		t.Errorf("最近错误记录应该为0（旧记录被清理，新记录无错误），实际为%d", len(stats.RecentErrors))
	}
}

// TestTriggerStatsPerformanceMetrics 测试性能指标获取
func TestTriggerStatsPerformanceMetrics(t *testing.T) {
	stats := NewTriggerStats()
	
	// 添加一些测试数据
	stats.UpdateStats(true, 100*time.Millisecond, false)
	stats.UpdateStats(false, 50*time.Millisecond, true)
	
	metrics := stats.GetPerformanceMetrics()
	
	// 验证指标存在
	requiredKeys := []string{
		"total_executions", "total_matches", "error_count", "retry_count",
		"match_rate", "error_rate", "avg_exec_time_ms", "max_exec_time_ms",
		"min_exec_time_ms", "throughput_per_sec", "memory_usage_mb",
		"recent_executions", "recent_matches", "recent_errors",
		"last_executed", "last_matched",
	}
	
	for _, key := range requiredKeys {
		if _, exists := metrics[key]; !exists {
			t.Errorf("性能指标中缺少键: %s", key)
		}
	}
	
	// 验证一些具体值
	if metrics["total_executions"] != 2 {
		t.Errorf("总执行次数应该为2，实际为%v", metrics["total_executions"])
	}
	
	if metrics["total_matches"] != 1 {
		t.Errorf("总匹配次数应该为1，实际为%v", metrics["total_matches"])
	}
	
	if metrics["error_count"] != 1 {
		t.Errorf("错误次数应该为1，实际为%v", metrics["error_count"])
	}
}

// TestFilterRecentTimes 测试时间过滤函数
func TestFilterRecentTimes(t *testing.T) {
	now := time.Now()
	cutoff := now.Add(-time.Minute)
	
	times := []time.Time{
		now.Add(-2 * time.Minute), // 应该被过滤掉
		now.Add(-30 * time.Second), // 应该保留
		now,                        // 应该保留
	}
	
	filtered := filterRecentTimes(times, cutoff)
	
	if len(filtered) != 2 {
		t.Errorf("过滤后应该有2个时间，实际为%d", len(filtered))
	}
	
	// 验证过滤后的时间都在cutoff之后
	for _, timeVal := range filtered {
		if !timeVal.After(cutoff) {
			t.Errorf("过滤后的时间%v应该在cutoff时间%v之后", timeVal, cutoff)
		}
	}
}

// TestTriggerManagerPerformanceReport 测试触发器管理器性能报告
func TestTriggerManagerPerformanceReport(t *testing.T) {
	// 这个测试需要一个简单的TriggerManager实例
	tm := &TriggerManager{
		Stats: make(map[string]*TriggerStats),
	}
	
	// 添加一些测试统计数据
	stats1 := NewTriggerStats()
	stats1.UpdateStats(true, 100*time.Millisecond, false)
	tm.Stats["trigger1"] = stats1
	
	stats2 := NewTriggerStats()
	stats2.UpdateStats(false, 50*time.Millisecond, true)
	tm.Stats["trigger2"] = stats2
	
	// 获取性能报告
	report := tm.GetPerformanceReport()
	
	// 验证报告结构
	if _, exists := report["system"]; !exists {
		t.Error("性能报告应该包含系统级别的指标")
	}
	
	if _, exists := report["trigger1"]; !exists {
		t.Error("性能报告应该包含trigger1的指标")
	}
	
	if _, exists := report["trigger2"]; !exists {
		t.Error("性能报告应该包含trigger2的指标")
	}
	
	// 测试详细报告
	detailedReport := tm.GetDetailedPerformanceReport()
	
	if _, exists := detailedReport["aggregate"]; !exists {
		t.Error("详细性能报告应该包含聚合统计")
	}
}
