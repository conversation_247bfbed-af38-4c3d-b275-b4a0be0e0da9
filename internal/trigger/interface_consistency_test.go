package trigger

import (
	"net/http"
	"regexp"
	"testing"
	"time"

	"github.com/mubeng/mubeng/common"
)

// TestTriggerInterfaceConsistency 测试触发器接口一致性
func TestTriggerInterfaceConsistency(t *testing.T) {
	// 创建测试用的HTTP请求和响应
	req, _ := http.NewRequest("GET", "http://example.com/test", nil)
	resp := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
	}
	reqTime := 100 * time.Millisecond

	// 测试所有触发器类型都实现了统一接口
	triggers := []Trigger{
		&StatusTrigger{
			Codes:        []int{200},
			Priority:     1,
			ProcessStage: PreRequest,
			Actions: []common.ActionConfig{
				{Type: "log", Params: map[string]interface{}{"message": "status test"}},
			},
		},
		&BodyTrigger{
			Patterns:     []*regexp.Regexp{regexp.MustCompile("test")},
			Priority:     1,
			ProcessStage: PreRequest,
			Actions: []common.ActionConfig{
				{Type: "log", Params: map[string]interface{}{"message": "body test"}},
			},
		},
		&MaxRequestTimeTrigger{
			MaxTime:      50, // 50ms，小于测试的100ms
			Priority:     1,
			ProcessStage: PreRequest,
			Actions: []common.ActionConfig{
				{Type: "log", Params: map[string]interface{}{"message": "max time test"}},
			},
		},
		&URLTrigger{
			Patterns:     []*regexp.Regexp{regexp.MustCompile("example.com")},
			Priority:     1,
			ProcessStage: PreRequest,
			Actions: []common.ActionConfig{
				{Type: "log", Params: map[string]interface{}{"message": "url test"}},
			},
		},
	}

	for i, trigger := range triggers {
		t.Run(getTriggerTypeName(trigger), func(t *testing.T) {
			// 测试基本接口方法
			priority := trigger.GetPriority()
			if priority != 1 {
				t.Errorf("触发器 %d: 期望优先级为1，实际为%d", i, priority)
			}

			stage := trigger.GetProcessStage()
			if stage != PreRequest {
				t.Errorf("触发器 %d: 期望处理阶段为PreRequest，实际为%s", i, stage)
			}

			// 测试GetActions方法
			actions := trigger.GetActions()
			if len(actions) == 0 {
				t.Errorf("触发器 %d: GetActions返回空动作列表", i)
			}

			// 测试GetMatchedActions方法
			matchedActions := trigger.GetMatchedActions(req, resp, reqTime)
			
			// 验证GetMatchedActions的行为一致性
			matched := trigger.Match(req, resp, reqTime)
			if matched && len(matchedActions) == 0 {
				t.Errorf("触发器 %d: 匹配成功但GetMatchedActions返回空列表", i)
			}
			if !matched && len(matchedActions) > 0 {
				t.Errorf("触发器 %d: 匹配失败但GetMatchedActions返回非空列表", i)
			}

			// 验证动作内容的一致性（对于简单触发器）
			if matched {
				expectedActions := trigger.GetActions()
				if len(matchedActions) != len(expectedActions) {
					t.Errorf("触发器 %d: GetMatchedActions返回的动作数量(%d)与GetActions不一致(%d)", 
						i, len(matchedActions), len(expectedActions))
				}
			}
		})
	}
}

// TestEnhancedConditionalTriggerInterface 测试增强条件触发器接口
func TestEnhancedConditionalTriggerInterface(t *testing.T) {
	// 创建测试配置
	config := &common.Config{
		Actions: map[string]common.ActionSequenceName{
			"test_action": {
				Sequence: []common.ActionConfig{
					{Type: "log", Params: map[string]interface{}{"message": "enhanced test"}},
				},
			},
		},
	}

	// 创建增强条件触发器
	enhancedTrigger := &EnhancedConditionalTrigger{
		Priority:     1,
		ProcessStage: PreRequest,
		EventName:    "test_event",
		Config:       config,
		Conditions: map[string]*EnhancedCondition{
			"status_200": {
				Name:   "status_200",
				Enable: true,
				Matcher: func(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
					return resp != nil && resp.StatusCode == 200
				},
			},
		},
		Matches: []*EnhancedMatch{
			{
				Name:   "test_match",
				Enable: true,
				Logic:  "status_200",
				Actions: []common.ActionConfig{
					{Type: "log", Params: map[string]interface{}{"message": "enhanced matched"}},
				},
			},
		},
	}

	// 测试请求和响应
	req, _ := http.NewRequest("GET", "http://example.com/test", nil)
	resp := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
	}
	reqTime := 100 * time.Millisecond

	// 测试接口一致性
	t.Run("EnhancedConditionalTrigger", func(t *testing.T) {
		// 测试基本接口
		if enhancedTrigger.GetPriority() != 1 {
			t.Error("增强条件触发器优先级不正确")
		}

		if enhancedTrigger.GetProcessStage() != PreRequest {
			t.Error("增强条件触发器处理阶段不正确")
		}

		// 测试GetActions方法（应该返回所有可能的动作）
		allActions := enhancedTrigger.GetActions()
		if len(allActions) == 0 {
			t.Error("增强条件触发器GetActions返回空列表")
		}

		// 测试GetMatchedActions方法
		matchedActions := enhancedTrigger.GetMatchedActions(req, resp, reqTime)
		if len(matchedActions) == 0 {
			t.Error("增强条件触发器GetMatchedActions在匹配条件下返回空列表")
		}

		// 验证匹配逻辑
		matched := enhancedTrigger.Match(req, resp, reqTime)
		if !matched {
			t.Error("增强条件触发器应该匹配状态码200")
		}

		// 验证动作内容
		if len(matchedActions) > 0 {
			action := matchedActions[0]
			if action.Type != "log" {
				t.Errorf("期望动作类型为log，实际为%s", action.Type)
			}
		}
	})
}

// TestConditionalTriggerInterface 测试条件触发器接口
func TestConditionalTriggerInterface(t *testing.T) {
	// 创建测试配置
	config := &common.Config{
		Actions: map[string]common.ActionSequenceName{
			"test_sequence": {
				Sequence: []common.ActionConfig{
					{Type: "log", Params: map[string]interface{}{"message": "conditional test"}},
				},
			},
		},
	}

	// 创建条件触发器
	conditionalTrigger := &ConditionalTrigger{
		Priority:     1,
		ProcessStage: PreRequest,
		EventName:    "test_event",
		Config:       config,
		Conditions: []ConditionMatcher{
			{
				Name:               "test_condition",
				ActionSequenceName: "test_sequence",
				Enable:             true,
				Matcher: func(req *http.Request, resp *http.Response, reqTime time.Duration) bool {
					return resp != nil && resp.StatusCode == 200
				},
			},
		},
	}

	// 测试请求和响应
	req, _ := http.NewRequest("GET", "http://example.com/test", nil)
	resp := &http.Response{
		StatusCode: 200,
		Header:     make(http.Header),
	}
	reqTime := 100 * time.Millisecond

	t.Run("ConditionalTrigger", func(t *testing.T) {
		// 测试基本接口
		if conditionalTrigger.GetPriority() != 1 {
			t.Error("条件触发器优先级不正确")
		}

		if conditionalTrigger.GetProcessStage() != PreRequest {
			t.Error("条件触发器处理阶段不正确")
		}

		// 测试GetMatchedActions方法
		matchedActions := conditionalTrigger.GetMatchedActions(req, resp, reqTime)
		if len(matchedActions) == 0 {
			t.Error("条件触发器GetMatchedActions在匹配条件下返回空列表")
		}

		// 验证匹配逻辑
		matched := conditionalTrigger.Match(req, resp, reqTime)
		if !matched {
			t.Error("条件触发器应该匹配状态码200")
		}
	})
}

// TestInterfaceMethodConsistency 测试接口方法的一致性
func TestInterfaceMethodConsistency(t *testing.T) {
	// 创建一个简单的状态触发器
	trigger := &StatusTrigger{
		Codes:        []int{404},
		Priority:     5,
		ProcessStage: PostBody,
		Actions: []common.ActionConfig{
			{Type: "retry", Params: map[string]interface{}{"count": 3}},
		},
	}

	req, _ := http.NewRequest("GET", "http://example.com/test", nil)
	
	// 测试匹配的情况
	respMatch := &http.Response{StatusCode: 404}
	reqTime := 50 * time.Millisecond

	matchedActions := trigger.GetMatchedActions(req, respMatch, reqTime)
	if len(matchedActions) != 1 {
		t.Errorf("匹配时期望1个动作，实际为%d", len(matchedActions))
	}

	// 测试不匹配的情况
	respNoMatch := &http.Response{StatusCode: 200}
	noMatchActions := trigger.GetMatchedActions(req, respNoMatch, reqTime)
	if len(noMatchActions) != 0 {
		t.Errorf("不匹配时期望0个动作，实际为%d", len(noMatchActions))
	}

	// 验证GetActions始终返回相同结果
	actions1 := trigger.GetActions()
	actions2 := trigger.GetActions()
	if len(actions1) != len(actions2) {
		t.Error("GetActions方法返回结果不一致")
	}
}

// getTriggerTypeName 获取触发器类型名称（用于测试）
func getTriggerTypeName(trigger Trigger) string {
	switch trigger.(type) {
	case *StatusTrigger:
		return "StatusTrigger"
	case *BodyTrigger:
		return "BodyTrigger"
	case *MaxRequestTimeTrigger:
		return "MaxRequestTimeTrigger"
	case *URLTrigger:
		return "URLTrigger"
	case *EnhancedConditionalTrigger:
		return "EnhancedConditionalTrigger"
	case *ConditionalTrigger:
		return "ConditionalTrigger"
	default:
		return "UnknownTrigger"
	}
}
