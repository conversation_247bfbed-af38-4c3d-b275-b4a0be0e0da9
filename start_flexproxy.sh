#!/bin/bash

# FlexProxy 启动脚本
# 解决 "没有可执行的操作" 错误

echo "启动 FlexProxy 代理服务器..."
echo "配置文件: config.yaml"
echo "代理文件: proxies.txt"
echo "监听地址: 127.0.0.1:8080"
echo ""

# 检查配置文件是否存在
if [ ! -f "config.yaml" ]; then
    echo "错误: 配置文件 config.yaml 不存在"
    exit 1
fi

# 检查代理文件是否存在
if [ ! -f "proxies.txt" ]; then
    echo "错误: 代理文件 proxies.txt 不存在"
    exit 1
fi

# 启动 FlexProxy
# -a: 指定监听地址和端口
# -f: 指定代理文件
# -config: 指定配置文件
# -v: 启用详细日志
go run main.go -a 127.0.0.1:8080 -f proxies.txt -config config.yaml -v