package main

import (
	"fmt"
	"log"
	"os"

	"github.com/mubeng/mubeng/common/logger"
	"github.com/mubeng/mubeng/internal/certificate"
)

func main() {
	fmt.Println("=== FlexProxy 服务器启动测试 ===")

	// 测试证书管理器初始化
	fmt.Println("1. 测试证书管理器初始化...")
	certConfig := &certificate.Config{
		CertDir:     "./test_server_certs",
		AutoInstall: false, // 测试模式不自动安装
	}
	
	certLogger := logger.GetLogger("CERT_TEST")
	certManager, err := certificate.NewCertificateManager(certConfig, certLogger)
	if err != nil {
		log.Printf("⚠️  证书管理器初始化失败: %v", err)
	} else {
		fmt.Println("✅ 证书管理器初始化成功")
		
		// 测试证书生成
		fmt.Println("2. 测试证书生成...")
		testDomains := []string{"example.com", "test.local"}
		for _, domain := range testDomains {
			cert, err := certManager.GetCertificate(domain)
			if err != nil {
				fmt.Printf("❌ 为域名 %s 生成证书失败: %v\n", domain, err)
			} else {
				fmt.Printf("✅ 为域名 %s 生成证书成功\n", domain)
				_ = cert // 使用证书变量避免未使用警告
			}
		}
	}

	// 测试启动检查器
	if certManager != nil {
		fmt.Println("3. 测试启动检查器...")
		checker := certificate.NewStartupChecker(certManager, certLogger, false)
		if err := checker.CheckAndSetup(); err != nil {
			fmt.Printf("⚠️  启动检查过程中出现问题: %v\n", err)
		} else {
			fmt.Println("✅ 启动检查完成")
		}
	}

	fmt.Println("4. 显示证书信息...")
	if certManager != nil {
		fmt.Printf("📁 证书目录: %s\n", certManager.GetCACertPath())
		
		// 检查证书文件是否存在
		if _, err := os.Stat(certManager.GetCACertPath()); err == nil {
			fmt.Println("✅ CA证书文件已生成")
		} else {
			fmt.Printf("❌ CA证书文件不存在: %v\n", err)
		}
		
		// 显示安装说明
		fmt.Println("\n📖 证书安装说明:")
		instructions := certManager.GetInstallInstructions()
		fmt.Println(instructions)
	}

	// 注意：实际的服务器启动需要完整的配置和代理管理器
	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("📝 注意事项：")
	fmt.Println("   - 证书管理系统已成功初始化")
	fmt.Println("   - 要完整测试服务器启动，需要配置代理管理器")
	fmt.Println("   - 证书文件位于: ./test_server_certs/")
	fmt.Println("   - 可以手动清理测试文件")
	
	// 清理提示
	fmt.Println("\n🧹 清理命令:")
	fmt.Println("   rm -rf ./test_server_certs/")
}
