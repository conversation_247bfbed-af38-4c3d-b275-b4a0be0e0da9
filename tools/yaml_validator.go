package main

import (
	"fmt"
	"io/ioutil"
	"os"

	"gopkg.in/yaml.v2"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run yaml_validator.go <yaml文件>")
		os.Exit(1)
	}

	filename := os.Args[1]
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		fmt.Printf("读取文件错误: %v\n", err)
		os.Exit(1)
	}

	var config interface{}
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		fmt.Printf("YAML解析错误: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ YAML语法正确: %s\n", filename)
}
