package main

import (
	"fmt"
	"log"
	"os"

	"github.com/mubeng/mubeng/common"
)

func main() {
	fmt.Println("=== FlexProxy 配置验证调试程序 ===")

	// 测试配置文件路径
	configFile := "simple_config.yaml"
	if len(os.Args) > 1 {
		configFile = os.Args[1]
	}

	fmt.Printf("正在测试配置文件: %s\n", configFile)

	// 1. 先不验证地加载配置
	fmt.Println("\n1. 加载配置（不验证）...")
	cfg, err := common.LoadConfigFromYAMLWithoutValidation(configFile)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	fmt.Println("✓ 配置加载成功")

	// 2. 单独进行验证
	fmt.Println("\n2. 验证配置...")
	err = common.ValidateConfig(cfg)
	if err != nil {
		fmt.Printf("✗ 配置验证失败: %v\n", err)
		// 打印详细的配置信息
		fmt.Printf("\n=== 当前配置详情 ===\n")
		fmt.Printf("Global.Enable: %v\n", cfg.Global.Enable)
		fmt.Printf("Global.RulePriority: %v\n", cfg.Global.RulePriority)
		fmt.Printf("Global.DefaultProcessStage: '%s'\n", cfg.Global.DefaultProcessStage)
		fmt.Printf("Global.DNSLookupMode: '%s'\n", cfg.Global.DNSLookupMode)
		fmt.Printf("Global.IPRotationMode: '%s'\n", cfg.Global.IPRotationMode)
		fmt.Printf("Global.MinProxyPoolSize: %v\n", cfg.Global.MinProxyPoolSize)
		fmt.Printf("Global.MaxProxyFetchAttempts: %v\n", cfg.Global.MaxProxyFetchAttempts)
		fmt.Printf("Global.DNSCacheTTL: %v\n", cfg.Global.DNSCacheTTL)
		fmt.Printf("Global.IPVersionPriority: '%s'\n", cfg.Global.IPVersionPriority)
		fmt.Printf("Global.DefaultDNSTimeout: %v\n", cfg.Global.DefaultDNSTimeout)
		fmt.Printf("Global.RetryProxyReusePolicy: '%s'\n", cfg.Global.RetryProxyReusePolicy)
		fmt.Printf("Global.RetryProxyCooldownTime: %v\n", cfg.Global.RetryProxyCooldownTime)
	} else {
		fmt.Println("✓ 配置验证成功")
	}

	// 3. 测试完整加载（包含验证）
	fmt.Println("\n3. 完整加载配置（包含验证）...")
	_, err = common.LoadConfigFromYAML(configFile)
	if err != nil {
		fmt.Printf("✗ 完整加载失败: %v\n", err)
	} else {
		fmt.Println("✓ 完整加载成功")
	}

	fmt.Println("\n=== 调试完成 ===")
}
