package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/internal/certificate"
)

func main() {
	fmt.Println("=== FlexProxy 证书管理系统测试 ===")

	// 创建logger
	testLogger := logger.GetLogger("CERTIFICATE_TEST")

	// 创建证书管理器配置
	config := &certificate.Config{
		CertDir:     "./test_certs",
		AutoInstall: false, // 测试模式不自动安装
	}

	// 创建证书管理器
	fmt.Println("1. 初始化证书管理器...")
	certManager, err := certificate.NewCertificateManager(config, testLogger)
	if err != nil {
		log.Fatalf("初始化证书管理器失败: %v", err)
	}
	fmt.Println("✅ 证书管理器初始化成功")

	// 测试CA证书生成
	fmt.Println("\n2. 检查CA证书...")
	caCertPath := certManager.GetCACertPath()
	if _, err := os.Stat(caCertPath); err == nil {
		fmt.Printf("✅ CA证书已存在: %s\n", caCertPath)
	} else {
		fmt.Printf("❌ CA证书不存在: %v\n", err)
	}

	// 测试域名证书生成
	fmt.Println("\n3. 测试域名证书生成...")
	testDomains := []string{
		"example.com",
		"google.com",
		"github.com",
		"127.0.0.1",
	}

	for _, domain := range testDomains {
		cert, err := certManager.GetCertificate(domain)
		if err != nil {
			fmt.Printf("❌ 为域名 %s 生成证书失败: %v\n", domain, err)
		} else {
			fmt.Printf("✅ 为域名 %s 生成证书成功 (证书链长度: %d)\n", domain, len(cert.Certificate))
		}
	}

	// 测试证书安装检查
	fmt.Println("\n4. 检查证书安装状态...")
	installed, err := certManager.CheckCertificateInstallation()
	if err != nil {
		fmt.Printf("⚠️  检查证书安装状态失败: %v\n", err)
	} else if installed {
		fmt.Println("✅ CA证书已安装到系统")
	} else {
		fmt.Println("❌ CA证书未安装到系统")
	}

	// 测试启动检查器
	fmt.Println("\n5. 测试启动检查器...")
	checker := certificate.NewStartupChecker(certManager, testLogger, false) // 非交互模式
	if err := checker.CheckAndSetup(); err != nil {
		fmt.Printf("⚠️  启动检查过程中出现问题: %v\n", err)
	} else {
		fmt.Println("✅ 启动检查完成")
	}

	// 生成安装脚本
	fmt.Println("\n6. 生成安装脚本...")
	scriptPath, err := certManager.GenerateInstallScript()
	if err != nil {
		fmt.Printf("❌ 生成安装脚本失败: %v\n", err)
	} else {
		fmt.Printf("✅ 安装脚本已生成: %s\n", scriptPath)
	}

	// 显示安装说明
	fmt.Println("\n7. 安装说明:")
	fmt.Println(strings.Repeat("=", 50))
	instructions := certManager.GetInstallInstructions()
	fmt.Println(instructions)

	// 显示证书信息
	fmt.Println("\n8. 证书信息:")
	fmt.Println(strings.Repeat("=", 50))
	checker.ShowBrowserSpecificInstructions()

	fmt.Println("\n=== 测试完成 ===")
	fmt.Println("📁 测试证书目录: ./test_certs")
	fmt.Println("🔧 如需清理测试文件，请手动删除 ./test_certs 目录")
}
