package main

import (
	"fmt"
	"log"
	"os"

	"gopkg.in/yaml.v3"
)

type Event struct {
	Name                string                   `yaml:"name"`
	Enable              bool                     `yaml:"enable"`
	Conditions          []map[string]interface{} `yaml:"conditions"`
	Matches             []map[string]interface{} `yaml:"matches"`
	ConditionalActions  []map[string]interface{} `yaml:"conditional_actions"`
}

type Config struct {
	Events []Event `yaml:"events"`
}

func main() {
	if len(os.Args) < 2 {
		log.Fatal("用法: go run debug_events.go <config.yaml>")
	}

	filename := os.Args[1]
	data, err := os.ReadFile(filename)
	if err != nil {
		log.Fatalf("读取文件失败: %v", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		log.Fatalf("解析YAML失败: %v", err)
	}

	fmt.Printf("=== 事件验证调试 ===\n")
	fmt.Printf("总事件数: %d\n\n", len(config.Events))

	for i, event := range config.Events {
		fmt.Printf("事件 %d: %s\n", i, event.Name)
		fmt.Printf("  启用: %t\n", event.Enable)
		fmt.Printf("  条件数: %d\n", len(event.Conditions))
		fmt.Printf("  匹配数: %d\n", len(event.Matches))
		fmt.Printf("  条件动作数: %d\n", len(event.ConditionalActions))
		
		// 检查是否有条件或匹配规则
		hasConditionsOrMatches := len(event.Conditions) > 0 || len(event.Matches) > 0 || len(event.ConditionalActions) > 0
		if !hasConditionsOrMatches {
			fmt.Printf("  ❌ 错误: 缺少条件或匹配规则\n")
		} else {
			fmt.Printf("  ✅ 正常: 有条件或匹配规则\n")
		}
		fmt.Println()
	}
}
