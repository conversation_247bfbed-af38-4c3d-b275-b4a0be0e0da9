package main

import (
	"fmt"
	"os"

	"github.com/flexp/flexp/common"
	"gopkg.in/yaml.v3"
)

func main() {
	fmt.Println("开始YAML配置文件调试...")

	// 读取YAML文件
	yamlFile, err := os.ReadFile("simple_config.yaml")
	if err != nil {
		fmt.Printf("读取文件失败: %v\n", err)
		return
	}
	fmt.Println("✓ YAML文件读取成功")

	// 尝试解析为通用map
	var genericConfig map[string]interface{}
	err = yaml.Unmarshal(yamlFile, &genericConfig)
	if err != nil {
		fmt.Printf("✗ 解析为通用map失败: %v\n", err)
		return
	}
	fmt.Println("✓ YAML语法检查通过")

	// 尝试解析为Config结构体
	var cfg common.Config
	err = yaml.Unmarshal(yamlFile, &cfg)
	if err != nil {
		fmt.Printf("✗ Config结构体解析失败: %v\n", err)
		return
	}
	fmt.Println("✓ Config结构体解析成功")

	// 检查基本字段
	fmt.Printf("Global配置启用状态: %v\n", cfg.Global.Enable)

	if cfg.Server != nil {
		fmt.Println("✓ Server配置存在")
	} else {
		fmt.Println("✗ Server配置不存在")
	}

	if cfg.Proxy != nil {
		fmt.Println("✓ Proxy配置存在")
	} else {
		fmt.Println("✗ Proxy配置不存在")
	}

	// 尝试验证配置
	fmt.Println("\n开始配置验证...")
	validator := common.NewConfigValidator()
	err = validator.ValidateConfig(&cfg)
	if err != nil {
		fmt.Printf("✗ 配置验证失败: %v\n", err)
		return
	}
	fmt.Println("✓ 配置验证成功")

	fmt.Println("\n所有测试通过！")
}
