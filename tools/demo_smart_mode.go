package main

import (
	"context"
	"fmt"
	"time"

	"github.com/flexp/flexp/internal/strategy"
)

func main() {
	fmt.Println("=== FlexProxy 智能模式演示 ===")
	fmt.Println()

	// 创建智能策略实例
	smartStrategy := strategy.NewSmartProxyStrategy()
	proxies := []string{
		"proxy1.example.com:8080",
		"proxy2.example.com:8080", 
		"proxy3.example.com:8080",
		"proxy4.example.com:8080",
	}

	fmt.Printf("可用代理列表: %v\n", proxies)
	fmt.Println()

	// 场景1: 正常请求（无重试标记）
	fmt.Println("--- 场景1: 正常请求 ---")
	ctx1 := context.Background()
	demonstrateProxySelection(smartStrategy, ctx1, proxies, "正常请求")

	// 场景2: 使用新代理重试
	fmt.Println("\n--- 场景2: 使用新代理重试 ---")
	ctx2 := context.WithValue(context.Background(), "retry_action_type", "retry")
	ctx2 = context.WithValue(ctx2, "action_requires_retry", true)
	demonstrateProxySelection(smartStrategy, ctx2, proxies, "新代理重试")

	// 场景3: 使用相同代理重试
	fmt.Println("\n--- 场景3: 使用相同代理重试 ---")
	ctx3 := context.WithValue(context.Background(), "retry_action_type", "retry_same")
	ctx3 = context.WithValue(ctx3, "action_requires_retry", true)
	demonstrateProxySelection(smartStrategy, ctx3, proxies, "相同代理重试")

	// 场景4: 连续请求展示智能切换
	fmt.Println("\n--- 场景4: 连续请求展示智能切换 ---")
	demonstrateContinuousRequests(smartStrategy, proxies)

	// 场景5: 代理质量指标更新
	fmt.Println("\n--- 场景5: 代理质量指标更新 ---")
	demonstrateMetricsUpdate(smartStrategy)

	fmt.Println("\n=== 演示完成 ===")
}

func demonstrateProxySelection(strategy *strategy.SmartProxyStrategy, ctx context.Context, proxies []string, scenario string) {
	fmt.Printf("场景: %s\n", scenario)
	
	// 连续选择3次，观察行为
	for i := 1; i <= 3; i++ {
		proxy, err := strategy.SelectProxy(ctx, proxies, "example.com")
		if err != nil {
			fmt.Printf("  第%d次选择失败: %v\n", i, err)
		} else {
			fmt.Printf("  第%d次选择: %s\n", i, proxy)
		}
		time.Sleep(100 * time.Millisecond)
	}
}

func demonstrateContinuousRequests(strategy *strategy.SmartProxyStrategy, proxies []string) {
	scenarios := []struct {
		name        string
		contextFunc func() context.Context
	}{
		{
			name: "正常请求",
			contextFunc: func() context.Context {
				return context.Background()
			},
		},
		{
			name: "新代理重试",
			contextFunc: func() context.Context {
				ctx := context.WithValue(context.Background(), "retry_action_type", "retry")
				return context.WithValue(ctx, "action_requires_retry", true)
			},
		},
		{
			name: "相同代理重试",
			contextFunc: func() context.Context {
				ctx := context.WithValue(context.Background(), "retry_action_type", "retry_same")
				return context.WithValue(ctx, "action_requires_retry", true)
			},
		},
		{
			name: "正常请求",
			contextFunc: func() context.Context {
				return context.Background()
			},
		},
		{
			name: "新代理重试",
			contextFunc: func() context.Context {
				ctx := context.WithValue(context.Background(), "retry_action_type", "retry")
				return context.WithValue(ctx, "action_requires_retry", true)
			},
		},
	}

	fmt.Println("连续请求序列:")
	for i, scenario := range scenarios {
		ctx := scenario.contextFunc()
		proxy, err := strategy.SelectProxy(ctx, proxies, "example.com")
		if err != nil {
			fmt.Printf("  请求%d (%s): 失败 - %v\n", i+1, scenario.name, err)
		} else {
			fmt.Printf("  请求%d (%s): %s\n", i+1, scenario.name, proxy)
		}
		time.Sleep(200 * time.Millisecond)
	}
}

func demonstrateMetricsUpdate(strategy *strategy.SmartProxyStrategy) {
	fmt.Println("更新代理质量指标:")
	
	// 模拟不同代理的性能数据
	metricsData := []struct {
		proxy        string
		success      bool
		responseTime time.Duration
		domain       string
	}{
		{"proxy1.example.com:8080", true, 100 * time.Millisecond, "example.com"},
		{"proxy2.example.com:8080", false, 500 * time.Millisecond, "example.com"},
		{"proxy3.example.com:8080", true, 150 * time.Millisecond, "example.com"},
		{"proxy4.example.com:8080", true, 80 * time.Millisecond, "example.com"},
		{"proxy1.example.com:8080", true, 120 * time.Millisecond, "test.com"},
		{"proxy2.example.com:8080", true, 200 * time.Millisecond, "test.com"},
	}

	for _, data := range metricsData {
		strategy.UpdateMetrics(data.proxy, data.success, data.responseTime, data.domain)
		status := "成功"
		if !data.success {
			status = "失败"
		}
		fmt.Printf("  更新 %s (域名: %s): %s, 响应时间: %v\n", 
			data.proxy, data.domain, status, data.responseTime)
	}

	fmt.Println("指标更新完成，后续选择将考虑这些质量数据")
}

// 辅助函数：展示上下文信息
func showContextInfo(ctx context.Context) {
	if retryType, ok := ctx.Value("retry_action_type").(string); ok {
		fmt.Printf("  上下文信息 - 重试类型: %s", retryType)
	}
	if requiresRetry, ok := ctx.Value("action_requires_retry").(bool); ok {
		fmt.Printf(", 需要重试: %v", requiresRetry)
	}
	fmt.Println()
}
