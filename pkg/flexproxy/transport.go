package flexproxy

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strings"

	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
	"github.com/flexp/flexp/common/logger"
	"github.com/flexp/flexp/pkg/helper/awsurl"
	"h12.io/socks"
)

// 模块级别的日志器
var transportLogger = logger.GetTransportLogger()

// Transport 自动在 HTTP/S 或 SOCKS v4(A) & v5 代理之间切换传输。
//
// 根据协议方案，返回带有 http.Transport.Dialer 或 http.Transport.Proxy 的 http.Transport 值。
// 如果协议方案是 "aws"，将返回默认的 http.Transport。
func Transport(p string, dnsMode string, reverseDNSResolver interface{}, httpProxyDNS string) (*http.Transport, error) {
	var proxyURL *url.URL
	var err error
	// socks5 服务器只支持 socks 代理
	if ServerType == "socks5" && !strings.HasPrefix(p, constants.SOCKS5Prefix) {
		return nil, errors.NewErrorWithDetails(errors.ErrTypeProxy, errors.ErrCodeProxyProtocolMismatch, "SOCKS5服务器只支持SOCKS代理", "代理: "+p+", 服务器类型: "+ServerType)
	}
	tr := new(http.Transport)

	// 解析代理URL
	if !awsurl.IsURL(p) {
		proxyURL, err = url.Parse(p)
		if err != nil {
			return nil, err
		}
	}

	// 设置DNS解析器
	if dnsMode == "remote" && proxyURL != nil {
		// 根据代理类型配置远程DNS解析
		switch proxyURL.Scheme {
		case "http", "https":
			// 使用HTTP代理进行DNS解析
			tr.Proxy = http.ProxyURL(proxyURL)

			// 应用HTTPProxyDNS配置
			if httpProxyDNS != "" {
				// 解析HTTPProxyDNS配置
				dnsConfig := parseHTTPProxyDNS(httpProxyDNS)

				// 创建自定义的DialContext，应用DNS配置
				tr.DialContext = func(ctx context.Context, network, address string) (net.Conn, error) {
					host, port, err := net.SplitHostPort(address)
					if err != nil {
						return nil, err
					}

					// 检查是否需要应用DNS配置
					if newHost, ok := dnsConfig[host]; ok {
						address = net.JoinHostPort(newHost, port)
					}

					// 使用默认拨号器
					return (&net.Dialer{}).DialContext(ctx, network, address)
				}
			} else {
				// 确保通过代理解析DNS
				tr.DialContext = func(ctx context.Context, network, address string) (net.Conn, error) {
					// 这里不需要手动解析，因为HTTP代理会处理DNS解析
					return (&net.Dialer{}).DialContext(ctx, network, address)
				}
			}
		case "socks4", "socks4a", "socks5":
			// 使用SOCKS代理进行DNS解析
			// nolint: staticcheck
			tr.Dial = socks.Dial(p)
		default:
			return nil, errors.WrapErrorWithDetails(ErrUnsupportedProxyProtocolScheme, errors.ErrTypeProxy, errors.ErrCodeProxyProtocolUnsupported, "不支持的代理协议", "协议: "+proxyURL.Scheme)
		}
	} else {
		// 默认本地解析
		tr.DialContext = (&net.Dialer{}).DialContext

		// 如果是HTTP/HTTPS代理，设置Proxy
		if proxyURL != nil {
			switch proxyURL.Scheme {
			case "socks4", "socks4a", "socks5":
				// nolint: staticcheck
				tr.Dial = socks.Dial(p)
			case "http", "https":
				tr.Proxy = http.ProxyURL(proxyURL)
			}
		}
	}

	// 添加反向DNS解析支持
	if reverseDNSResolver != nil {
		// 检查是否是"no"模式的反向DNS解析器
		isNoMode := false

		// 安全地检查反向DNS解析器的模式
		if rdnsResolver, ok := reverseDNSResolver.(interface{ GetMode() string }); ok {
			// 使用类型断言检查是否成功
			mode := rdnsResolver.GetMode()
			if mode == "no" {
				isNoMode = true
				transportLogger.Debug("反向DNS解析模式为'no'，跳过反向DNS解析")
			}
		} else {
			// 如果类型断言失败，记录日志并默认不进行反向DNS解析
			transportLogger.Debug("反向DNS解析器不支持GetMode方法，默认跳过反向DNS解析")
			isNoMode = true
		}

		// 只有在非"no"模式下才进行反向DNS解析
		if !isNoMode {
			// 保存原始的DialContext
			originalDialContext := tr.DialContext

			// 创建新的DialContext，添加反向DNS解析
			tr.DialContext = func(ctx context.Context, network, address string) (net.Conn, error) {
				// 先使用原始的DialContext建立连接
				conn, err := originalDialContext(ctx, network, address)
				if err != nil {
					return nil, err
				}

				// 如果是TCP连接，尝试进行反向DNS解析
				if tcpConn, ok := conn.(*net.TCPConn); ok {
					remoteAddr := tcpConn.RemoteAddr().(*net.TCPAddr)
					if resolver, ok := reverseDNSResolver.(interface {
						LookupAddr(ctx context.Context, addr string) ([]string, error)
					}); ok {
						// 异步进行反向DNS解析，不阻塞连接
						go func() {
							// 添加恢复机制，防止panic
							defer func() {
								if r := recover(); r != nil {
									transportLogger.Error(fmt.Sprintf("反向DNS解析崩溃: %v", r))
								}
							}()

							// 确保resolver不为nil
							if resolver == nil {
								transportLogger.Warn("反向DNS解析器为空，跳过解析")
								return
							}

							names, err := resolver.LookupAddr(ctx, remoteAddr.IP.String())
							if err == nil && len(names) > 0 {
								// 记录解析结果
								transportLogger.GetRawLogger().Debugf("反向DNS解析结果: %s -> %v", remoteAddr.IP.String(), names)
							} else if err != nil {
								transportLogger.GetRawLogger().Debugf("反向DNS解析失败: %v", err)
							}
						}()
					}
				}

				return conn, nil
			}
		}
	}

	// AWS URL处理
	if awsurl.IsURL(p) {
		return tr, errors.WrapErrorWithDetails(ErrUnsupportedProxyProtocolScheme, errors.ErrTypeProxy, errors.ErrCodeProxyProtocolUnsupported, "AWS协议不支持作为代理传输", "URL: "+p)
	}

	tr.DisableKeepAlives = true
	tr.TLSClientConfig = &tls.Config{
		InsecureSkipVerify: true,
		MinVersion:         tls.VersionTLS10,
		CipherSuites:       getUnsafeCipherSuites(),
	}

	return tr, nil
}

// 解析HTTPProxyDNS配置
func parseHTTPProxyDNS(config string) map[string]string {
	result := make(map[string]string)
	if config == "" {
		return result
	}

	// 检查是否是域名或URL格式
	if !strings.Contains(config, "=") && !strings.Contains(config, ",") {
		// 如果是简单域名或URL，返回空映射（将在RemoteDNSResolver中处理）
		return result
	}

	// 格式：host1=ip1,host2=ip2
	entries := strings.Split(config, ",")
	for _, entry := range entries {
		parts := strings.SplitN(entry, "=", 2)
		if len(parts) == 2 {
			host := strings.TrimSpace(parts[0])
			ip := strings.TrimSpace(parts[1])
			if host != "" && ip != "" {
				result[host] = ip
			}
		}
	}

	return result
}
