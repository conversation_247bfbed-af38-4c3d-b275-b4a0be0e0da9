package flexproxy

import "crypto/tls"

// getUnsafeCipherSuites 返回 Go 标准库认为不安全的所有密码套件列表。
// 这包括所有未包含在默认密码套件列表中的密码套件。
func getUnsafeCipherSuites() []uint16 {
	// 复制自: https://github.com/projectdiscovery/nuclei/pull/4753/files
	unsafeCipherSuites := make([]uint16, 0, len(tls.InsecureCipherSuites())+len(tls.CipherSuites()))
	for _, suite := range tls.InsecureCipherSuites() {
		unsafeCipherSuites = append(unsafeCipherSuites, suite.ID)
	}
	for _, suite := range tls.CipherSuites() {
		unsafeCipherSuites = append(unsafeCipherSuites, suite.ID)
	}

	return unsafeCipherSuites
}
