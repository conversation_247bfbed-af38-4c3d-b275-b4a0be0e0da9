package awsurl

import (
	"context"
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/flexp/flexp/common/constants"
	"github.com/flexp/flexp/common/errors"
)

// URL 表示包含凭证和区域的已解析 AWS URL
type URL struct {
	AccessKeyID     string
	SecretAccessKey string
	Region          string
}

// Parse 解析以下格式的 AWS URL：
// - aws://ACCESS_KEY_ID:SECRET_ACCESS_KEY@region
//
// 支持凭证和区域周围的可选引号。
func Parse(rawURL string) (*URL, error) {
	// 检查前缀
	if !strings.HasPrefix(rawURL, constants.AWSURLPrefix) {
		return nil, errors.WrapErrorWithDetails(nil, errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "AWS URL必须以"+constants.AWSURLPrefix+"开头", fmt.Sprintf("url: %s", rawURL))
	}

	// 移除前缀
	urlWithoutPrefix := strings.TrimPrefix(rawURL, constants.AWSURLPrefix)

	// 分割为凭证和区域部分
	parts := strings.Split(urlWithoutPrefix, constants.CredentialsSeparator)
	if len(parts) != 2 {
		return nil, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "AWS URL缺少"+constants.CredentialsSeparator+"分隔符", fmt.Sprintf("url: %s", rawURL))
	}

	credentials := parts[0]

	// 提取区域并移除其后的任何路径
	region := parts[1]
	if idx := strings.Index(region, "/"); idx != -1 {
		region = region[:idx]
	}

	// 解析凭证
	var accessKeyID, secretAccessKey string
	credParts := strings.Split(credentials, constants.CredentialsDelimiter)
	if len(credParts) != 2 {
		return nil, errors.NewErrorWithDetails(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "AWS凭证格式无效，必须是ACCESS_KEY_ID"+constants.CredentialsDelimiter+"SECRET_ACCESS_KEY格式", fmt.Sprintf("credentials: %s", credentials))
	}

	// 移除凭证的引号并修剪空格
	accessKeyID = strings.TrimSpace(unquote(credParts[0]))
	secretAccessKey = strings.TrimSpace(unquote(credParts[1]))

	// 移除区域的引号并修剪空格
	region = strings.TrimSpace(unquote(region))

	// 验证各部分
	if accessKeyID == "" {
		return nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "ACCESS_KEY_ID不能为空")
	}
	if secretAccessKey == "" {
		return nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "SECRET_ACCESS_KEY不能为空")
	}
	if region == "" {
		return nil, errors.NewError(errors.ErrTypeValidation, errors.ErrCodeValidationFailed, "region不能为空")
	}

	return &URL{
		AccessKeyID:     accessKeyID,
		SecretAccessKey: secretAccessKey,
		Region:          region,
	}, nil
}

// IsURL 检查字符串是否为 AWS URL
func IsURL(s string) bool {
	return strings.HasPrefix(s, constants.AWSURLPrefix)
}

// IsValidURL 检查字符串是否为有效的 AWS URL
func IsValidURL(s string) bool {
	_, err := Parse(s)
	return err == nil
}

// String 返回 URL 的字符串表示
func (u *URL) String() string {
	return fmt.Sprintf("aws://%s:%s@%s", u.AccessKeyID, u.SecretAccessKey, u.Region)
}

// Credentials 返回 URL 的 AWS 凭证
func (u *URL) Credentials(session string) (aws.Credentials, error) {
	creds := credentials.NewStaticCredentialsProvider(u.AccessKeyID, u.SecretAccessKey, session)

	return creds.Retrieve(context.TODO())
}
