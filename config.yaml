# FlexProxy 配置文件
# 这是一个全面的代理应用配置模板，包含详细的中文注释说明

# ============================================================================
# 全局配置 (Global Configuration)
# ============================================================================
global:
  # 是否启用代理服务
  # 值: true/false
  # 说明: 控制整个代理服务的开关状态
  enable: true

  # 代理文件路径
  # 值: 文件路径字符串
  # 说明: 指定包含代理服务器列表的文件路径，支持相对路径和绝对路径
  # 示例: "./proxies.txt", "/etc/flexproxy/proxies.txt"
  proxy_file: "./proxies.txt"

  # 全局封禁IP配置列表
  # 说明: 配置需要全局封禁的IP地址及其封禁时长
  global_banned_ips:
    # IP地址: 需要封禁的IP地址
    # duration: 封禁时长，支持三种格式:
    #   1. 整数(秒数): 如 3600 表示3600秒
    #   2. Go时间格式字符串: 如 "1h30m", "300ms", "2s"
    #   3. 特殊值 "reboot": 重启后解封(永久封禁)
    - ip: "*************"
      duration: 3600  # 封禁1小时 (3600秒)
    - ip: "*********"
      duration: "1h30m"  # 封禁1小时30分钟 (Go duration格式)
    - ip: "***********"
      duration: "reboot"  # 重启后解封(永久封禁)

  # 全局封禁域名配置列表
  # 说明: 配置需要全局封禁的域名及其封禁时长
  banned_domains:
    # domain: 需要封禁的域名 (必须是有效的FQDN格式)
    # duration: 封禁时长，支持三种格式:
    #   1. 整数(秒数): 如 86400 表示86400秒
    #   2. Go时间格式字符串: 如 "24h", "30m", "2h15m"
    #   3. 特殊值 "reboot": 重启后解封(永久封禁)
    - domain: "malicious-site.com"
      duration: 86400  # 封禁24小时 (86400秒)
    - domain: "spam-domain.net"
      duration: "24h"  # 封禁24小时 (Go duration格式)
    - domain: "blocked-forever.com"
      duration: "reboot"  # 重启后解封(永久封禁)

  # 阻止访问的IP地址列表
  # 值: IP地址字符串数组
  # 说明: 直接阻止这些IP地址的所有请求，必须是有效的IP地址格式
  blocked_ips:
    - "***********"
    - "************"
    - "*********"

  # 信任的IP地址列表
  # 值: IP地址字符串数组
  # 说明: 这些IP地址的请求将被优先处理或跳过某些检查
  # 注意: 必须是单个IP地址，不支持CIDR表示法（如***********/24）
  # 如需支持IP段，请列出具体的IP地址
  trusted_ips:
    - "127.0.0.1"      # 本地回环地址IPv4
    - "::1"            # 本地回环地址IPv6
    - "***********"    # 示例：内网网关IP
    - "********"       # 示例：内网IP

  # 排除的URL模式列表
  # 值: 字符串数组
  # 说明: 匹配这些模式的URL将不会通过代理处理
  # 支持正则表达式和通配符模式
  excluded_patterns:
    - "*.local"
    - "localhost:*"
    - "127.0.0.1:*"
    - "*.internal.company.com"

  # 排除范围
  # 值: 字符串
  # 说明: 定义排除规则的应用范围和匹配方式
  # 可选值:
  #   - "extension": 仅匹配文件扩展名
  #   - "domain": 仅匹配域名
  #   - "url": 仅匹配完整URL
  #   - "all": 尝试所有匹配方式（URL、域名、扩展名）
  #   - 其他值: 默认行为，等同于"all"
  # 推荐值: "all" (最全面的匹配方式)
  excluded_scope: "all"

  # 规则优先级
  # 值: 0-100的整数
  # 说明: 定义规则处理的优先级，数值越高优先级越高
  # 0=最低优先级, 100=最高优先级
  rule_priority: 50

  # 默认处理阶段
  # 值: "pre", "post_header", "post_body"
  # 说明: 定义默认的请求处理阶段
  # - pre: 请求前处理
  # - post_header: 响应头处理
  # - post_body: 响应体处理
  default_process_stage: "pre"

  # DNS查找模式
  # 值: "system", "custom", "hybrid"
  # 说明: 定义DNS解析的方式
  # 注意: 代码中实际使用的常量是 "local", "remote", "custom"
  # 但验证标签要求 "system", "custom", "hybrid"，这里使用验证标签支持的值
  # - system: 使用系统DNS (对应代码中的"local")
  # - custom: 使用自定义DNS服务器
  # - hybrid: 混合模式，优先使用自定义DNS，失败时回退到系统DNS
  dns_lookup_mode: "custom"

  # 反向DNS查找
  # 值: "no", "dns", "file:/path/to/hosts.txt", 或直接映射值
  # 说明: 配置反向DNS查找的行为
  # - "no": 不进行反向DNS解析
  # - "dns": 使用系统DNS进行反向解析
  # - "file:/path/to/hosts.txt": 从文件加载IP到域名的映射 (格式: ******* example.com)
  # - 直接映射: "******* example.com, ******* another.com" (逗号分隔多个映射)
  reverse_dns_lookup: "dns"

  # 自定义DNS服务器配置列表
  # 说明: 配置自定义DNS服务器的详细参数
  # ✅ 更新说明:
  # - server字段现在支持两种格式：纯IP地址（如"*******"）或IP:端口格式（如"*******:53"）
  # - 验证器已更新为：validate:"required,ip_or_ip_port"
  # - 如果不指定端口，将根据protocol字段使用默认端口
  # - HTTPProxyDNS字段同样支持 "IP:端口" 格式
  custom_dns_servers:
    # 主要DNS服务器 - Cloudflare（纯IP地址格式）
    - server: "*******"  # 纯IP地址，使用协议默认端口
      protocol: "udp"    # 协议: udp, tcp, doh, dot (注意: 验证标签要求 "udp tcp doh dot")
      timeout: 5000      # 超时时间(毫秒): 1000-30000
      priority: 1        # 优先级: 0-100, 数值越小优先级越高
      tags: ["cloudflare", "primary"]


  # HTTP代理DNS设置
  # 值: DNS服务器地址字符串
  # 说明: 指定HTTP代理使用的DNS服务器，支持DoH (DNS over HTTPS)
  #
  # ✅ 支持的格式:
  # 1. 基本格式: "IP地址" (如 "*******") - 使用默认端口53
  # 2. 端口格式: "IP地址:端口" (如 "*******:5353") - 自定义端口
  # 3. DoH格式: "https://域名/path" (如 "https://*******/dns-query")
  # 4. 复杂格式: "IP:端口@协议,参数" (如 "*******:53@udp,timeout=5000")
  #
  # 示例: "*******:53", "*******:5353", "https://cloudflare-dns.com/dns-query"
  # 留空则使用系统默认DNS
  http_proxy_dns: "*******:53"

  # IP轮换模式
  # 值: "random", "sequential", "quality", "smart"
  # 说明: 定义代理IP的选择策略
  # - random: 随机选择
  # - sequential: 顺序选择
  # - quality: 基于质量评分选择
  # - smart: 智能选择（根据触发动作自动决定是否切换代理）
  ip_rotation_mode: "smart"

  # 最小代理池大小
  # 值: 大于等于1的整数
  # 说明: 维护的最小可用代理数量，低于此数量时会自动补充
  min_proxy_pool_size: 10       

  # 最大代理获取尝试次数
  # 值: 1-10的整数
  # 说明: 获取新代理时的最大尝试次数
  max_proxy_fetch_attempts: 3

  # DNS缓存TTL (生存时间)
  # 值: 大于等于0的整数 (秒)
  # 说明: DNS查询结果的缓存时间，0表示不缓存
  dns_cache_ttl: 300

  # 禁用DNS缓存
  # 值: true/false
  # 说明: 是否完全禁用DNS缓存功能
  dns_no_cache: false

  # IP版本优先级
  # 值: "ipv4", "ipv6", "dual"
  # 说明: 定义IP协议版本的使用优先级
  # - ipv4: 优先使用IPv4
  # - ipv6: 优先使用IPv6
  # - dual: 同时支持IPv4和IPv6
  ip_version_priority: "ipv4"

  # 默认DNS超时时间
  # 值: 1000-30000的整数 (毫秒)
  # 说明: DNS查询的默认超时时间
  default_dns_timeout: 5000

  # 重试代理复用策略
  # 值: "allow", "deny", "cooldown"
  # 说明: 定义失败后重试时是否可以复用相同的代理
  # - allow: 允许立即复用
  # - deny: 禁止复用
  # - cooldown: 冷却时间后可复用
  retry_proxy_reuse_policy: "cooldown"

  # 重试代理冷却时间
  # 值: 大于等于0的整数 (秒)
  # 说明: 代理失败后的冷却时间，在此期间不会被重新使用
  retry_proxy_cooldown_time: 60

  # 重试代理全局跟踪
  # 值: true/false
  # 说明: 是否在全局范围内跟踪代理的重试状态
  # true: 全局跟踪，所有请求共享代理状态
  # false: 独立跟踪，每个请求独立管理代理状态
  retry_proxy_global_tracking: true

# ================================
# 服务器配置
# ================================
# 定义HTTP服务器的基本配置
server:
  host: "0.0.0.0"               # 服务器监听地址
  port: 8080                    # 服务器监听端口: 1-65535
  https_port: 8443              # HTTPS端口: 1-65535
  socks_port: 1080              # SOCKS端口: 1-65535
  read_timeout: "30s"           # 读取超时时间
  write_timeout: "30s"          # 写入超时时间
  idle_timeout: "120s"          # 空闲超时时间
  connect_timeout: "10s"        # 连接超时时间
  max_idle_conns: 100           # 最大空闲连接数: >=0
  max_idle_conns_per_host: 10   # 每个主机最大空闲连接数: >=0
  max_conns_per_host: 50        # 每个主机最大连接数: >=0
  buffer_size: 4096             # 缓冲区大小: >=1024
  max_header_bytes: 1048576     # 最大请求头字节数: >=1024
  debounce_delay: "100ms"       # 防抖延迟

# ================================
# 代理配置
# ================================
# 定义代理服务器的配置
proxy:
  strategy: "random"           # 代理策略: random, sequential, quality, custom
  load_balancer: "round_robin" # 负载均衡器: round_robin, weighted_round_robin, least_connections, response_time, ip_hash
  max_retries: 3               # 最大重试次数: >=0
  retry_interval: "1s"         # 重试间隔
  max_retry_interval: "30s"    # 最大重试间隔
  pool_size: 100               # 代理池大小: >=1
  rotation_interval: 300       # 轮换间隔(秒): >=0

  # 健康检查配置
  health_check:
    enabled: true              # 是否启用健康检查
    interval: "30s"            # 检查间隔
    timeout: "10s"             # 检查超时时间
    path: "/health"            # 健康检查路径
    max_consecutive_failures: 3    # 最大连续失败次数: >=1
    max_consecutive_successes: 2   # 最大连续成功次数: >=1

  # 质量评分配置
  quality_score:
    default: 0.5                      # 默认质量分数: 0-1
    success_rate_weight: 0.6          # 成功率权重: 0-1
    response_time_weight: 0.4         # 响应时间权重: 0-1
    max_failure_rate: 0.3             # 最大失败率: 0-1
    top_proxy_ratio: 0.2              # 顶级代理比例: 0-1
    response_time_baseline: 1000      # 响应时间基线(毫秒): >=0
    smoothing_factor: 0.1             # 平滑因子: 0-1

# ================================
# 缓存配置
# ================================
# 定义缓存系统的配置
cache:
  type: "memory"               # 缓存类型: memory, redis, file
  ttl: "3600s"                 # 默认缓存TTL
  size: 1000                   # 最大缓存条目数: >=1
  cleanup_interval: "300s"     # 清理间隔

  # 缓存键前缀配置
  key_prefixes:
    proxy_list: "proxy:list"
    proxy_status: "proxy:status:"
    user_session: "user:session:"
    rate_limit: "rate:limit:"

  # DNS缓存配置
  dns:
    ttl: "300s"                # DNS缓存TTL
    cleanup_interval: "600s"   # DNS缓存清理间隔

# ================================
# 日志配置
# ================================
# 定义日志系统的配置
logging:
  level: "info"                # 日志级别: debug, info, warn, error, fatal
  format: "json"               # 日志格式: json, text
  file: "./logs/flexproxy.log" # 日志文件路径
  max_size: 100                # 日志文件最大大小(MB): >=1
  max_backups: 10              # 最大备份文件数: >=0
  max_age: 30                  # 日志文件最大保存天数: >=1
  time_format: "2006-01-02T15:04:05.000Z07:00" # 时间格式

# ================================
# 监控配置
# ================================
# 定义监控和指标收集的配置
monitoring:
  enabled: true                # 是否启用监控
  port: 9090                   # 监控端口: 1-65535
  path: "/metrics"             # 指标路径

  # 指标配置
  metrics:
    request_duration: "histogram"    # 请求持续时间指标类型
    request_count: "counter"         # 请求计数指标类型
    proxy_status: "gauge"            # 代理状态指标类型
    cache_hit_rate: "gauge"          # 缓存命中率指标类型

  # 标签配置
  labels:
    service: "flexproxy"       # 服务标签
    version: "1.0.0"           # 版本标签
    environment: "production"  # 环境标签

# ================================
# 安全配置
# ================================
# 定义安全相关的配置
security:
  # 认证配置
  auth:
    type: "none"               # 认证类型: none, basic, bearer, apikey
    token_expiry: "24h"        # Token过期时间

  # 加密配置
  encryption:
    algorithm: "aes256"        # 加密算法: aes256, rsa
    key_length: 32             # 密钥长度: >=16

  # TLS配置
  tls:
    enabled: false             # 是否启用TLS
    cert_file: ""              # 证书文件路径
    key_file: ""               # 私钥文件路径
    min_version: "1.2"         # 最小TLS版本
    max_version: "1.3"         # 最大TLS版本

# ================================
# 限流配置
# ================================
# 定义请求限流的配置
rate_limiting:
  enabled: false               # 是否启用限流
  algorithm: "token_bucket"    # 限流算法: token_bucket, leaky_bucket, fixed_window, sliding_window
  rate: 100                    # 限流速率(请求/秒): >=1
  burst: 200                   # 突发容量: >=1
  window: "1m"                 # 时间窗口
  cleanup_period: "5m"         # 清理周期

# ================================
# DNS服务配置
# ================================
# 定义DNS解析服务的配置
dns_service:
  enabled: true                # 是否启用DNS服务
  cache_ttl: "300s"           # DNS缓存TTL
  timeout: "5s"               # DNS查询超时时间
  retries: 3                  # DNS查询重试次数: >=0

  # DNS服务器配置
  servers:
    primary: "*******:53"     # 主DNS服务器
    secondary: "*******:53"   # 备用DNS服务器
    fallback: "system"        # 回退DNS: system, none

  # DNS模式配置
  modes:
    ipv4: "enabled"           # IPv4解析: enabled, disabled, preferred
    ipv6: "enabled"           # IPv6解析: enabled, disabled, preferred
    reverse: "enabled"        # 反向解析: enabled, disabled

# ================================
# 动作序列配置
# ================================
# 定义可重用的动作序列，供事件触发时执行
#
# ✅ 支持的动作类型（16种，已完全整合到Executor系统）：
#
# 基础动作类型（8种）：
#    log, banip, ban_domain, block_request, modify_request,
#    modify_response, cache_response, script
#
# 扩展动作类型（8种）：
#    retry_same, retry, banipdomain, save_to_pool, cache,
#    request_url, null_response, bypass_proxy
#
# 🔥 重点功能：modify_request 和 modify_response 动作
# ===============================================
# 这两个动作支持完整的HTTP请求/响应修改功能：
#
# 📋 支持的修改操作：
#   ✅ Header修改：添加、删除、修改HTTP头部
#   ✅ Body修改：支持6种内容格式（JSON、XML、HTML、纯文本、URL编码、二进制）
#   ✅ 状态码修改：修改HTTP响应状态码（仅modify_response）
#   ✅ 自动化功能：Content-Type自动设置、Content-Length自动更新、格式验证
#
# 📝 配置方式：
#   1. 简单模式：使用 body 参数（向后兼容）
#   2. 高级模式：使用 body_config 参数（支持多格式、编码、验证）
#
# 🎯 支持的内容格式：
#   • JSON：application/json（自动语法验证）
#   • XML：application/xml（基本结构验证）
#   • HTML：text/html（基本结构检查）
#   • 纯文本：text/plain
#   • URL编码：application/x-www-form-urlencoded（键值对验证）
#   • 二进制：application/octet-stream（Base64编码支持）
actions:
  # ================================
  # 基础动作类型示例（8种）
  # ================================

  # 1. log - 日志记录动作
  log_handler:
    sequence:
      - type: "log"
        message: "事件触发: {{.event_name}} - URL: {{.url}}"
        level: "info"

  # 2. banip - IP封禁动作
  banip_handler:
    sequence:
      - type: "banip"
        duration: 3600  # 封禁1小时

  # 3. ban_domain - 域名封禁动作
  ban_domain_handler:
    sequence:
      - type: "ban_domain"
        domain: "{{.domain}}"
        duration: 7200  # 封禁2小时

  # 4. block_request - 阻止请求动作
  block_request_handler:
    sequence:
      - type: "block_request"
        reason: "安全策略阻止"
        status_code: 403

  # 5. modify_request - 修改请求动作（简单模式）
  # ================================================
  # 📝 支持的参数：
  #   • headers: 要添加或修改的请求头（键值对）
  #   • remove_headers: 要删除的请求头名称列表
  #   • body: 新的请求体内容（字符串格式，向后兼容）
  #   • body_config: 高级请求体配置（支持多种格式）
  #   • auto_content_type: 是否自动设置Content-Type（默认true）
  modify_request_handler:
    sequence:
      - type: "modify_request"
        headers:
          "User-Agent": "FlexProxy/1.0"
          "X-Forwarded-By": "FlexProxy"
          "Authorization": "Bearer token-12345"
        remove_headers: ["X-Real-IP", "Accept-Encoding"]
        body: '{"modified": true, "proxy": "FlexProxy", "timestamp": "2024-01-01T00:00:00Z"}'

  # 6. modify_response - 修改响应动作（简单模式）
  # ================================================
  # 📝 支持的参数：
  #   • headers: 要添加或修改的响应头（键值对）
  #   • remove_headers: 要删除的响应头名称列表
  #   • status_code: 新的响应状态码（整数）
  #   • body: 新的响应体内容（字符串格式，向后兼容）
  #   • body_config: 高级响应体配置（支持多种格式）
  #   • auto_content_type: 是否自动设置Content-Type（默认true）
  modify_response_handler:
    sequence:
      - type: "modify_response"
        headers:
          "X-Proxy-Modified": "true"
          "Cache-Control": "no-cache, no-store"
          "Content-Type": "application/json"
        remove_headers: ["Server", "X-Powered-By"]
        status_code: 200
        body: '{"success": true, "message": "Response modified by FlexProxy", "data": {"original": "replaced"}}'

  # 7. modify_request_advanced - 高级请求修改（支持多种格式）
  # ================================================
  # 🔥 body_config 高级配置参数说明：
  #   • content: 内容字符串（必需）
  #   • format: 内容格式（json|xml|html|text|form|binary）
  #   • content_type: 自定义Content-Type（可选，会覆盖自动检测）
  #   • encoding: 编码方式（utf-8|base64，默认utf-8）
  #   • auto_content_type: 是否自动设置Content-Type（默认true）
  modify_request_advanced:
    sequence:
      - type: "modify_request"
        headers:
          "X-Modified-By": "FlexProxy-Advanced"
        body_config:
          content: |
            {
              "advanced_modification": true,
              "supported_formats": ["JSON", "XML", "HTML", "Text", "Form", "Binary"],
              "auto_content_type": true,
              "request_data": {
                "user_id": 12345,
                "action": "advanced_update"
              }
            }
          format: "json"
          content_type: "application/json; charset=utf-8"

  # 8. modify_response_advanced - 高级响应修改（支持多种格式）
  # ================================================
  # 🎯 展示所有支持的高级功能：
  #   ✅ 多格式支持：JSON、XML、HTML、纯文本、URL编码、二进制
  #   ✅ 自动Content-Type检测和设置
  #   ✅ 格式验证（JSON语法、XML结构等）
  #   ✅ Base64编码支持（二进制数据）
  #   ✅ 向后兼容（支持简单body参数）
  modify_response_advanced:
    sequence:
      - type: "modify_response"
        headers:
          "X-Advanced-Modification": "true"
          "X-Supported-Formats": "JSON,XML,HTML,Text,Form,Binary"
        status_code: 200
        body_config:
          content: |
            {
              "status": "success",
              "message": "Advanced response modification by FlexProxy",
              "features": {
                "json_support": true,
                "xml_support": true,
                "html_support": true,
                "binary_support": true,
                "auto_content_type": true,
                "format_validation": true
              },
              "data": {
                "modification_time": "2024-01-01T00:00:00Z",
                "proxy_version": "FlexProxy v1.0"
              }
            }
          format: "json"
          auto_content_type: true

  # 9. 多格式Body修改示例集合
  # ================================================
  # 展示所有6种支持的内容格式的完整配置示例

  # 9.1 JSON格式修改示例
  modify_json_example:
    sequence:
      - type: "modify_request"
        body_config:
          content: '{"api_version": "v2", "modified_by": "FlexProxy", "data": {"user_id": 12345}}'
          format: "json"
          # 自动设置: Content-Type: application/json

  # 9.2 XML格式修改示例
  modify_xml_example:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            <?xml version="1.0" encoding="UTF-8"?>
            <response>
              <status>success</status>
              <message>XML response modified by FlexProxy</message>
              <data>
                <item id="1">Modified Item</item>
              </data>
            </response>
          format: "xml"
          # 自动设置: Content-Type: application/xml

  # 9.3 HTML格式修改示例
  modify_html_example:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <title>页面已被FlexProxy修改</title>
            </head>
            <body>
                <h1>内容已修改</h1>
                <p>此页面内容已被FlexProxy代理服务器修改。</p>
            </body>
            </html>
          format: "html"
          # 自动设置: Content-Type: text/html

  # 9.4 纯文本格式修改示例
  modify_text_example:
    sequence:
      - type: "modify_response"
        body_config:
          content: |
            FlexProxy 代理服务器响应
            ========================

            原始内容已被替换为此文本内容。
            修改时间: 2024-01-01 00:00:00
            代理版本: FlexProxy v1.0
          format: "text"
          # 自动设置: Content-Type: text/plain

  # 9.5 URL编码格式修改示例
  modify_form_example:
    sequence:
      - type: "modify_request"
        body_config:
          content: "username=flexproxy_user&password=modified_password&action=login&remember=true"
          format: "form"
          # 自动设置: Content-Type: application/x-www-form-urlencoded

  # 9.6 二进制数据修改示例
  modify_binary_example:
    sequence:
      - type: "modify_response"
        headers:
          "Content-Disposition": "attachment; filename=\"modified_file.bin\""
        body_config:
          content: "RmxleFByb3h5IEJpbmFyeSBEYXRhIEV4YW1wbGUh"  # "FlexProxy Binary Data Example!" in base64
          encoding: "base64"
          content_type: "application/octet-stream"
          # Base64解码后设置为二进制内容

  # 9.7 自动格式检测示例
  modify_auto_detect_example:
    sequence:
      - type: "modify_response"
        body_config:
          content: '{"auto_detected": true, "message": "Content-Type will be automatically detected as JSON"}'
          # 不指定format，系统会自动检测为JSON格式并设置相应的Content-Type

  # 7. cache_response - 缓存响应动作
  cache_response_handler:
    sequence:
      - type: "cache_response"
        duration: 3600
        cache_key: "response_{{.url_hash}}"

  # 8. script - 脚本执行动作
  script_handler:
    sequence:
      - type: "script"
        script: |
          console.log('处理请求:', opt.url);
          return opt.status >= 400;
        language: "javascript"

  # ================================
  # 扩展动作类型示例（8种）
  # ================================

  # 9. retry_same - 重试相同代理动作
  retry_same_handler:
    sequence:
      - type: "retry_same"
        retry_count: 2

  # 10. retry - 重试新代理动作
  retry_handler:
    sequence:
      - type: "retry"
        retry_count: 3

  # 11. banipdomain - 域名级IP封禁动作
  banipdomain_handler:
    sequence:
      - type: "banipdomain"
        duration: 3600
        scope: "domain"

  # 12. save_to_pool - 保存到代理池动作
  save_to_pool_handler:
    sequence:
      - type: "save_to_pool"
        pool_name: "quality_proxies"
        quality_score: 85

  # 13. cache - 缓存动作
  cache_handler:
    sequence:
      - type: "cache"
        duration: 1800
        cache_scope: "domain"
        max_use_count: 100

  # 14. request_url - 请求URL动作
  request_url_handler:
    sequence:
      - type: "request_url"
        url: "https://api.example.com/webhook"
        method: "POST"
        body: '{"event": "proxy_action", "data": "{{.request_data}}"}'
        body_type: "json"
        headers: "Content-Type:application/json"
        timeout_ms: 5000

  # 15. null_response - 空响应动作
  null_response_handler:
    sequence:
      - type: "null_response"
        status_code: 204
        headers:
          "X-Null-Response": "true"

  # 16. bypass_proxy - 绕过代理动作
  bypass_proxy_handler:
    sequence:
      - type: "bypass_proxy"
        timeout_ms: 10000

  # ================================
  # 组合动作序列示例
  # ================================

  # 综合安全处理序列
  security_handler:
    sequence:
      - type: "log"
        message: "🚨 安全威胁检测: {{.url}} from {{.client_ip}}"
        level: "error"
      - type: "banip"
        duration: 3600
      - type: "null_response"
        status_code: 403
        body: '{"error": "Access denied", "code": 403}'

  # 错误恢复处理序列
  error_recovery_handler:
    sequence:
      - type: "retry"
        retry_count: 2
      - type: "save_to_pool"
        pool_name: "error_proxies"
        quality_score: 30

# ================================
# 事件配置
# ================================
# 定义各种触发条件和对应的处理动作
# 支持的触发器类型：status, body, max_request_time, conn_time_out, min_request_time,
#                  url, domain, combined, custom, request_body, request_header, response_header
events:
  # ================================
  # 基础触发器类型示例
  # ================================

  # 1. status - 状态码触发事件
  - name: "status_code_handler"
    enable: true
    trigger_type: "status"
    process_stage: "post_header"
    priority: 10

    conditions:
      - name: "error_status"
        enable: true
        status_codes:
          codes: [403, 404, 500, 502, 503]
          relation: "or"

    matches:
      - name: "error_match"
        enable: true
        conditions: ["error_status"]
        actions:
          - type: "banip"
            duration: 600
          - type: "retry"
            retry_count: 2

  # 2. body - 响应体内容触发事件
  - name: "body_content_handler"
    enable: true
    trigger_type: "body"
    process_stage: "post_body"
    priority: 15

    conditions:
      - name: "error_content"
        enable: true
        body_patterns:
          patterns:
            - pattern: "error|failed|denied"
              type: "regex"
              weight: 10
          relation: "or"
          case_sensitive: false

    matches:
      - name: "content_error_match"
        enable: true
        conditions: ["error_content"]
        actions:
          - type: "log"
            message: "检测到错误内容: {{.url}}"
            level: "warn"

  # 3. max_request_time - 最大请求时间触发事件
  - name: "max_request_time_handler"
    enable: true
    trigger_type: "max_request_time"
    process_stage: "post_body"
    priority: 12

    conditions:
      - name: "slow_request"
        enable: true
        max_request_time: 10000  # 10秒

    matches:
      - name: "timeout_match"
        enable: true
        conditions: ["slow_request"]
        actions:
          - type: "retry_same"
            retry_count: 1

  # 4. conn_time_out - 连接超时触发事件
  - name: "connection_timeout_handler"
    enable: true
    trigger_type: "conn_time_out"
    process_stage: "post_body"
    priority: 20

    conditions:
      - name: "connection_timeout"
        enable: true
        connection_timeout: 5000  # 5秒

    matches:
      - name: "conn_timeout_match"
        enable: true
        conditions: ["connection_timeout"]
        actions:
          - type: "retry"
            retry_count: 2

  # 5. min_request_time - 最小请求时间触发事件
  - name: "min_request_time_handler"
    enable: true
    trigger_type: "min_request_time"
    process_stage: "post_body"
    priority: 8

    conditions:
      - name: "too_fast_response"
        enable: true
        min_request_time: 100  # 100ms

    matches:
      - name: "fast_response_match"
        enable: true
        conditions: ["too_fast_response"]
        actions:
          - type: "cache"
            duration: 300
            cache_scope: "url"

  # 6. url - URL模式触发事件
  - name: "url_pattern_handler"
    enable: true
    trigger_type: "url"
    process_stage: "pre"
    priority: 5

    conditions:
      - name: "api_endpoints"
        enable: true
        url_patterns:
          patterns:
            - pattern: "/api/"
              type: "string"
              weight: 10
          relation: "or"

    matches:
      - name: "api_match"
        enable: true
        conditions: ["api_endpoints"]
        actions:
          - type: "modify_request"
            headers:
              "X-API-Request": "true"

  # 7. domain - 域名模式触发事件
  - name: "domain_handler"
    enable: true
    trigger_type: "domain"
    process_stage: "pre"
    priority: 6

    conditions:
      - name: "cdn_domains"
        enable: true
        domain_patterns:
          patterns:
            - pattern: ".*\\.cdn\\."
              type: "regex"
              weight: 10
          relation: "or"

    matches:
      - name: "cdn_match"
        enable: true
        conditions: ["cdn_domains"]
        actions:
          - type: "bypass_proxy"

  # 8. request_header - 请求头触发事件
  - name: "request_header_handler"
    enable: true
    trigger_type: "request_header"
    process_stage: "pre"
    priority: 7

    conditions:
      - name: "user_agent_check"
        enable: true
        request_header_patterns:
          headers:
            "User-Agent":
              patterns:
                - pattern: "bot|crawler"
                  type: "regex"
                  weight: 10
              relation: "or"

    matches:
      - name: "bot_match"
        enable: true
        conditions: ["user_agent_check"]
        actions:
          - type: "null_response"
            status_code: 429

  # 9. response_header - 响应头触发事件
  - name: "response_header_handler"
    enable: true
    trigger_type: "response_header"
    process_stage: "post_header"
    priority: 11

    conditions:
      - name: "rate_limit_headers"
        enable: true
        response_header_patterns:
          headers:
            "X-RateLimit-Remaining":
              patterns:
                - pattern: "^[0-5]$"
                  type: "regex"
                  weight: 15
              relation: "or"

    matches:
      - name: "rate_limit_match"
        enable: true
        conditions: ["rate_limit_headers"]
        actions:
          - type: "cache"
            duration: 60
            cache_scope: "global"

  # 10. request_body - 请求体触发事件
  - name: "request_body_handler"
    enable: true
    trigger_type: "request_body"
    process_stage: "pre"
    priority: 3

    conditions:
      - name: "malicious_payload"
        enable: true
        request_body_patterns:
          patterns:
            - pattern: "<script|javascript:"
              type: "regex"
              weight: 20
          relation: "or"
          case_sensitive: false

    matches:
      - name: "security_threat_match"
        enable: true
        conditions: ["malicious_payload"]
        actions:
          - type: "block_request"
            reason: "恶意请求检测"
          - type: "banip"
            duration: 3600

  # 11. combined - 组合条件触发事件
  - name: "combined_conditions_handler"
    enable: true
    trigger_type: "combined"
    process_stage: "post_body"
    priority: 25

    conditions:
      - name: "high_latency"
        enable: true
        max_request_time: 8000
      - name: "error_status"
        enable: true
        status_codes:
          codes: [500, 502, 503]
          relation: "or"

    matches:
      - name: "latency_and_error"
        enable: true
        conditions: ["high_latency", "error_status"]
        logic: "AND"
        actions:
          - type: "banip"
            duration: 1800
          - type: "save_to_pool"
            pool_name: "problematic_proxies"

  # 12. custom - 自定义触发事件
  - name: "custom_trigger_handler"
    enable: true
    trigger_type: "custom"
    process_stage: "post_body"
    priority: 30

    conditions:
      - name: "custom_condition"
        enable: true
        trigger_id: "custom_business_logic"

    matches:
      - name: "custom_match"
        enable: true
        conditions: ["custom_condition"]
        actions:
          - type: "script"
            script: "console.log('自定义逻辑处理:', opt.url);"
            language: "javascript"

# ================================
# 高级配置
# ================================
# 定义错误恢复、追踪、性能和调试相关的高级配置
advanced:
  # 错误恢复配置
  error_recovery:
    max_retry_attempts: 3        # 最大重试次数: 0-10
    initial_retry_delay: "1s"    # 初始重试延迟
    max_retry_delay: "30s"       # 最大重试延迟
    retry_multiplier: 2.0        # 重试延迟倍数: >=1.0
    failure_threshold: 5         # 失败阈值: >=1
    success_threshold: 3         # 成功阈值: >=1
    circuit_timeout: "60s"       # 熔断器超时时间
    circuit_reset_timeout: "300s" # 熔断器重置超时时间

  # 追踪配置
  tracing:
    enabled: true                # 是否启用追踪
    hex_generator_length: 16     # 十六进制生成器长度: 8-64
    sequence_modulus: 10000      # 序列模数: >=1000

  # 性能配置
  performance:
    worker_pool_size: 10         # 工作池大小: >=1
    queue_size: 1000            # 队列大小: >=1
    batch_size: 100             # 批处理大小: >=1
    flush_interval: "5s"        # 刷新间隔

  # 调试配置
  debug:
    enabled: false              # 是否启用调试模式
    verbose_logging: false      # 详细日志记录
    dump_requests: false        # 转储请求
    dump_responses: false       # 转储响应
    profile_enabled: false      # 性能分析启用
    profile_port: 8081         # 性能分析端口: 1-65535

# ================================
# 路径配置
# ================================
# 定义各种文件和目录路径
paths:
  config_dir: "./config"          # 配置文件目录
  log_dir: "./logs"              # 日志文件目录
  cache_dir: "./cache"           # 缓存文件目录
  temp_dir: "./temp"             # 临时文件目录
  data_dir: "./data"             # 数据文件目录
  backup_dir: "./backup"         # 备份文件目录
  plugin_dir: "./plugins"        # 插件目录

# ================================
# 系统配置
# ================================
# 定义系统级别的配置选项
system:
  os_detection: true             # 操作系统检测
  arch_detection: true           # 架构检测

  # 信号处理配置
  signal_handling:
    graceful_shutdown: true      # 优雅关闭
    shutdown_timeout: "30s"      # 关闭超时时间
    signals: ["SIGTERM", "SIGINT", "SIGQUIT"] # 处理的信号列表

  # 资源限制配置
  limits:
    max_memory: "1GB"           # 最大内存使用
    max_cpu_percent: 80         # 最大CPU使用百分比: 1-100
    max_file_descriptors: 10000 # 最大文件描述符数: >=1
    max_goroutines: 10000       # 最大协程数: >=1

# ================================
# 协议配置
# ================================
# 定义各种网络协议的配置
protocols:
  # HTTP协议配置
  http:
    enabled: true
    version: "1.1"              # HTTP版本: 1.0, 1.1, 2.0
    keep_alive: true            # 保持连接
    compression: true           # 启用压缩 (注意: HTTPConfig中compression是bool类型)

  # HTTPS协议配置
  https:
    enabled: true
    version: "1.1"              # HTTPS版本: 1.0, 1.1, 2.0
    keep_alive: true            # 保持连接
    compression: true           # 启用压缩
    verify_ssl: true            # SSL验证

  # SOCKS4协议配置
  socks4:
    enabled: false

  # SOCKS5协议配置
  socks5:
    enabled: false
    auth_required: false

  # DNS协议配置
  dns:
    udp: true                   # 启用UDP DNS
    tcp: false                  # 启用TCP DNS
    tls: false                  # 启用DNS over TLS
    https: false                # 启用DNS over HTTPS
    doh: false                  # 启用DoH

# ================================
# 插件配置
# ================================
# 定义插件系统的配置
plugins:
  enabled: false                # 是否启用插件系统
  auto_load: true              # 自动加载插件
  plugin_paths: ["./plugins"]   # 插件搜索路径

  # 插件配置映射
  configs:
    example_plugin:
      enabled: false
      config_file: "example.yaml"
      parameters:
        param1: "value1"
        param2: 42

# ================================
# 开发配置
# ================================
# 定义开发和测试相关的配置
development:
  mode: "production"            # 模式: development, testing, production
  hot_reload: false            # 热重载

  # 测试配置
  testing:
    enabled: false              # 是否启用测试模式
    mock_responses: false       # 模拟响应
    test_data_dir: "./testdata" # 测试数据目录

  # 性能分析配置
  profiling:
    enabled: false              # 是否启用性能分析
    cpu_profile: false          # CPU性能分析
    memory_profile: false       # 内存性能分析
    block_profile: false        # 阻塞性能分析
    mutex_profile: false        # 互斥锁性能分析
