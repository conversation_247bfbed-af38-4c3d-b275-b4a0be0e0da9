package common

import (
	"os"
	"time"

	"github.com/flexp/flexp/internal/interfaces"
)

// Options 包含所需的配置
type Options struct {
	ProxyManager interfaces.ProxyManagerInterface // 改为接口类型
	Result       *os.File
	Timeout      time.Duration
	MaxRetries   int // 添加最大重试次数字段

	Address    string
	Auth       string
	CC         string
	Check      bool
	Countries  []string
	Daemon     bool
	File       string
	Goroutine  int
	Output     string
	Sync       bool
	Verbose    bool
	Watch      bool
	Type       string
	RuleConfig *Config // 使用 common.Config (YAML 配置)
	ConfigFile string
}
