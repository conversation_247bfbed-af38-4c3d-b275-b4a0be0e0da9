package common

// 注意：处理阶段常量和动作类型常量已迁移到 common/constants/constants.go 中统一管理
// 请使用 constants.PreProcess, constants.ActionRetrySame 等常量

// 触发器类型常量
const (
	TriggerStatus         = "status"          // 状态码触发
	TriggerBody           = "body"            // 响应内容触发
	TriggerMaxRequestTime = "max_request_time" // 请求时间长于触发
	TriggerConnTimeOut    = "conn_time_out"    // 连接超时触发
	TriggerMinRequestTime = "min_request_time" // 请求时间短于触发
	TriggerURL            = "url"             // URL匹配触发
	TriggerDomain         = "domain"          // 域名匹配触发
	TriggerCombined       = "combined"        // 组合条件触发
	TriggerCustom         = "custom"          // 自定义触发器
	TriggerRequestBody    = "request_body"    // 请求内容触发器
	TriggerRequestHeader  = "request_header"  // 请求头触发器
	TriggerResponseHeader = "response_header" // 响应头触发器
)

// Config 是整个配置文件的顶层结构
type Config struct {
	Global      GlobalConfig                `yaml:"global" validate:"required"`
	Server      *ServerConfig               `yaml:"server,omitempty"`
	Proxy       *ProxyConfig                `yaml:"proxy,omitempty"`
	Cache       *CacheConfig                `yaml:"cache,omitempty"`
	Logging     *LoggingConfig              `yaml:"logging,omitempty"`
	Monitoring  *MonitoringConfig           `yaml:"monitoring,omitempty"`
	Security    *SecurityConfig             `yaml:"security,omitempty"`
	RateLimiting *RateLimitingConfig        `yaml:"rate_limiting,omitempty"`
	DNSService  *DNSServiceConfig           `yaml:"dns_service,omitempty"`
	Actions     map[string]ActionSequenceName `yaml:"actions" validate:"dive"`
	Events      []EventConfig               `yaml:"events" validate:"dive"`
	Advanced    *AdvancedConfig             `yaml:"advanced,omitempty"`
	Paths       *PathsConfig                `yaml:"paths,omitempty"`
	System      *SystemConfig               `yaml:"system,omitempty"`
	Protocols   *ProtocolsConfig            `yaml:"protocols,omitempty"`
	Plugins     *PluginsConfig              `yaml:"plugins,omitempty"`
	Development *DevelopmentConfig          `yaml:"development,omitempty"`
}

// GlobalConfig 对应 YAML 中的 'global' 部分
type GlobalConfig struct {
	Enable                     bool                `yaml:"enable"`
	ProxyFile                  string              `yaml:"proxy_file"` // 代理文件路径
	GlobalBannedIPs            []BanIPConfig       `yaml:"global_banned_ips" validate:"dive"`
	BannedDomains              []BannedDomainConfig `yaml:"banned_domains" validate:"dive"`
	BlockedIPs                 []string            `yaml:"blocked_ips" validate:"dive,ip"`
	TrustedIPs                 []string            `yaml:"trusted_ips" validate:"dive,ip"`
	ExcludedPatterns           []string            `yaml:"excluded_patterns"`
	ExcludedScope              string              `yaml:"excluded_scope"`
	RulePriority               int                 `yaml:"rule_priority" validate:"min=0,max=100"`
	DefaultProcessStage        string              `yaml:"default_process_stage" validate:"oneof=pre post_header post_body"`
	DNSLookupMode              string              `yaml:"dns_lookup_mode" validate:"oneof=system custom hybrid"`
	ReverseDNSLookup           string              `yaml:"reverse_dns_lookup"`
	CustomDNSServers           []CustomDNSServer   `yaml:"custom_dns_servers" validate:"dive"`
	HTTPProxyDNS               string              `yaml:"http_proxy_dns"`
	IPRotationMode             string              `yaml:"ip_rotation_mode" validate:"oneof=random sequential quality smart"`
	MinProxyPoolSize           int                 `yaml:"min_proxy_pool_size" validate:"min=1"`
	MaxProxyFetchAttempts      int                 `yaml:"max_proxy_fetch_attempts" validate:"min=1,max=10"`
	DNSCacheTTL                int                 `yaml:"dns_cache_ttl" validate:"min=0"`
	DNSNoCache                 bool                `yaml:"dns_no_cache"`
	IPVersionPriority          string              `yaml:"ip_version_priority" validate:"oneof=ipv4 ipv6 dual"`
	DefaultDNSTimeout          int                 `yaml:"default_dns_timeout" validate:"min=1000,max=30000"`
	RetryProxyReusePolicy      string              `yaml:"retry_proxy_reuse_policy" validate:"oneof=allow deny cooldown"`
	RetryProxyCooldownTime     int                 `yaml:"retry_proxy_cooldown_time" validate:"min=0"`
	RetryProxyGlobalTracking   bool                `yaml:"retry_proxy_global_tracking"`
}

// BanIPConfig 代表全局IP封禁配置项
type BanIPConfig struct {
	IP       string      `yaml:"ip" validate:"required,ip"`
	Duration interface{} `yaml:"duration" validate:"required"` // int 或 string "reboot"
}

// BannedDomainConfig 代表全局域名封禁配置项
type BannedDomainConfig struct {
	Domain   string      `yaml:"domain" validate:"required,fqdn"`
	Duration interface{} `yaml:"duration" validate:"required"` // int 或 string "reboot"
}

// CustomDNSServer 代表自定义DNS服务器配置
type CustomDNSServer struct {
	Server   string   `yaml:"server" validate:"required,ip_or_ip_port"`
	Protocol string   `yaml:"protocol" validate:"required,oneof=udp tcp doh dot"`
	Timeout  int      `yaml:"timeout,omitempty" validate:"min=1000,max=30000"` // omitempty 如果允许默认值
	Priority int      `yaml:"priority,omitempty" validate:"min=0,max=100"`
	Tags     []string `yaml:"tags,omitempty"`
}

// DNSServerConfig 新增DNS服务器配置结构
type DNSServerConfig struct {
	Address  string            // 服务器地址（IP:端口）
	Protocol string            // 协议（udp/tcp/doh等）
	Timeout  int               // 超时时间（毫秒）
	Priority int               // 优先级
	Tags     []string          // 标签
	Options  map[string]string // 其他选项
}

// ActionConfig 代表单个动作的配置
// 使用 Params map[string]interface{} 来捕获所有未知或特定于类型的参数
type ActionConfig struct {
	Type   string                 `yaml:"type"`
	Params map[string]interface{} `yaml:",inline"`
}

// ActionSequenceName 代表一个动作序列，包含主序列和可选的备用序列
type ActionSequenceName struct {
	Sequence            []ActionConfig `yaml:"sequence"`
	Relation []ActionConfig `yaml:"relation,omitempty"` // 关联动作序列
}

// EventConfig 代表单个事件的配置
type EventConfig struct {
	Name                   string                    `yaml:"name"`
	Enable                 bool                      `yaml:"enable"`
	TriggerType            string                    `yaml:"trigger_type"`
	Conditions             []ConditionConfig         `yaml:"conditions"`             // 条件定义列表
	Matches                []MatchConfig             `yaml:"matches"`               // 匹配规则列表
	ConditionalActions     []ConditionalActionConfig `yaml:"conditional_actions,omitempty"` // 向后兼容（已废弃）
	ProcessStage           string                    `yaml:"process_stage"`
	Priority               int                       `yaml:"priority"`
	DNSCacheTTL            int                       `yaml:"dns_cache_ttl,omitempty"`
	DNSLookupMode          string                    `yaml:"dns_lookup_mode,omitempty"`
	DNSNoCache             bool                      `yaml:"dns_no_cache,omitempty"`
	CustomDNSServers       []CustomDNSServer         `yaml:"custom_dns_servers,omitempty"`
	HTTPProxyDNS           string                    `yaml:"http_proxy_dns,omitempty"`
}

// ConditionalActionConfig 条件-动作配置（保留向后兼容）
type ConditionalActionConfig struct {
	ConditionName          string                       `yaml:"condition_name"`          // 条件名称
	StatusCodes            *StatusCodesConfig           `yaml:"status_codes,omitempty"`
	BodyPatterns           *EnhancedPatternConfig       `yaml:"body_patterns,omitempty"`
	MaxRequestTime         int                          `yaml:"max_request_time,omitempty"`
	ConnectionTimeout      int                          `yaml:"connection_timeout,omitempty"`
	MinRequestTime         int                          `yaml:"min_request_time,omitempty"`
	URLPatterns            *EnhancedPatternConfig       `yaml:"url_patterns,omitempty"`
	DomainPatterns         *EnhancedPatternConfig       `yaml:"domain_patterns,omitempty"`
	RequestBodyPatterns    *EnhancedPatternConfig       `yaml:"request_body_patterns,omitempty"`
	RequestHeaderPatterns  *EnhancedHeaderPatternConfig `yaml:"request_header_patterns,omitempty"`
	ResponseHeaderPatterns *EnhancedHeaderPatternConfig `yaml:"response_header_patterns,omitempty"`
	ConditionRelation      string                       `yaml:"condition_relation,omitempty"` // 当前条件内部的逻辑关系
	TriggerID              string                       `yaml:"trigger_id,omitempty"`
	ActionSequenceName     string                       `yaml:"action_sequence_name"`             // 该条件匹配时执行的动作序列
	Enable                 bool                         `yaml:"enable"`                     // 是否启用该条件
}

// ConditionConfig 新的条件配置结构
type ConditionConfig struct {
	Name                   string                       `yaml:"name"`                       // 条件名称
	Enable                 bool                         `yaml:"enable"`                     // 是否启用该条件
	StatusCodes            *StatusCodesConfig           `yaml:"status_codes,omitempty"`
	BodyPatterns           *EnhancedPatternConfig       `yaml:"body_patterns,omitempty"`
	MaxRequestTime         int                          `yaml:"max_request_time,omitempty"`
	ConnectionTimeout      int                          `yaml:"connection_timeout,omitempty"`
	MinRequestTime         int                          `yaml:"min_request_time,omitempty"`
	URLPatterns            *EnhancedPatternConfig       `yaml:"url_patterns,omitempty"`
	DomainPatterns         *EnhancedPatternConfig       `yaml:"domain_patterns,omitempty"`
	RequestBodyPatterns    *EnhancedPatternConfig       `yaml:"request_body_patterns,omitempty"`
	RequestHeaderPatterns  *EnhancedHeaderPatternConfig `yaml:"request_header_patterns,omitempty"`
	ResponseHeaderPatterns *EnhancedHeaderPatternConfig `yaml:"response_header_patterns,omitempty"`
	ConditionRelation      string                       `yaml:"condition_relation,omitempty"` // 条件内部的逻辑关系：AND, OR
	TriggerID              string                       `yaml:"trigger_id,omitempty"`
}

// MatchConfig 匹配规则配置，支持多层嵌套
type MatchConfig struct {
	Name       string         `yaml:"name,omitempty"`      // 匹配规则名称
	Conditions []string       `yaml:"conditions,omitempty"` // 条件名称列表
	SubMatches []MatchConfig  `yaml:"sub_matches,omitempty"` // 嵌套的子匹配规则
	Logic      string         `yaml:"logic,omitempty"`     // 条件组合逻辑：AND, OR, NOT
	Actions    []ActionConfig `yaml:"actions"`             // 独立的动作列表
	Enable     bool           `yaml:"enable"`              // 是否启用该匹配规则
}

// StatusCodesConfig 状态码配置
type StatusCodesConfig struct {
	Codes    []int  `yaml:"codes"`    // 状态码列表
	Relation string `yaml:"relation"` // 关系："and" 或 "or"
}

// HeaderPatternConfig 头部模式配置
type HeaderPatternConfig struct {
	HeaderName string   `yaml:"header_name"` // 头部名称
	Patterns   []string `yaml:"patterns"`    // 模式列表
	Relation   string   `yaml:"relation"`    // 关系："and" 或 "or"
}

// EnhancedPatternConfig 增强模式配置
type EnhancedPatternConfig struct {
	Patterns       []PatternExpression `yaml:"patterns"`             // 模式表达式列表
	Relation       string              `yaml:"relation"`             // 模式间关系："and" 或 "or"
	Chained        []ChainedPattern    `yaml:"chained,omitempty"`    // 链式模式
	Negation       bool                `yaml:"negation,omitempty"`   // 是否取反
	CaseSensitive  bool                `yaml:"case_sensitive"`       // 是否大小写敏感
	MultiLine      bool                `yaml:"multiline,omitempty"`  // 是否多行模式
	DotAll         bool                `yaml:"dotall,omitempty"`     // 是否点匹配所有字符
	Unicode        bool                `yaml:"unicode,omitempty"`    // 是否启用Unicode支持
	Timeout        int                 `yaml:"timeout,omitempty"`    // 匹配超时时间（毫秒）
	MaxMatches     int                 `yaml:"max_matches,omitempty"` // 最大匹配数量
}

// PatternExpression 模式表达式
type PatternExpression struct {
	Pattern string                  `yaml:"pattern"` // 正则表达式或字符串模式
	Type    string                  `yaml:"type"`    // 模式类型："regex", "string", "glob"
	Weight  int                     `yaml:"weight"`  // 权重
	Chain    []ChainedPattern        `yaml:"chain,omitempty"`
	Logic    string                  `yaml:"logic,omitempty"` // AND, OR, XOR, NOT
	Negate   bool                    `yaml:"negate,omitempty"`
	SubGroup *EnhancedPatternConfig  `yaml:"subgroup,omitempty"`
}

// ChainedPattern 链式模式
type ChainedPattern struct {
	Order    int    `yaml:"order"`    // 执行顺序
	Pattern  string `yaml:"pattern"`  // 模式
	Operator string `yaml:"operator"` // 操作符："and", "or", "not"
	Negate    bool   `yaml:"negate,omitempty"`
}

// EnhancedHeaderPatternConfig 增强头部模式配置
type EnhancedHeaderPatternConfig struct {
	Headers map[string]*EnhancedPatternConfig `yaml:"headers"` // 头部名称到模式配置的映射
}

// HeaderPatternExpression 头部模式表达式
type HeaderPatternExpression struct {
	Header   string                 `yaml:"header"`
	Patterns *EnhancedPatternConfig `yaml:"patterns"`
	Negate   bool                   `yaml:"negate,omitempty"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host                  string           `yaml:"host" validate:"required"`
	Port                  int              `yaml:"port" validate:"min=1,max=65535"`
	HTTPSPort             int              `yaml:"https_port" validate:"min=1,max=65535"`
	SOCKSPort             int              `yaml:"socks_port" validate:"min=1,max=65535"`
	ReadTimeout           string           `yaml:"read_timeout"`
	WriteTimeout          string           `yaml:"write_timeout"`
	IdleTimeout           string           `yaml:"idle_timeout"`
	ConnectTimeout        string           `yaml:"connect_timeout"`
	MaxIdleConns          int              `yaml:"max_idle_conns" validate:"min=0"`
	MaxIdleConnsPerHost   int              `yaml:"max_idle_conns_per_host" validate:"min=0"`
	MaxConnsPerHost       int              `yaml:"max_conns_per_host" validate:"min=0"`
	BufferSize            int              `yaml:"buffer_size" validate:"min=1024"`
	MaxHeaderBytes        int              `yaml:"max_header_bytes" validate:"min=1024"`
	DebounceDelay         string           `yaml:"debounce_delay"`
	Compression           *CompressionConfig `yaml:"compression,omitempty"`
	HTTP2                 *HTTP2Config     `yaml:"http2,omitempty"`
	Profiling             *ProfilingConfig `yaml:"profiling,omitempty"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	Enabled               bool                `yaml:"enabled" default:"true"`
	Strategy              string              `yaml:"strategy" validate:"oneof=random sequential quality custom"`
	LoadBalancer          string              `yaml:"load_balancer" validate:"oneof=round_robin weighted_round_robin least_connections response_time ip_hash"`
	MaxRetries            int                 `yaml:"max_retries" validate:"min=0"`
	RetryInterval         string              `yaml:"retry_interval"`
	MaxRetryInterval      string              `yaml:"max_retry_interval"`
	HealthCheck           *HealthCheckConfig  `yaml:"health_check,omitempty"`
	PoolSize              int                 `yaml:"pool_size" validate:"min=1"`
	RotationInterval      int                 `yaml:"rotation_interval" validate:"min=0"`
	QualityScore          *QualityScoreConfig `yaml:"quality_score,omitempty"`
}

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled                   bool   `yaml:"enabled"`
	Interval                  string `yaml:"interval"`
	Timeout                   string `yaml:"timeout"`
	Path                      string `yaml:"path"`
	MaxConsecutiveFailures    int    `yaml:"max_consecutive_failures" validate:"min=1"`
	MaxConsecutiveSuccesses   int    `yaml:"max_consecutive_successes" validate:"min=1"`
}

// QualityScoreConfig 质量评分配置
type QualityScoreConfig struct {
	Default                float64 `yaml:"default" validate:"min=0,max=1"`
	SuccessRateWeight      float64 `yaml:"success_rate_weight" validate:"min=0,max=1"`
	ResponseTimeWeight     float64 `yaml:"response_time_weight" validate:"min=0,max=1"`
	MaxFailureRate         float64 `yaml:"max_failure_rate" validate:"min=0,max=1"`
	TopProxyRatio          float64 `yaml:"top_proxy_ratio" validate:"min=0,max=1"`
	ResponseTimeBaseline   int     `yaml:"response_time_baseline" validate:"min=0"`
	SmoothingFactor        float64 `yaml:"smoothing_factor" validate:"min=0,max=1"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled         bool                      `yaml:"enabled" default:"true"`
	Type            string                    `yaml:"type" validate:"oneof=memory redis file"`
	TTL             string                    `yaml:"ttl"`
	Size            int                       `yaml:"size" validate:"min=1"`
	CleanupInterval string                    `yaml:"cleanup_interval"`
	KeyPrefixes     map[string]string         `yaml:"key_prefixes,omitempty"`
	DNS             *DNSCacheConfig           `yaml:"dns,omitempty"`
}

// DNSCacheConfig DNS缓存配置
type DNSCacheConfig struct {
	TTL             string `yaml:"ttl"`
	CleanupInterval string `yaml:"cleanup_interval"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Enabled     bool   `yaml:"enabled" default:"true"`
	Level       string `yaml:"level" validate:"oneof=debug info warn error fatal"`
	Format      string `yaml:"format" validate:"oneof=json text"`
	File        string `yaml:"file"`
	MaxSize     int    `yaml:"max_size" validate:"min=1"`
	MaxAge      int    `yaml:"max_age" validate:"min=1"`
	MaxBackups  int    `yaml:"max_backups" validate:"min=0"`
	TimeFormat  string `yaml:"time_format"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled bool                      `yaml:"enabled"`
	Port    int                       `yaml:"port" validate:"min=1,max=65535"`
	Path    string                    `yaml:"path"`
	Metrics map[string]string         `yaml:"metrics,omitempty"`
	Labels  map[string]string         `yaml:"labels,omitempty"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	Enabled    bool              `yaml:"enabled" default:"true"`
	Auth       *AuthConfig       `yaml:"auth,omitempty"`
	Encryption *EncryptionConfig `yaml:"encryption,omitempty"`
	TLS        *TLSConfig        `yaml:"tls,omitempty"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	Type        string `yaml:"type" validate:"oneof=none basic bearer apikey"`
	TokenExpiry string `yaml:"token_expiry"`
}

// EncryptionConfig 加密配置
type EncryptionConfig struct {
	Algorithm string `yaml:"algorithm" validate:"oneof=aes256 rsa"`
	KeyLength int    `yaml:"key_length" validate:"min=16"`
}

// TLSConfig TLS配置
type TLSConfig struct {
	Enabled    bool   `yaml:"enabled"`
	CertFile   string `yaml:"cert_file"`
	KeyFile    string `yaml:"key_file"`
	MinVersion string `yaml:"min_version"`
	MaxVersion string `yaml:"max_version"`
}

// RateLimitingConfig 限流配置
type RateLimitingConfig struct {
	Enabled       bool   `yaml:"enabled"`
	Algorithm     string `yaml:"algorithm" validate:"oneof=token_bucket leaky_bucket fixed_window sliding_window"`
	Rate          int    `yaml:"rate" validate:"min=1"`
	Burst         int    `yaml:"burst" validate:"min=1"`
	Window        string `yaml:"window"`
	CleanupPeriod string `yaml:"cleanup_period"`
}

// DNSServiceConfig DNS服务配置
type DNSServiceConfig struct {
	Enabled  bool              `yaml:"enabled"`
	CacheTTL string            `yaml:"cache_ttl"`
	Timeout  string            `yaml:"timeout"`
	Retries  int               `yaml:"retries" validate:"min=0"`
	Servers  map[string]string `yaml:"servers,omitempty"`
	Modes    map[string]string `yaml:"modes,omitempty"`
}

// AdvancedConfig 高级配置
type AdvancedConfig struct {
	Enabled       bool                 `yaml:"enabled" default:"false"`
	ErrorRecovery *ErrorRecoveryConfig `yaml:"error_recovery,omitempty"`
	Tracing       *TracingConfig       `yaml:"tracing,omitempty"`
	Performance   *PerformanceConfig   `yaml:"performance,omitempty"`
	Debug         *DebugConfig         `yaml:"debug,omitempty"`
}

// ErrorRecoveryConfig 错误恢复配置
type ErrorRecoveryConfig struct {
	MaxRetryAttempts     int     `yaml:"max_retry_attempts" validate:"min=0"`
	InitialRetryDelay    string  `yaml:"initial_retry_delay"`
	MaxRetryDelay        string  `yaml:"max_retry_delay"`
	RetryMultiplier      float64 `yaml:"retry_multiplier" validate:"min=1"`
	FailureThreshold     int     `yaml:"failure_threshold" validate:"min=1"`
	SuccessThreshold     int     `yaml:"success_threshold" validate:"min=1"`
	CircuitTimeout       string  `yaml:"circuit_timeout"`
	CircuitResetTimeout  string  `yaml:"circuit_reset_timeout"`
}

// TracingConfig 追踪配置
type TracingConfig struct {
	Enabled             bool `yaml:"enabled"`
	HexGeneratorLength  int  `yaml:"hex_generator_length" validate:"min=8"`
	SequenceModulus     int  `yaml:"sequence_modulus" validate:"min=1000"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	WorkerPoolSize int    `yaml:"worker_pool_size" validate:"min=1"`
	QueueSize      int    `yaml:"queue_size" validate:"min=1"`
	BatchSize      int    `yaml:"batch_size" validate:"min=1"`
	FlushInterval  string `yaml:"flush_interval"`
}

// DebugConfig 调试配置
type DebugConfig struct {
	Enabled         bool `yaml:"enabled"`
	VerboseLogging  bool `yaml:"verbose_logging"`
	DumpRequests    bool `yaml:"dump_requests"`
	DumpResponses   bool `yaml:"dump_responses"`
	ProfileEnabled  bool `yaml:"profile_enabled"`
	ProfilePort     int  `yaml:"profile_port" validate:"min=1,max=65535"`
}

// PathsConfig 路径配置
type PathsConfig struct {
	BaseDir       string            `yaml:"base_dir"`
	ConfigDir     string            `yaml:"config_dir"`
	LogsDir       string            `yaml:"logs_dir"`
	DataDir       string            `yaml:"data_dir"`
	TempDir       string            `yaml:"temp_dir"`
	BackupDir     string            `yaml:"backup_dir"`
	FilePermission int              `yaml:"file_permission"`
	DirPermission  int              `yaml:"dir_permission"`
	Extensions     map[string]string `yaml:"extensions,omitempty"`
}

// SystemConfig 系统配置
type SystemConfig struct {
	OSDetection     bool                  `yaml:"os_detection"`
	ArchDetection   bool                  `yaml:"arch_detection"`
	SignalHandling  *SignalHandlingConfig `yaml:"signal_handling,omitempty"`
	Limits          *ResourceLimitsConfig `yaml:"limits,omitempty"`
}

// SignalHandlingConfig 信号处理配置
type SignalHandlingConfig struct {
	GracefulShutdown bool     `yaml:"graceful_shutdown"`
	ShutdownTimeout  string   `yaml:"shutdown_timeout"`
	Signals          []string `yaml:"signals,omitempty"`
}

// ResourceLimitsConfig 资源限制配置
type ResourceLimitsConfig struct {
	MaxMemory           string `yaml:"max_memory"`
	MaxCPUPercent       int    `yaml:"max_cpu_percent" validate:"min=1,max=100"`
	MaxFileDescriptors  int    `yaml:"max_file_descriptors" validate:"min=1"`
	MaxGoroutines       int    `yaml:"max_goroutines" validate:"min=1"`
}

// ProtocolsConfig 协议配置
type ProtocolsConfig struct {
	HTTP   *HTTPConfig  `yaml:"http,omitempty"`
	HTTPS  *HTTPSConfig `yaml:"https,omitempty"`
	SOCKS4 *SOCKS4Config `yaml:"socks4,omitempty"`
	SOCKS5 *SOCKS5Config `yaml:"socks5,omitempty"`
	DNS    *DNSProtocolConfig `yaml:"dns,omitempty"`
}

// HTTPConfig HTTP协议配置
type HTTPConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Version     string `yaml:"version" validate:"oneof=1.0 1.1 2.0"`
	KeepAlive   bool   `yaml:"keep_alive"`
	Compression bool   `yaml:"compression"`
}

// HTTPSConfig HTTPS协议配置
type HTTPSConfig struct {
	Enabled     bool   `yaml:"enabled"`
	Version     string `yaml:"version" validate:"oneof=1.0 1.1 2.0"`
	KeepAlive   bool   `yaml:"keep_alive"`
	Compression bool   `yaml:"compression"`
	VerifySSL   bool   `yaml:"verify_ssl"`
}

// SOCKS4Config SOCKS4协议配置
type SOCKS4Config struct {
	Enabled bool `yaml:"enabled"`
}

// SOCKS5Config SOCKS5协议配置
type SOCKS5Config struct {
	Enabled      bool `yaml:"enabled"`
	AuthRequired bool `yaml:"auth_required"`
}

// DNSProtocolConfig DNS协议配置
type DNSProtocolConfig struct {
	UDP   bool `yaml:"udp"`
	TCP   bool `yaml:"tcp"`
	TLS   bool `yaml:"tls"`
	HTTPS bool `yaml:"https"`
	DOH   bool `yaml:"doh"`
}

// PluginsConfig 插件配置
type PluginsConfig struct {
	Enabled   bool                     `yaml:"enabled"`
	Directory string                   `yaml:"directory"`
	AutoLoad  bool                     `yaml:"auto_load"`
	Available []PluginConfig           `yaml:"available,omitempty"`
}

// PluginConfig 单个插件配置
type PluginConfig struct {
	Name    string                 `yaml:"name"`
	Enabled bool                   `yaml:"enabled"`
	Config  map[string]interface{} `yaml:"config,omitempty"`
}

// DevelopmentConfig 开发配置
type DevelopmentConfig struct {
	Enabled   bool            `yaml:"enabled" default:"false"`
	Mode      string          `yaml:"mode" validate:"oneof=development testing production"`
	HotReload bool            `yaml:"hot_reload"`
	Testing   *TestingConfig  `yaml:"testing,omitempty"`
	Profiling *ProfilingConfig `yaml:"profiling,omitempty"`
}

// TestingConfig 测试配置
type TestingConfig struct {
	Enabled       bool   `yaml:"enabled"`
	MockResponses bool   `yaml:"mock_responses"`
	TestDataDir   string `yaml:"test_data_dir"`
}

// ProfilingConfig 性能分析配置
type ProfilingConfig struct {
	Enabled      bool `yaml:"enabled"`
	CPUProfile   bool `yaml:"cpu_profile"`
	MemoryProfile bool `yaml:"memory_profile"`
	BlockProfile bool `yaml:"block_profile"`
	MutexProfile bool `yaml:"mutex_profile"`
}

// CompressionConfig 压缩配置
type CompressionConfig struct {
	Enabled    bool     `yaml:"enabled"`
	Algorithms []string `yaml:"algorithms,omitempty" validate:"dive,oneof=gzip deflate br"`
	Level      int      `yaml:"level" validate:"min=-1,max=9"`
	MinSize    int      `yaml:"min_size" validate:"min=0"`
}

// HTTP2Config HTTP/2配置
type HTTP2Config struct {
	Enabled           bool `yaml:"enabled"`
	MaxConcurrentStreams uint32 `yaml:"max_concurrent_streams" validate:"min=1"`
	MaxFrameSize      uint32 `yaml:"max_frame_size" validate:"min=16384,max=16777215"`
	InitialWindowSize uint32 `yaml:"initial_window_size" validate:"min=65535"`
	ServerPush        bool   `yaml:"server_push"`
}