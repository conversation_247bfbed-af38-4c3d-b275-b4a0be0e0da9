package common

import (
	"testing"

	"github.com/mubeng/mubeng/common/constants"
)

// createValidGlobalConfig 创建一个有效的全局配置
func createValidGlobalConfig() GlobalConfig {
	return GlobalConfig{
		Enable:                   true,
		ProxyFile:               "test.txt",
		DefaultProcessStage:     constants.PreRequest,
		DNSLookupMode:          "system",
		IPRotationMode:         "sequential",
		MinProxyPoolSize:       1,
		MaxProxyFetchAttempts:  1,
		IPVersionPriority:      "ipv4",
		DefaultDNSTimeout:      5000,
		RetryProxyReusePolicy:  "allow",
	}
}

// TestValidatePriorityRange 测试优先级范围验证
func TestValidatePriorityRange(t *testing.T) {
	validator := NewConfigValidator()

	// 测试有效的优先级
	validConfig := &Config{
		Global: createValidGlobalConfig(),
		Events: []EventConfig{
			{
				Name:     "valid_priority",
				Enable:   true,
				Priority: 50,
				ProcessStage: constants.PreRequest,
				ConditionalActions: []ConditionalActionConfig{
					{
						ConditionName: "test_condition",
						StatusCodes: &StatusCodesConfig{
							Codes: []int{200},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
		},
		Actions: map[string]ActionSequenceName{
			"test_action": {
				Sequence: []ActionConfig{
					{
						Type: "log",
						Params: map[string]interface{}{
							"message": "test",
						},
					},
				},
			},
		},
	}

	err := validator.ValidateConfig(validConfig)
	if err != nil {
		t.Errorf("有效优先级配置验证失败: %v", err)
	}

	// 测试无效的优先级（超出范围）
	invalidConfig := *validConfig
	invalidConfig.Events[0].Priority = 150

	err = validator.ValidateConfig(&invalidConfig)
	if err == nil {
		t.Error("超出范围的优先级应该验证失败")
	}

	// 测试负数优先级
	invalidConfig.Events[0].Priority = -1
	err = validator.ValidateConfig(&invalidConfig)
	if err == nil {
		t.Error("负数优先级应该验证失败")
	}
}

// TestValidateProcessStage 测试处理阶段验证
func TestValidateProcessStage(t *testing.T) {
	validator := NewConfigValidator()

	// 测试有效的处理阶段
	validStages := []string{
		constants.PreRequest,
		constants.PostHeader,
		constants.PostBody,
		"", // 空值应该被允许
	}

	for _, stage := range validStages {
		config := &Config{
			Global: createValidGlobalConfig(),
			Events: []EventConfig{
				{
					Name:         "test_event",
					Enable:       true,
					Priority:     1,
					ProcessStage: stage,
					ConditionalActions: []ConditionalActionConfig{
						{
							ConditionName: "test_condition",
							StatusCodes: &StatusCodesConfig{
								Codes: []int{200},
							},
							ActionSequenceName: "test_action",
							Enable:             true,
						},
					},
				},
			},
			Actions: map[string]ActionSequenceName{
				"test_action": {
					Sequence: []ActionConfig{
						{
							Type: "log",
							Params: map[string]interface{}{
								"message": "test",
							},
						},
					},
				},
			},
		}

		err := validator.ValidateConfig(config)
		if err != nil {
			t.Errorf("有效处理阶段 '%s' 验证失败: %v", stage, err)
		}
	}

	// 测试无效的处理阶段
	invalidConfig := &Config{
		Global: createValidGlobalConfig(),
		Events: []EventConfig{
			{
				Name:         "test_event",
				Enable:       true,
				Priority:     1,
				ProcessStage: "invalid_stage",
				ConditionalActions: []ConditionalActionConfig{
					{
						ConditionName: "test_condition",
						StatusCodes: &StatusCodesConfig{
							Codes: []int{200},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
		},
		Actions: map[string]ActionSequenceName{
			"test_action": {
				Sequence: []ActionConfig{
					{
						Type: "log",
						Params: map[string]interface{}{
							"message": "test",
						},
					},
				},
			},
		},
	}

	err := validator.ValidateConfig(invalidConfig)
	if err == nil {
		t.Error("无效的处理阶段应该验证失败")
	}
}

// TestValidateEventConfiguration 测试事件配置验证
func TestValidateEventConfiguration(t *testing.T) {
	validator := NewConfigValidator()

	// 测试缺少触发条件的事件
	invalidConfig := &Config{
		Global: createValidGlobalConfig(),
		Events: []EventConfig{
			{
				Name:     "empty_event",
				Enable:   true,
				Priority: 1,
				ProcessStage: constants.PreRequest,
				// 没有任何触发条件
			},
		},
		Actions: map[string]ActionSequenceName{},
	}

	err := validator.ValidateConfig(invalidConfig)
	if err == nil {
		t.Error("缺少触发条件的事件应该验证失败")
	}

	// 测试新架构不完整的配置（只有Conditions没有Matches）
	invalidNewArchConfig := &Config{
		Global: createValidGlobalConfig(),
		Events: []EventConfig{
			{
				Name:     "incomplete_new_arch",
				Enable:   true,
				Priority: 1,
				ProcessStage: constants.PreRequest,
				Conditions: []ConditionConfig{
					{
						Name:   "test_condition",
						Enable: true,
						StatusCodes: &StatusCodesConfig{
							Codes: []int{200},
						},
					},
				},
				// 缺少Matches
			},
		},
		Actions: map[string]ActionSequenceName{},
	}

	err = validator.ValidateConfig(invalidNewArchConfig)
	if err == nil {
		t.Error("不完整的新架构配置应该验证失败")
	}
}

// TestValidatePriorityConflict 测试优先级冲突验证
func TestValidatePriorityConflict(t *testing.T) {
	validator := NewConfigValidator()

	// 测试同一阶段相同优先级的冲突
	conflictConfig := &Config{
		Global: createValidGlobalConfig(),
		Events: []EventConfig{
			{
				Name:     "event1",
				Enable:   true,
				Priority: 10,
				ProcessStage: constants.PreRequest,
				ConditionalActions: []ConditionalActionConfig{
					{
						ConditionName: "condition1",
						StatusCodes: &StatusCodesConfig{
							Codes: []int{200},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
			{
				Name:     "event2",
				Enable:   true,
				Priority: 10, // 相同优先级
				ProcessStage: constants.PreRequest, // 相同阶段
				ConditionalActions: []ConditionalActionConfig{
					{
						ConditionName: "condition2",
						StatusCodes: &StatusCodesConfig{
							Codes: []int{404},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
		},
		Actions: map[string]ActionSequenceName{
			"test_action": {
				Sequence: []ActionConfig{
					{
						Type: "log",
						Params: map[string]interface{}{
							"message": "test",
						},
					},
				},
			},
		},
	}

	err := validator.ValidateConfig(conflictConfig)
	if err == nil {
		t.Error("优先级冲突应该验证失败")
	}

	// 测试不同阶段相同优先级（应该允许）
	validConfig := *conflictConfig
	validConfig.Events[1].ProcessStage = constants.PostHeader

	err = validator.ValidateConfig(&validConfig)
	if err != nil {
		t.Errorf("不同阶段的相同优先级应该被允许: %v", err)
	}
}

// TestValidateDNSConfiguration 测试DNS配置验证
func TestValidateDNSConfiguration(t *testing.T) {
	validator := NewConfigValidator()

	// 测试DNS配置冲突（同时设置TTL和禁用缓存）
	conflictConfig := &Config{
		Global: createValidGlobalConfig(),
		Events: []EventConfig{
			{
				Name:         "dns_conflict",
				Enable:       true,
				Priority:     1,
				ProcessStage: constants.PreRequest,
				DNSCacheTTL:  3600,  // 设置了TTL
				DNSNoCache:   true,  // 同时禁用缓存
				ConditionalActions: []ConditionalActionConfig{
					{
						ConditionName: "test_condition",
						StatusCodes: &StatusCodesConfig{
							Codes: []int{200},
						},
						ActionSequenceName: "test_action",
						Enable:             true,
					},
				},
			},
		},
		Actions: map[string]ActionSequenceName{
			"test_action": {
				Sequence: []ActionConfig{
					{
						Type: "log",
						Params: map[string]interface{}{
							"message": "test",
						},
					},
				},
			},
		},
	}

	err := validator.ValidateConfig(conflictConfig)
	if err == nil {
		t.Error("DNS配置冲突应该验证失败")
	}
}
