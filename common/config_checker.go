package common

import (
	"fmt"
	"github.com/flexp/flexp/common/logger"
)

// ConfigChecker 配置检查器，用于检查各个模块的启用状态
type ConfigChecker struct {
	config *Config
	logger logger.Logger
}

// NewConfigChecker 创建配置检查器
func NewConfigChecker(config *Config) *ConfigChecker {
	return &ConfigChecker{
		config: config,
		logger: logger.GetLogger("config-checker"),
	}
}

// IsCacheEnabled 检查缓存是否启用
func (cc *ConfigChecker) IsCacheEnabled() bool {
	if cc.config == nil || cc.config.Cache == nil {
		return true // 默认启用
	}
	enabled := cc.config.Cache.Enabled
	if !enabled {
		cc.logger.Info("缓存功能已禁用")
	}
	return enabled
}

// IsLoggingEnabled 检查日志是否启用
func (cc *ConfigChecker) IsLoggingEnabled() bool {
	if cc.config == nil || cc.config.Logging == nil {
		return true // 默认启用
	}
	enabled := cc.config.Logging.Enabled
	if !enabled {
		// 注意：这里不能使用日志记录，因为日志本身可能被禁用
		fmt.Println("日志功能已禁用")
	}
	return enabled
}

// IsSecurityEnabled 检查安全功能是否启用
func (cc *ConfigChecker) IsSecurityEnabled() bool {
	if cc.config == nil || cc.config.Security == nil {
		return true // 默认启用
	}
	enabled := cc.config.Security.Enabled
	if !enabled {
		cc.logger.Info("安全功能已禁用")
	}
	return enabled
}

// IsProxyEnabled 检查代理功能是否启用
func (cc *ConfigChecker) IsProxyEnabled() bool {
	if cc.config == nil || cc.config.Proxy == nil {
		return true // 默认启用
	}
	enabled := cc.config.Proxy.Enabled
	if !enabled {
		cc.logger.Warn("代理功能已禁用 - 这可能影响核心功能")
	}
	return enabled
}

// IsAdvancedEnabled 检查高级功能是否启用
func (cc *ConfigChecker) IsAdvancedEnabled() bool {
	if cc.config == nil || cc.config.Advanced == nil {
		return false // 默认禁用
	}
	enabled := cc.config.Advanced.Enabled
	if enabled {
		cc.logger.Info("高级功能已启用")
	} else {
		cc.logger.Info("高级功能已禁用")
	}
	return enabled
}

// IsDevelopmentEnabled 检查开发功能是否启用
func (cc *ConfigChecker) IsDevelopmentEnabled() bool {
	if cc.config == nil || cc.config.Development == nil {
		return false // 默认禁用
	}
	enabled := cc.config.Development.Enabled
	if enabled {
		cc.logger.Info("开发功能已启用")
	} else {
		cc.logger.Info("开发功能已禁用")
	}
	return enabled
}

// IsMonitoringEnabled 检查监控功能是否启用
func (cc *ConfigChecker) IsMonitoringEnabled() bool {
	if cc.config == nil || cc.config.Monitoring == nil {
		return true // 默认启用
	}
	enabled := cc.config.Monitoring.Enabled
	if !enabled {
		cc.logger.Info("监控功能已禁用")
	}
	return enabled
}

// LogEnabledFeatures 记录所有启用的功能
func (cc *ConfigChecker) LogEnabledFeatures() {
	if cc.config == nil {
		cc.logger.Warn("配置为空，使用默认设置")
		return
	}

	features := []struct {
		name    string
		enabled bool
	}{
		{"缓存", cc.IsCacheEnabled()},
		{"日志", cc.IsLoggingEnabled()},
		{"安全", cc.IsSecurityEnabled()},
		{"代理", cc.IsProxyEnabled()},
		{"高级功能", cc.IsAdvancedEnabled()},
		{"开发功能", cc.IsDevelopmentEnabled()},
		{"监控", cc.IsMonitoringEnabled()},
	}

	enabledFeatures := []string{}
	disabledFeatures := []string{}

	for _, feature := range features {
		if feature.enabled {
			enabledFeatures = append(enabledFeatures, feature.name)
		} else {
			disabledFeatures = append(disabledFeatures, feature.name)
		}
	}

	if len(enabledFeatures) > 0 {
		cc.logger.Info(fmt.Sprintf("已启用功能: %v", enabledFeatures))
	}
	if len(disabledFeatures) > 0 {
		cc.logger.Info(fmt.Sprintf("已禁用功能: %v", disabledFeatures))
	}
}

// ValidateEnabledFeatures 验证启用的功能配置
func (cc *ConfigChecker) ValidateEnabledFeatures() error {
	if cc.config == nil {
		return fmt.Errorf("配置为空")
	}

	// 检查代理功能是否被禁用（这可能导致问题）
	if !cc.IsProxyEnabled() {
		cc.logger.Warn("警告：代理功能已禁用，这可能影响系统的核心功能")
	}

	// 检查日志功能是否被禁用
	if !cc.IsLoggingEnabled() {
		fmt.Println("警告：日志功能已禁用，调试可能会受到影响")
	}

	return nil
}
