package common

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

// TestConfigManagerCreation 测试配置管理器创建
func TestConfigManagerCreation(t *testing.T) {
	tempDir := t.TempDir()
	
	options := ConfigManagerOptions{
		MaxBackups:     5,
		BackupDir:      tempDir,
		EnableAutoSave: true,
		SaveInterval:   60,
	}
	
	cm := NewConfigManager(options)
	
	if cm == nil {
		t.<PERSON>rror("配置管理器创建失败")
	}
	
	if cm.maxBackups != 5 {
		t.<PERSON><PERSON><PERSON>("期望最大备份数为5，实际为%d", cm.maxBackups)
	}
	
	if cm.backupDir != tempDir {
		t.Errorf("期望备份目录为%s，实际为%s", tempDir, cm.backupDir)
	}
	
	// 验证备份目录是否创建
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		t.<PERSON><PERSON>("备份目录未创建")
	}
}

// TestConfigLoad 测试配置加载
func TestConfigLoad(t *testing.T) {
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "test_config.yaml")
	
	// 创建测试配置文件
	configContent := `
global:
  enable: true
  proxy_file: "test.txt"
  default_process_stage: "pre"
  dns_lookup_mode: "system"
  ip_rotation_mode: "sequential"
  min_proxy_pool_size: 1
  max_proxy_fetch_attempts: 1
  ip_version_priority: "ipv4"
  default_dns_timeout: 5000
  retry_proxy_reuse_policy: "allow"

events:
  - name: "test_event"
    enable: true
    priority: 1
    process_stage: "pre"
    conditional_actions:
      - condition_name: "test_condition"
        enable: true
        action_sequence_name: "test_action"
        status_codes:
          codes: [200]

actions:
  test_action:
    sequence:
      - type: "log"
        params:
          message: "test message"
`
	
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		t.Fatalf("创建测试配置文件失败: %v", err)
	}
	
	// 创建配置管理器
	cm := NewConfigManager(ConfigManagerOptions{
		MaxBackups: 3,
		BackupDir:  tempDir,
	})
	
	// 加载配置
	err := cm.LoadConfig(configPath)
	if err != nil {
		t.Fatalf("配置加载失败: %v", err)
	}
	
	// 验证配置
	config := cm.GetCurrentConfig()
	if config == nil {
		t.Error("当前配置为空")
	}
	
	if !config.Global.Enable {
		t.Error("全局配置应该启用")
	}
	
	version := cm.GetCurrentVersion()
	if version.Version != 1 {
		t.Errorf("期望版本号为1，实际为%d", version.Version)
	}
}

// TestConfigReload 测试配置热重载
func TestConfigReload(t *testing.T) {
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "test_config.yaml")
	
	// 创建初始配置文件
	initialConfig := `
global:
  enable: true
  proxy_file: "test.txt"
  default_process_stage: "pre"
  dns_lookup_mode: "system"
  ip_rotation_mode: "sequential"
  min_proxy_pool_size: 1
  max_proxy_fetch_attempts: 1
  ip_version_priority: "ipv4"
  default_dns_timeout: 5000
  retry_proxy_reuse_policy: "allow"

events:
  - name: "initial_event"
    enable: true
    priority: 1
    process_stage: "pre"
    conditional_actions:
      - condition_name: "initial_condition"
        enable: true
        action_sequence_name: "initial_action"
        status_codes:
          codes: [200]

actions:
  initial_action:
    sequence:
      - type: "log"
        params:
          message: "initial message"
`
	
	if err := os.WriteFile(configPath, []byte(initialConfig), 0644); err != nil {
		t.Fatalf("创建初始配置文件失败: %v", err)
	}
	
	// 创建配置管理器并加载初始配置
	cm := NewConfigManager(ConfigManagerOptions{
		MaxBackups: 3,
		BackupDir:  tempDir,
	})
	
	if err := cm.LoadConfig(configPath); err != nil {
		t.Fatalf("初始配置加载失败: %v", err)
	}
	
	initialVersion := cm.GetCurrentVersion().Version
	
	// 等待一小段时间确保时间戳不同
	time.Sleep(10 * time.Millisecond)
	
	// 创建新的配置文件
	newConfig := `
global:
  enable: true
  proxy_file: "test.txt"
  default_process_stage: "pre"
  dns_lookup_mode: "system"
  ip_rotation_mode: "sequential"
  min_proxy_pool_size: 1
  max_proxy_fetch_attempts: 1
  ip_version_priority: "ipv4"
  default_dns_timeout: 5000
  retry_proxy_reuse_policy: "allow"

events:
  - name: "reloaded_event"
    enable: true
    priority: 2
    process_stage: "pre"
    conditional_actions:
      - condition_name: "reloaded_condition"
        enable: true
        action_sequence_name: "reloaded_action"
        status_codes:
          codes: [404]

actions:
  reloaded_action:
    sequence:
      - type: "log"
        params:
          message: "reloaded message"
`
	
	if err := os.WriteFile(configPath, []byte(newConfig), 0644); err != nil {
		t.Fatalf("创建新配置文件失败: %v", err)
	}
	
	// 热重载配置
	err := cm.ReloadConfig(configPath)
	if err != nil {
		t.Fatalf("配置热重载失败: %v", err)
	}
	
	// 验证新配置
	config := cm.GetCurrentConfig()
	if len(config.Events) == 0 {
		t.Error("重载后事件列表为空")
	} else if config.Events[0].Name != "reloaded_event" {
		t.Errorf("期望事件名为'reloaded_event'，实际为'%s'", config.Events[0].Name)
	}
	
	newVersion := cm.GetCurrentVersion().Version
	if newVersion <= initialVersion {
		t.Errorf("重载后版本号应该增加，初始版本: %d，新版本: %d", initialVersion, newVersion)
	}
	
	// 验证备份历史
	history := cm.GetVersionHistory()
	if len(history) == 0 {
		t.Error("应该有备份历史记录")
	}
}

// TestConfigRollback 测试配置回滚
func TestConfigRollback(t *testing.T) {
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "test_config.yaml")
	
	// 创建初始配置
	initialConfig := `
global:
  enable: true
  proxy_file: "test.txt"
  default_process_stage: "pre"
  dns_lookup_mode: "system"
  ip_rotation_mode: "sequential"
  min_proxy_pool_size: 1
  max_proxy_fetch_attempts: 1
  ip_version_priority: "ipv4"
  default_dns_timeout: 5000
  retry_proxy_reuse_policy: "allow"

events:
  - name: "version1_event"
    enable: true
    priority: 1
    process_stage: "pre"
    conditional_actions:
      - condition_name: "version1_condition"
        enable: true
        action_sequence_name: "version1_action"
        status_codes:
          codes: [200]

actions:
  version1_action:
    sequence:
      - type: "log"
        params:
          message: "version 1 message"
`
	
	if err := os.WriteFile(configPath, []byte(initialConfig), 0644); err != nil {
		t.Fatalf("创建初始配置文件失败: %v", err)
	}
	
	// 创建配置管理器
	cm := NewConfigManager(ConfigManagerOptions{
		MaxBackups: 5,
		BackupDir:  tempDir,
	})
	
	// 加载初始配置
	if err := cm.LoadConfig(configPath); err != nil {
		t.Fatalf("初始配置加载失败: %v", err)
	}
	
	version1 := cm.GetCurrentVersion().Version
	
	// 创建备份
	if err := cm.createBackup("Manual backup v1"); err != nil {
		t.Fatalf("创建备份失败: %v", err)
	}
	
	// 模拟配置更新
	cm.currentVersion.Version = 2
	cm.currentVersion.Description = "Updated to v2"
	
	// 回滚到版本1
	err := cm.RollbackToVersion(version1)
	if err != nil {
		t.Fatalf("配置回滚失败: %v", err)
	}
	
	// 验证回滚结果
	currentVersion := cm.GetCurrentVersion()
	if currentVersion.Version != version1 {
		t.Errorf("回滚后版本号不正确，期望: %d，实际: %d", version1, currentVersion.Version)
	}
}

// TestReloadCallback 测试重载回调
func TestReloadCallback(t *testing.T) {
	tempDir := t.TempDir()
	
	cm := NewConfigManager(ConfigManagerOptions{
		MaxBackups: 3,
		BackupDir:  tempDir,
	})
	
	callbackExecuted := false
	var callbackConfig *Config
	
	// 注册回调
	cm.RegisterReloadCallback(func(config *Config) error {
		callbackExecuted = true
		callbackConfig = config
		return nil
	})
	
	// 创建测试配置
	configPath := filepath.Join(tempDir, "callback_test.yaml")
	configContent := `
global:
  enable: true
  proxy_file: "test.txt"
  default_process_stage: "pre"
  dns_lookup_mode: "system"
  ip_rotation_mode: "sequential"
  min_proxy_pool_size: 1
  max_proxy_fetch_attempts: 1
  ip_version_priority: "ipv4"
  default_dns_timeout: 5000
  retry_proxy_reuse_policy: "allow"

events: []
actions: {}
`
	
	if err := os.WriteFile(configPath, []byte(configContent), 0644); err != nil {
		t.Fatalf("创建测试配置文件失败: %v", err)
	}
	
	// 加载配置（应该触发回调）
	if err := cm.LoadConfig(configPath); err != nil {
		t.Fatalf("配置加载失败: %v", err)
	}
	
	// 验证回调是否执行
	if !callbackExecuted {
		t.Error("重载回调未执行")
	}
	
	if callbackConfig == nil {
		t.Error("回调中的配置为空")
	}
}
