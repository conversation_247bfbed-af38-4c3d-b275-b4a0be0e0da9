package constants

import "time"

// 应用程序常量
const (
	// 应用信息
	AppName    = "FlexProxy"
	AppVersion = "1.0.0"
	UserAgent  = AppName + "/" + AppVersion
)

// 网络相关常量
const (
	// 默认端口
	DefaultHTTPPort  = 8080
	DefaultHTTPSPort = 8443
	DefaultSOCKSPort = 1080
	
	// 超时设置
	DefaultConnectTimeout = 30 * time.Second
	DefaultReadTimeout    = 60 * time.Second
	DefaultWriteTimeout   = 60 * time.Second
	DefaultIdleTimeout    = 120 * time.Second
	
	// 连接池设置
	DefaultMaxIdleConns        = 100
	DefaultMaxIdleConnsPerHost = 10
	DefaultMaxConnsPerHost     = 50
	
	// 重试设置
	DefaultMaxRetries    = 3
	DefaultRetryInterval = 1 * time.Second
	MaxRetryInterval     = 30 * time.Second
	
	// 缓冲区大小
	DefaultBufferSize = 32 * 1024 // 32KB
	MaxBufferSize     = 1024 * 1024 // 1MB
	
	// 防抖延迟
	DefaultDebounceDelay = 100 * time.Millisecond
)

// 代理相关常量
const (
	// 代理类型
	ProxyTypeHTTP  = "http"
	ProxyTypeHTTPS = "https"
	ProxyTypeSOCKS = "socks"
	ProxyTypeSOCKS4 = "socks4"
	ProxyTypeSOCKS5 = "socks5"
	
	// 代理状态
	ProxyStatusActive   = "active"
	ProxyStatusInactive = "inactive"
	ProxyStatusError    = "error"
	ProxyStatusTesting  = "testing"
	
	// 代理选择策略
	StrategyRandom     = "random"
	StrategySequential = "sequential"
	StrategyQuality    = "quality"
	StrategySmart      = "smart"
	StrategyCustom     = "custom"
)

// 负载均衡常量
const (
	// 负载均衡算法
	LoadBalancerRoundRobin         = "round_robin"
	LoadBalancerWeightedRoundRobin = "weighted_round_robin"
	LoadBalancerLeastConnections   = "least_connections"
	LoadBalancerResponseTime       = "response_time"
	LoadBalancerIPHash             = "ip_hash"
	
	// 健康检查
	DefaultHealthCheckInterval = 30 * time.Second
	DefaultHealthCheckTimeout  = 5 * time.Second
	DefaultHealthCheckPath     = "/health"
	MaxConsecutiveFailures     = 3
	MaxConsecutiveSuccesses    = 2
)

// 缓存相关常量
const (
	// 缓存类型
	CacheTypeMemory = "memory"
	CacheTypeRedis  = "redis"
	CacheTypeFile   = "file"
	
	// 缓存默认设置
	DefaultCacheTTL      = 5 * time.Minute
	DefaultCacheTTLSeconds = 300 // 5分钟，单位秒
	DefaultCacheSize     = 1000
	DefaultCleanupInterval = 10 * time.Minute
	
	// 缓存键前缀
	CacheKeyProxyList   = "proxy:list"
	CacheKeyProxyStatus = "proxy:status:"
	CacheKeyUserSession = "user:session:"
	CacheKeyRateLimit   = "rate:limit:"
)

// 配置相关常量
const (
	// 配置文件路径
	DefaultConfigFile = "config.yaml"
	ConfigDirName     = "flexproxy"
	
	// 环境变量前缀
	EnvPrefix = "FLEXPROXY_"
	
	// 配置键
	ConfigKeyLogLevel    = "log.level"
	ConfigKeyLogFormat   = "log.format"
	ConfigKeyServerPort  = "server.port"
	ConfigKeyProxyList   = "proxy.list"
	ConfigKeyStrategy    = "proxy.strategy"
)

// 日志相关常量
const (
	// 日志级别
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
	LogLevelFatal = "fatal"
	
	// 日志格式
	LogFormatJSON = "json"
	LogFormatText = "text"
	
	// 时间格式
	TimeFormatDefault = "2006-01-02 15:04:05"
	TimeFormatISO8601 = "2006-01-02T15:04:05Z07:00"
	TimeFormatRFC3339 = time.RFC3339
	
	// 日志文件
	DefaultLogFile     = "flexproxy.log"
	DefaultLogMaxSize  = 100 // MB
	DefaultLogMaxAge   = 30  // 天
	DefaultLogMaxBackups = 10
)

// 注意：错误相关常量已迁移到 common/errors/errors.go 中统一管理

// HTTP 相关常量
const (
	// HTTP 方法
	MethodGET     = "GET"
	MethodPOST    = "POST"
	MethodPUT     = "PUT"
	MethodDELETE  = "DELETE"
	MethodPATCH   = "PATCH"
	MethodHEAD    = "HEAD"
	MethodOPTIONS = "OPTIONS"
	MethodCONNECT = "CONNECT"
	
	// HTTP 状态码
	StatusOK                  = 200
	StatusBadRequest          = 400
	StatusUnauthorized        = 401
	StatusForbidden           = 403
	StatusNotFound            = 404
	StatusMethodNotAllowed    = 405
	StatusTooManyRequests     = 429
	StatusInternalServerError = 500
	StatusBadGateway          = 502
	StatusServiceUnavailable  = 503
	StatusGatewayTimeout      = 504
	
	// HTTP 头
	HeaderContentType     = "Content-Type"
	HeaderContentLength   = "Content-Length"
	HeaderAuthorization   = "Authorization"
	HeaderUserAgent       = "User-Agent"
	HeaderXForwardedFor   = "X-Forwarded-For"
	HeaderXRealIP         = "X-Real-IP"
	HeaderXRequestID      = "X-Request-ID"
	HeaderXTraceID        = "X-Trace-ID"
	
	// Content-Type 值
	ContentTypeJSON = "application/json"
	ContentTypeXML  = "application/xml"
	ContentTypeForm = "application/x-www-form-urlencoded"
	ContentTypeText = "text/plain"
	ContentTypeHTML = "text/html"
)

// 监控和指标常量
const (
	// 指标名称
	MetricRequestTotal     = "requests_total"
	MetricRequestDuration  = "request_duration_seconds"
	MetricProxyStatus      = "proxy_status"
	MetricConnectionsActive = "connections_active"
	MetricErrorsTotal      = "errors_total"
	
	// 指标标签
	LabelMethod    = "method"
	LabelStatus    = "status"
	LabelProxy     = "proxy"
	LabelStrategy  = "strategy"
	LabelErrorType = "error_type"
)

// 安全相关常量
const (
	// 认证类型
	AuthTypeNone   = "none"
	AuthTypeBasic  = "basic"
	AuthTypeBearer = "bearer"
	AuthTypeAPIKey = "apikey"
	
	// 加密算法
	EncryptionAES256 = "aes256"
	EncryptionRSA    = "rsa"
	
	// 默认密钥长度
	DefaultKeyLength = 32
	
	// 令牌过期时间
	DefaultTokenExpiry = 24 * time.Hour
)

// 限流相关常量
const (
	// 限流算法
	RateLimitTokenBucket   = "token_bucket"
	RateLimitLeakyBucket   = "leaky_bucket"
	RateLimitFixedWindow   = "fixed_window"
	RateLimitSlidingWindow = "sliding_window"
	
	// 默认限流设置
	DefaultRateLimit     = 1000 // 每分钟请求数
	DefaultBurstSize     = 100
	DefaultWindowSize    = 1 * time.Minute
	DefaultCleanupPeriod = 5 * time.Minute
)

// 文件和路径常量
const (
	// 文件权限
	FilePermission = 0644
	DirPermission  = 0755
	
	// 文件扩展名
	ExtYAML = ".yaml"
	ExtYML  = ".yml"
	ExtJSON = ".json"
	ExtTOML = ".toml"
	ExtLog  = ".log"
	
	// 目录名称
	DirLogs    = "logs"
	DirConfig  = "config"
	DirData    = "data"
	DirTemp    = "temp"
	DirBackup  = "backup"
)

// 系统相关常量
const (
	// 操作系统
	OSWindows = "windows"
	OSLinux   = "linux"
	OSDarwin  = "darwin"
	
	// 架构
	ArchAMD64 = "amd64"
	ArchARM64 = "arm64"
	Arch386   = "386"
	
	// 信号
	SigTERM = "SIGTERM"
	SigINT  = "SIGINT"
	SigHUP  = "SIGHUP"
	SigUSR1 = "SIGUSR1"
	SigUSR2 = "SIGUSR2"
)

// DNS相关常量
const (
	// DNS服务器
	DefaultDNSServer = "*******:53"
	DefaultPublicDNS = "*******"
	DNSTimeout       = 5 * time.Second
	DefaultDNSTimeout = 5 * time.Second
	DNSRetries       = 3
	
	// DNS模式
	DNSModeLocal  = "local"
	DNSModeRemote = "remote"
	DNSModeCustom = "custom"
)

// 正则表达式常量
const (
	// IP 地址正则
	RegexIPv4 = `^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`
	RegexIPv6 = `^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$`
	
	// URL 正则
	RegexURL = `^https?://[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$`
	
	// 端口正则
	RegexPort = `^([1-9][0-9]{0,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$`
	
	// 域名正则
	RegexDomain = `^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?$`
)

// 策略相关常量
const (
	// 最小请求数阈值
	MinRequestsForStats = 10
	
	// 响应时间基准（毫秒）
	ResponseTimeBaseline = 5000
	
	// 质量评分相关
	DefaultQualityScore = 0.5
	SuccessRateWeight = 0.7
	ResponseTimeWeight = 0.3
	MaxFailureRate = 0.8
	TopProxyRatio = 0.3
	TopTargetsRatio = 0.3
	
	// 响应时间平滑因子
	ResponseTimeSmoothingFactor = 0.2
	
	// 代理池相关常量
	LatencySmoothingFactor = 0.2
	ErrorRateWeight = 0.6
	LatencyWeight = 0.4
	HighErrorRateThreshold = 0.5
	MediumErrorRateThreshold = 0.3
	LowErrorRateThreshold = 0.1
	
	// 代理管理器相关常量
	ProxyResponseTimeSmoothingFactor = 0.8
	ProxyLatencySmoothingFactor = 0.2
	ProxySuccessRateWeight = 0.7
	ProxyResponseTimeWeight = 0.3
	
	// 动作相关常量
	DefaultRequestTimeoutMS = 5000
	DefaultMaxRedirects = 10
	DefaultActionTimeoutMS = 10000
	
	// 错误恢复相关常量
	DefaultMaxRetryAttempts    = 3
	DefaultInitialRetryDelay   = 100 * time.Millisecond
	DefaultMaxRetryDelay       = 5 * time.Second
	DefaultRetryMultiplier     = 2.0
	DefaultFailureThreshold    = 5
	DefaultSuccessThreshold    = 3
	DefaultCircuitTimeout      = 30 * time.Second
	DefaultCircuitResetTimeout = 60 * time.Second

	// 追踪相关常量
	DefaultHexGeneratorLength = 16
	SequenceModulus          = 1000000
	
	// AWS URL 前缀
	AWSURLPrefix = "aws://"
	
	// 凭证分隔符
	CredentialsSeparator = "@"
	CredentialsDelimiter = ":"
	
	// SOCKS5 协议前缀
	SOCKS5Prefix = "socks"
	
	// 默认监控端口
	DefaultMonitoringPort = 9090
	
	// 默认监控路径
	DefaultMonitoringPath = "/metrics"
)

// 动作类型常量
const (
	// 基础动作类型（8种）
	ActionTypeLog            = "log"
	ActionTypeBanIP          = "banip"
	ActionTypeBanDomain      = "ban_domain"
	ActionTypeBlockRequest   = "block_request"
	ActionTypeModifyRequest  = "modify_request"
	ActionTypeModifyResponse = "modify_response"
	ActionTypeCacheResponse  = "cache_response"
	ActionTypeScript         = "script"

	// 扩展动作类型（8种）
	ActionTypeRetry          = "retry"
	ActionTypeRetrySame      = "retry_same"
	ActionTypeSaveToPool     = "save_to_pool"
	ActionTypeCache          = "cache"
	ActionTypeRequestURL     = "request_url"
	ActionTypeBanIPDomain    = "banipdomain"
	ActionTypeNullResponse   = "null_response"
	ActionTypeBypassProxy    = "bypass_proxy"
)

// 安全执行器相关常量
const (
	// 封禁时长常量（秒）
	DefaultBanDurationSeconds = 3600        // 默认1小时
	RebootBanDurationSeconds  = 86400       // 重启封禁24小时
	PermanentBanDurationSeconds = 31536000  // 永久封禁1年（近似）

	// 特殊时长值
	BanDurationReboot = "reboot"

	// 默认封禁原因
	DefaultBlockReason = "请求被阻止"
	DefaultBanReason   = "安全策略封禁"

	// HTTP状态码范围（使用已有的常量）
	// MinHTTPStatusCode = 100  // 已在Modify动作相关常量中定义
	// MaxHTTPStatusCode = 599  // 已在Modify动作相关常量中定义

	// 默认阻止状态码
	DefaultBlockStatusCode = 403 // Forbidden

	// HTTP头部名称常量
	HeaderXFlexProxyBlocked = "X-FlexProxy-Blocked"
	HeaderXFlexProxyReason  = "X-FlexProxy-Reason"
	HeaderCFConnectingIP    = "CF-Connecting-IP"
	HeaderTrueClientIP      = "True-Client-IP"
	HeaderXClientIP         = "X-Client-IP"
	HeaderXClusterClientIP  = "X-Cluster-Client-IP"

	// 默认封禁范围
	DefaultBanScope = "domain"

	// IP提取上下文键
	HTTPRequestContextKey = "http_request"
)



// 默认配置值
var DefaultConfig = map[string]interface{}{
	"server.host":                "0.0.0.0",
	"server.port":                DefaultHTTPPort,
	"server.read_timeout":        DefaultReadTimeout,
	"server.write_timeout":       DefaultWriteTimeout,
	"server.idle_timeout":        DefaultIdleTimeout,
	"server.max_header_bytes":    1 << 20, // 1MB
	
	"proxy.strategy":             StrategyRandom,
	"proxy.load_balancer":        LoadBalancerRoundRobin,
	"proxy.health_check.enabled": true,
	"proxy.health_check.interval": DefaultHealthCheckInterval,
	"proxy.health_check.timeout":  DefaultHealthCheckTimeout,
	"proxy.health_check.path":     DefaultHealthCheckPath,
	"proxy.max_retries":          DefaultMaxRetries,
	"proxy.retry_interval":       DefaultRetryInterval,
	
	"cache.type":                 CacheTypeMemory,
	"cache.ttl":                  DefaultCacheTTL,
	"cache.size":                 DefaultCacheSize,
	"cache.cleanup_interval":     DefaultCleanupInterval,
	
	"log.level":                  LogLevelInfo,
	"log.format":                 LogFormatJSON,
	"log.file":                   DefaultLogFile,
	"log.max_size":               DefaultLogMaxSize,
	"log.max_age":                DefaultLogMaxAge,
	"log.max_backups":            DefaultLogMaxBackups,
	
	"rate_limit.enabled":         false,
	"rate_limit.algorithm":       RateLimitTokenBucket,
	"rate_limit.rate":            DefaultRateLimit,
	"rate_limit.burst":           DefaultBurstSize,
	"rate_limit.window":          DefaultWindowSize,
	
	"auth.type":                  AuthTypeNone,
	"auth.token_expiry":          DefaultTokenExpiry,
	
	"monitoring.enabled":         false,
	"monitoring.port":            DefaultMonitoringPort,
	"monitoring.path":            DefaultMonitoringPath,
}

// DNS 服务相关常量
const (
	// DNS 缓存设置
	DefaultDNSCacheTTL = 5 * time.Minute
	
	// DNS 清理间隔
	DNSCleanupInterval = 10 * time.Minute
)

// Action 服务相关常量
const (
	// Action 队列设置
	DefaultActionQueueSize = 1000
	DefaultActionWorkers   = 10
	
	// Action 超时设置
	DefaultActionTimeout = 30 * time.Second
	
	// Action 重试设置
	DefaultActionMaxRetries = 3
	DefaultActionRetryDelay = 1 * time.Second
)

// Trigger 服务相关常量
const (
	// Trigger 检查间隔
	DefaultTriggerCheckInterval = 1 * time.Second
	
	// Trigger 运行间隔
	DefaultTriggerRunInterval = 5 * time.Second
)

// 代理服务相关常量
const (
	// 代理轮询间隔
	ProxyRotationInterval = 1
	
	// 代理健康检查
	ProxyHealthCheckInterval = 10 * time.Minute
	
	// 代理连接池大小
	DefaultProxyPoolSize = 100
)

// 执行器相关常量
const (
	// 重试执行器常量
	RetryExecutorMaxRetryCount = 10  // 最大重试次数
	RetryExecutorDefaultDelay  = "1s" // 默认重试延迟

	// 绕过代理执行器常量
	BypassProxyDefaultTimeout = 30000 // 默认超时时间(毫秒)
	BypassProxyMaxTimeout     = 300000 // 最大超时时间(毫秒)

	// 缓存响应执行器常量
	CacheResponseDefaultDuration = 300    // 默认缓存时间(秒)
	CacheResponseMaxDuration     = 86400  // 最大缓存时间(秒，24小时)
	CacheResponseDefaultKey      = "auto" // 默认缓存键

	// 保存到代理池执行器常量
	SaveToPoolDefaultQuality = "auto"     // 默认质量等级
	SaveToPoolDefaultScore   = 0.0        // 默认最小分数
	SaveToPoolDefaultPool    = "default"  // 默认代理池名称

	// 空响应执行器常量
	NullResponseDefaultStatus      = 200                    // 默认状态码
	NullResponseDefaultContentType = "application/json"    // 默认内容类型
	NullResponseDefaultBody        = `{"status":"success"}` // 默认响应体

	// 脚本执行器常量
	ScriptExecutorDefaultTimeout   = 30000  // 默认脚本执行超时时间(毫秒)
	ScriptExecutorMaxTimeout       = 300000 // 最大脚本执行超时时间(毫秒)
	ScriptExecutorDefaultEngine    = "javascript" // 默认脚本引擎
	ScriptExecutorMaxScriptSize    = 1048576 // 最大脚本大小(1MB)

	// 请求URL执行器常量
	RequestURLDefaultTimeout       = 30000 // 默认请求超时时间(毫秒)
	RequestURLMaxTimeout          = 300000 // 最大请求超时时间(毫秒)
	RequestURLDefaultMethod       = "GET"  // 默认HTTP方法
	RequestURLMaxRetries          = 3      // 最大重试次数
	RequestURLDefaultUserAgent    = "FlexProxy/1.0" // 默认User-Agent

	// 缓存执行器常量
	CacheExecutorDefaultDuration   = 300000 // 默认缓存时间(毫秒)
	CacheExecutorMaxDuration       = 86400000 // 最大缓存时间(毫秒，24小时)
	CacheExecutorDefaultMaxUse     = 0       // 默认最大使用次数(0表示无限制)
	CacheExecutorDefaultScope      = "url"   // 默认缓存作用域

	// IP域名封禁执行器常量
	BanIPDomainDefaultDuration     = 3600    // 默认封禁时间(秒，1小时)
	BanIPDomainMaxDuration         = 2592000 // 最大封禁时间(秒，30天)
	BanIPDomainDefaultReason       = "违规行为" // 默认封禁原因
	BanIPDomainMaxListSize         = 10000   // 最大封禁列表大小
)

// 执行器错误消息常量
const (
	// 基础执行器错误消息
	ErrMsgProxyServiceNotInitIP     = "ProxyService未初始化，无法执行IP封禁"
	ErrMsgProxyServiceNotInitDomain = "ProxyService未初始化，无法执行域名封禁"
	ErrMsgReadRequestBodyFailed     = "读取原始请求体失败: %v"
	ErrMsgReadResponseBodyFailed    = "读取原始响应体失败: %v"
	ErrMsgModifyRequestPanic        = "请求修改过程中发生panic: %v"
	ErrMsgModifyResponsePanic       = "响应修改过程中发生panic: %v"
	ErrMsgKeywordOperationFailed    = "关键字操作失败: %v"
	ErrMsgHeaderModifyFailed        = "请求头修改失败: %v"
	ErrMsgResponseHeaderModifyFailed = "响应头修改失败: %v"
	ErrMsgAdvancedBodyModifyFailed  = "高级请求体修改失败: %v"
	ErrMsgAdvancedResponseBodyModifyFailed = "高级响应体修改失败: %v"
	ErrMsgBodyModifyFailed          = "请求体修改失败: %v"
	ErrMsgResponseBodyModifyFailed  = "响应体修改失败: %v"
	ErrMsgInvalidStatusCode         = "无效的状态码: %d"

	// 重试执行器错误消息
	ErrMsgRetryCountInvalid      = "重试次数必须大于0"
	ErrMsgRetryCountExceedsLimit = "重试次数超过最大限制，已调整为: %d"
	ErrMsgRetryCountExceedsProxy = "重试次数超过可用代理数量，已调整为: %d"
	ErrMsgProxyServiceNotInit    = "ProxyService未初始化，无法执行代理切换重试"
	ErrMsgProxyPoolEmpty         = "代理池为空，无法执行代理切换重试"
	ErrMsgGetProxyFailed         = "获取代理失败，无法执行重试: %v"
	ErrMsgSetRetryContextFailed  = "设置重试上下文失败: %v"
	ErrMsgSetBypassContextFailed = "设置绕过代理上下文失败: %v"

	// 参数验证错误消息
	ErrMsgRetryCountFormatInvalid = "重试次数格式无效"
	ErrMsgDelayFormatInvalid      = "延迟参数格式无效"
	ErrMsgRotationModeInvalid     = "IP轮换模式无效"
	ErrMsgTimeoutFormatInvalid    = "超时参数格式无效"
	ErrMsgDNSModeInvalid          = "DNS模式无效"

	// 缓存响应执行器错误消息
	ErrMsgCacheServiceNotInit     = "CacheService未初始化，无法执行缓存操作"
	ErrMsgCacheDurationInvalid    = "缓存时间格式无效"
	ErrMsgCacheKeyInvalid         = "缓存键格式无效"
	ErrMsgCacheOperationFailed    = "缓存操作失败: %v"

	// 保存到代理池执行器错误消息
	ErrMsgQualityTierInvalid      = "质量等级格式无效"
	ErrMsgMinScoreInvalid         = "最小分数格式无效"
	ErrMsgPoolNameInvalid         = "代理池名称格式无效"
	ErrMsgSaveToPoolFailed        = "保存到代理池失败: %v"

	// 空响应执行器错误消息
	ErrMsgStatusCodeInvalid       = "状态码格式无效"
	ErrMsgContentTypeInvalid      = "内容类型格式无效"
	ErrMsgResponseBodyInvalid     = "响应体格式无效"
	ErrMsgNullResponseFailed      = "生成空响应失败: %v"

	// 脚本执行器错误消息
	ErrMsgScriptEngineInvalid     = "脚本引擎类型无效"
	ErrMsgScriptContentInvalid    = "脚本内容格式无效"
	ErrMsgScriptTimeoutInvalid    = "脚本超时时间格式无效"
	ErrMsgScriptExecutionFailed   = "脚本执行失败: %v"
	ErrMsgScriptFileMissing       = "脚本文件不存在: %s"
	ErrMsgScriptSizeExceeded      = "脚本大小超过限制"

	// 请求URL执行器错误消息
	ErrMsgURLInvalid              = "URL格式无效"
	ErrMsgHTTPMethodInvalid       = "HTTP方法格式无效"
	ErrMsgRequestTimeoutInvalid   = "请求超时时间格式无效"
	ErrMsgRequestRetriesInvalid   = "重试次数格式无效"
	ErrMsgRequestExecutionFailed  = "HTTP请求执行失败: %v"
	ErrMsgRequestHeadersInvalid   = "请求头格式无效"

	// 缓存执行器错误消息
	ErrMsgCacheDurationInvalidOld = "缓存时间格式无效"
	ErrMsgCacheMaxUseInvalid      = "最大使用次数格式无效"
	ErrMsgCacheScopeInvalid       = "缓存作用域格式无效"
	ErrMsgCacheExecutionFailed    = "缓存操作执行失败: %v"

	// IP域名封禁执行器错误消息
	ErrMsgBanTargetInvalid        = "封禁目标格式无效"
	ErrMsgBanDurationInvalidMsg   = "封禁时间格式无效"
	ErrMsgBanReasonInvalid        = "封禁原因格式无效"
	ErrMsgBanListSizeExceeded     = "封禁列表大小超过限制"
	ErrMsgBanExecutionFailed      = "封禁操作执行失败: %v"

	// 成功日志消息
	// 基础执行器日志消息
	LogMsgBlockRequestSuccess = "阻止请求动作已执行: reason=%s, status_code=%d"
	LogMsgModifyRequestStart = "开始修改HTTP请求: headers=%v, remove_headers=%v, body_length=%d, has_body_config=%v, has_keyword_operations=%v"
	LogMsgModifyRequestSuccess = "修改请求动作已执行: headers=%v, remove_headers=%v, body_length=%d"
	LogMsgModifyRequestComplete = "HTTP请求修改完成，成功执行步骤: %v"
	LogMsgModifyRequestRollback = "开始回退请求修改，失败步骤: %s，已完成步骤: %v"
	LogMsgModifyRequestRollbackComplete = "请求修改回退完成"
	LogMsgModifyResponseStart = "开始修改HTTP响应: headers=%v, remove_headers=%v, status_code=%d, body_length=%d, has_body_config=%v, has_keyword_operations=%v"
	LogMsgModifyResponseSuccess = "修改响应动作已执行: headers=%v, remove_headers=%v, status_code=%d, body_length=%d"
	LogMsgModifyResponseComplete = "HTTP响应修改完成，成功执行步骤: %v"
	LogMsgModifyResponseRollback = "开始回退响应修改，失败步骤: %s，已完成步骤: %v"
	LogMsgModifyResponseRollbackComplete = "响应修改回退完成"

	// 扩展执行器日志消息
	LogMsgRetrySameSuccess = "使用相同代理重试已配置: 重试次数=%d, 延迟=%s, 代理=%s"
	LogMsgRetrySameSuccessNoProxy = "使用相同代理重试已配置: 重试次数=%d, 延迟=%s"
	LogMsgRetrySuccess = "使用新代理重试已配置: 重试次数=%d, 延迟=%s, 轮换模式=%s, 可用代理数=%d"
	LogMsgBypassProxySuccess = "绕过代理已配置: 超时=%dms, 保持连接=%v, DNS模式=%s"
	LogMsgCacheResponseSuccess = "响应缓存已配置: 缓存时间=%ds, 缓存键=%s, 作用域=%s"
	LogMsgSaveToPoolSuccess = "保存到代理池已配置: 质量等级=%s, 最小分数=%.1f, 代理池=%s, 域名特定=%v"
	LogMsgNullResponseSuccess = "空响应已配置: 状态码=%d, 内容类型=%s, 响应体长度=%d"
	LogMsgScriptExecutorSuccess = "脚本执行已配置: 引擎=%s, 超时=%dms, 脚本类型=%s, 脚本长度=%d"
	LogMsgRequestURLSuccess = "URL请求已配置: 方法=%s, URL=%s, 超时=%dms, 重试次数=%d"
	LogMsgCacheExecutorSuccess = "缓存已配置: 时间=%dms, 最大使用=%d, 作用域=%s, 忽略参数=%v"
	LogMsgBanIPDomainSuccess = "IP域名封禁已配置: 目标=%s, 时间=%ds, 原因=%s, 类型=%s"

	// 警告日志消息
	LogMsgCacheTimeAdjusted = "缓存时间无效或超过限制，已调整为默认值: %d秒"
	LogMsgScriptTimeoutAdjusted = "脚本超时时间无效或超过限制，已调整为默认值: %dms"
	LogMsgBanTimeAdjusted = "封禁时间无效或超过限制，已调整为默认值: %ds"
	LogMsgProxyNotFoundForPool = "未找到当前代理信息，无法保存到代理池"
	LogMsgCacheTimeAdjustedMs = "缓存时间无效或超过限制，已调整为默认值: %dms"
	LogMsgRequestTimeoutAdjusted = "请求超时时间无效或超过限制，已调整为默认值: %dms"

	// 执行器描述消息
	// 基础执行器描述
	DescLogExecutor = "记录日志信息"
	DescBanIPExecutor = "封禁IP地址"
	DescBanDomainExecutor = "封禁域名"
	DescBlockRequestExecutor = "阻止请求"
	DescModifyRequestExecutor = "修改请求内容"
	DescModifyResponseExecutor = "修改响应内容"
	DescCacheResponseExecutor = "缓存HTTP响应内容"
	DescScriptExecutor = "执行JavaScript或Lua脚本"

	// 扩展执行器描述
	DescRetrySameExecutor = "使用相同代理重试请求"
	DescRetryExecutor = "使用新代理重试请求"
	DescBypassProxyExecutor = "绕过代理直接连接目标服务器"
	DescSaveToPoolExecutor = "保存代理到质量池"
	DescNullResponseExecutor = "返回空响应或自定义响应"
	DescRequestURLExecutor = "向指定URL发送HTTP请求"
	DescCacheExecutor = "缓存请求和响应数据"
	DescBanIPDomainExecutor = "封禁IP地址或域名"
)

// HTTP头部常量
const (
	// 重试相关头部
	HeaderRetrySame      = "X-FlexProxy-Retry-Same"
	HeaderRetryNew       = "X-FlexProxy-Retry-New"
	HeaderRetryCount     = "X-FlexProxy-Retry-Count"
	HeaderRetryDelay     = "X-FlexProxy-Retry-Delay"
	HeaderRetryProxy     = "X-FlexProxy-Retry-Proxy"
	HeaderRotationMode   = "X-FlexProxy-Rotation-Mode"

	// 绕过代理相关头部
	HeaderBypass         = "X-FlexProxy-Bypass"
	HeaderBypassTimeout  = "X-FlexProxy-Bypass-Timeout"
	HeaderBypassKeepAlive = "X-FlexProxy-Bypass-KeepAlive"
	HeaderBypassDNS      = "X-FlexProxy-Bypass-DNS"

	// 缓存响应相关头部
	HeaderCacheResponse  = "X-FlexProxy-Cache-Response"
	HeaderCacheDuration  = "X-FlexProxy-Cache-Duration"
	HeaderCacheKey       = "X-FlexProxy-Cache-Key"
	HeaderCacheScope     = "X-FlexProxy-Cache-Scope"

	// 保存到代理池相关头部
	HeaderSaveToPool     = "X-FlexProxy-Save-To-Pool"
	HeaderQualityTier    = "X-FlexProxy-Quality-Tier"
	HeaderMinScore       = "X-FlexProxy-Min-Score"
	HeaderPoolName       = "X-FlexProxy-Pool-Name"

	// 空响应相关头部
	HeaderNullResponse   = "X-FlexProxy-Null-Response"
	HeaderNullStatus     = "X-FlexProxy-Null-Status"
	HeaderNullContentType = "X-FlexProxy-Null-Content-Type"

	// 脚本执行器相关头部
	HeaderScriptExecutor = "X-FlexProxy-Script-Executor"
	HeaderScriptEngine   = "X-FlexProxy-Script-Engine"
	HeaderScriptTimeout  = "X-FlexProxy-Script-Timeout"
	HeaderScriptType     = "X-FlexProxy-Script-Type"

	// 请求URL执行器相关头部
	HeaderRequestURL     = "X-FlexProxy-Request-URL"
	HeaderRequestMethod  = "X-FlexProxy-Request-Method"
	HeaderRequestTimeout = "X-FlexProxy-Request-Timeout"
	HeaderRequestRetries = "X-FlexProxy-Request-Retries"

	// 缓存执行器相关头部
	HeaderCacheExecutor  = "X-FlexProxy-Cache-Executor"
	HeaderCacheMaxUse    = "X-FlexProxy-Cache-Max-Use"
	HeaderIgnoreParams   = "X-FlexProxy-Ignore-Params"

	// IP域名封禁相关头部
	HeaderBanIPDomain    = "X-FlexProxy-Ban-IP-Domain"
	HeaderBanTarget      = "X-FlexProxy-Ban-Target"
	HeaderBanDuration    = "X-FlexProxy-Ban-Duration"
	HeaderBanReason      = "X-FlexProxy-Ban-Reason"
)

// 注意：错误消息常量已迁移到 common/errors/errors.go 中统一管理

// 状态常量
const (
	// 服务状态
	ServiceStatusStarting = "starting"
	ServiceStatusRunning  = "running"
	ServiceStatusStopping = "stopping"
	ServiceStatusStopped  = "stopped"
	ServiceStatusError    = "error"

	// 任务状态
	TaskStatusPending   = "pending"
	TaskStatusRunning   = "running"
	TaskStatusCompleted = "completed"
	TaskStatusFailed    = "failed"
	TaskStatusCancelled = "cancelled"
)

// 检查器相关常量
const (
	// 检查状态
	CheckStatusLive = "LIVE"
	CheckStatusDied = "DIED"

	// 检查器默认设置
	DefaultGoroutines = 50
)

// 证书管理常量
const (
	// 证书目录和文件
	DefaultCertDir     = "./certs"
	CACertFile         = "ca.crt"
	CAKeyFile          = "ca.key"

	// 证书有效期
	CACertValidityYears = 10
	CertValidityDays    = 365

	// 证书权限
	CertFilePermission = 0644
	KeyFilePermission  = 0600

	// 证书组织信息
	CertOrganization = "FlexProxy"
	CertCountry      = "US"
	CertLocality     = "San Francisco"
	CertCommonName   = "FlexProxy Root CA"
)

// 服务器相关常量
const (
	// MIME类型
	MimeTextPlain = "text/plain"

	// 全局代理使用跟踪
	DefaultProxyCooldownMinutes = 5
	DefaultProxyCooldownSeconds = 300

	// 内存和性能阈值
	MemoryThresholdBytes    = 1024 * 1024 * 1024 // 1GB
	GoroutineThresholdCount = 1000
)

// 处理阶段常量
const (
	PreProcess        = "pre"         // 请求前处理
	PostHeaderProcess = "post_header" // 响应头处理
	PostBodyProcess   = "post_body"   // 响应体处理

	// 触发器处理阶段
	PreRequest = "pre"
	PostHeader = "post_header"
	PostBody   = "post_body"
)

// 触发器类型常量
const (
	TriggerTypeStatus         = "status"
	TriggerTypeBody           = "body"
	TriggerTypeMaxRequestTime = "max_request_time"
	TriggerTypeConnTimeOut    = "conn_time_out"
	TriggerTypeMinRequestTime = "min_request_time"
	TriggerTypeURL            = "url"
	TriggerTypeDomain         = "domain"
	TriggerTypeCombined       = "combined"
	TriggerTypeCustom         = "custom"
	TriggerTypeRequestBody    = "request_body"
	TriggerTypeRequestHeader  = "request_header"
	TriggerTypeResponseHeader = "response_header"
)

// 条件关系常量
const (
	ConditionRelationAND = "AND"
	ConditionRelationOR  = "OR"
	ConditionRelationNOT = "NOT"
)

// AWS 和代理网关常量
const (
	// AWS API Gateway
	AWSAPIGatewayStageName = "flexp-proxy-gateway"

	// 检查器端点
	DefaultIPInfoEndpoint = "https://ipinfo.io/json"

	// 环境变量
	EnvVarPrefix = "FLEXPROXY_"
)

// 调试和监控常量
const (
	// 调试服务
	DebugServiceName = "debug"

	// 断点相关
	BreakpointPrefix = "bp_"

	// 监控健康检查名称
	HealthCheckMemory     = "memory"
	HealthCheckGoroutines = "goroutines"

	// 性能分析
	ProfileCPU    = "cpu"
	ProfileMemory = "memory"
	ProfileBlock  = "block"
	ProfileMutex  = "mutex"
)

// 文件和目录权限常量
const (
	// 标准权限
	StandardFilePermission = 0644
	StandardDirPermission  = 0755
	SecureFilePermission   = 0600
	SecureDirPermission    = 0700
)

// 网络和协议常量
const (
	// 协议前缀
	HTTPPrefix  = "http://"
	HTTPSPrefix = "https://"

	// 特殊地址
	LocalhostIPv4 = "127.0.0.1"
	LocalhostIPv6 = "::1"
	AllInterfaces = "0.0.0.0"

	// 默认用户代理
	DefaultUserAgent = "FlexProxy/1.0.0"
)

// 协议常量
const (
	// 网络协议
	ProtocolHTTP   = "http"
	ProtocolHTTPS  = "https"
	ProtocolSOCKS4 = "socks4"
	ProtocolSOCKS5 = "socks5"
	ProtocolTCP    = "tcp"
	ProtocolUDP    = "udp"
	
	// DNS 协议
	DNSProtocolUDP   = "udp"
	DNSProtocolTCP   = "tcp"
	DNSProtocolTLS   = "tls"
	DNSProtocolHTTPS = "https"
	DNSProtocolDOH   = "doh"
)

// 操作符常量
const (
	// 比较操作符
	OperatorEquals       = "equals"
	OperatorNotEquals    = "not_equals"
	OperatorContains     = "contains"
	OperatorNotContains  = "not_contains"
	OperatorStartsWith   = "starts_with"
	OperatorEndsWith     = "ends_with"
	OperatorIn           = "in"
	OperatorNotIn        = "not_in"
	OperatorGreaterThan  = "greater_than"
	OperatorLessThan     = "less_than"
	OperatorRegex        = "regex"
)

// 字段名常量
const (
	// HTTP 字段
	FieldURL    = "url"
	FieldMethod = "method"
	FieldHeader = "header"
	FieldBody   = "body"
	FieldIP     = "ip"

	// 时间字段
	FieldHour     = "hour"
	FieldWeekday  = "weekday"
	FieldDate     = "date"
	FieldTime     = "time"

	// 响应字段
	FieldStatus      = "status"
	FieldContentType = "content_type"
	FieldSize        = "size"
)

// Modify动作相关常量
const (
	// 支持的内容格式
	FormatJSON       = "json"
	FormatXML        = "xml"
	FormatHTML       = "html"
	FormatText       = "text"
	FormatPlain      = "plain"
	FormatForm       = "form"
	FormatURLEncoded = "urlencoded"
	FormatBinary     = "binary"
	FormatOctet      = "octet"
	FormatAuto       = "auto"

	// 支持的编码格式
	EncodingUTF8   = "utf-8"
	EncodingBase64 = "base64"

	// 关键字操作类型
	OperationAdd     = "add"
	OperationReplace = "replace"
	OperationRemove  = "remove"
	OperationAppend  = "append"

	// 匹配类型
	MatchTypeExact    = "exact"
	MatchTypeContains = "contains"
	MatchTypeWildcard = "wildcard"
	MatchTypeRegex    = "regex"

	// 条件类型
	ConditionExists     = "exists"
	ConditionNotExists  = "not_exists"
	ConditionValueMatch = "value_match"

	// 默认值和限制
	MaxHeaderValueLength = 8192
	DefaultAutoContentType = true

	// HTTP状态码范围
	MinHTTPStatusCode = 100
	MaxHTTPStatusCode = 599
)

// 支持的格式列表（用于验证）
var ValidFormats = []string{
	FormatJSON, FormatXML, FormatHTML, FormatText, FormatPlain,
	FormatForm, FormatURLEncoded, FormatBinary, FormatOctet, FormatAuto,
}

// 支持的编码列表（用于验证）
var ValidEncodings = []string{
	EncodingUTF8, EncodingBase64,
}

// 支持的操作类型列表（用于验证）
var ValidOperations = []string{
	OperationAdd, OperationReplace, OperationRemove, OperationAppend,
}

// 支持的匹配类型列表（用于验证）
var ValidMatchTypes = []string{
	MatchTypeExact, MatchTypeContains, MatchTypeWildcard, MatchTypeRegex,
}

// 支持的条件类型列表（用于验证）
var ValidConditions = []string{
	ConditionExists, ConditionNotExists, ConditionValueMatch,
}