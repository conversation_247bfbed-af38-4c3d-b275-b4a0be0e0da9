package common

import (
	"fmt"
	"github.com/mubeng/mubeng/common/logger"
)

// ConfigApplier 配置应用器，根据配置创建相应的服务实例
type ConfigApplier struct {
	config  *Config
	checker *ConfigChecker
	logger  logger.Logger
}

// NewConfigApplier 创建配置应用器
func NewConfigApplier(config *Config) *ConfigApplier {
	return &ConfigApplier{
		config:  config,
		checker: NewConfigChecker(config),
		logger:  logger.GetLogger("config-applier"),
	}
}

// ServiceCreationConfig 服务创建配置
type ServiceCreationConfig struct {
	CacheEnabled       bool
	LoggingEnabled     bool
	SecurityEnabled    bool
	AdvancedEnabled    bool
	DevelopmentEnabled bool
}

// GetServiceCreationConfig 获取服务创建配置
func (ca *ConfigApplier) GetServiceCreationConfig() *ServiceCreationConfig {
	return &ServiceCreationConfig{
		CacheEnabled:       ca.checker.IsCacheEnabled(),
		LoggingEnabled:     ca.checker.IsLoggingEnabled(),
		SecurityEnabled:    ca.checker.IsSecurityEnabled(),
		AdvancedEnabled:    ca.checker.IsAdvancedEnabled(),
		DevelopmentEnabled: ca.checker.IsDevelopmentEnabled(),
	}
}

// GetSecurityConfig 获取安全配置
func (ca *ConfigApplier) GetSecurityConfig() *SecurityConfig {
	if ca.config.Security != nil {
		return ca.config.Security
	}

	// 返回默认安全配置
	return &SecurityConfig{
		Enabled: true,
		Auth: &AuthConfig{
			Type:        "none",
			TokenExpiry: "24h",
		},
		Encryption: &EncryptionConfig{
			Algorithm: "aes256",
			KeyLength: 32,
		},
	}
}

// GetAdvancedConfig 获取高级配置
func (ca *ConfigApplier) GetAdvancedConfig() *AdvancedConfig {
	if ca.config.Advanced != nil {
		return ca.config.Advanced
	}

	// 返回默认高级配置
	return &AdvancedConfig{
		Enabled: false,
		ErrorRecovery: &ErrorRecoveryConfig{
			MaxRetryAttempts:    3,
			InitialRetryDelay:   "1s",
			MaxRetryDelay:       "30s",
			RetryMultiplier:     2.0,
			FailureThreshold:    5,
			SuccessThreshold:    3,
			CircuitTimeout:      "60s",
			CircuitResetTimeout: "300s",
		},
	}
}

// GetDevelopmentConfig 获取开发配置
func (ca *ConfigApplier) GetDevelopmentConfig() *DevelopmentConfig {
	if ca.config.Development != nil {
		return ca.config.Development
	}

	// 返回默认开发配置
	return &DevelopmentConfig{
		Enabled:   false,
		Mode:      "production",
		HotReload: false,
		Testing: &TestingConfig{
			Enabled:       false,
			MockResponses: false,
			TestDataDir:   "./testdata",
		},
		Profiling: &ProfilingConfig{
			Enabled:       false,
			CPUProfile:    false,
			MemoryProfile: false,
			BlockProfile:  false,
			MutexProfile:  false,
		},
	}
}

// GetProxyConfig 获取代理配置
func (ca *ConfigApplier) GetProxyConfig() *ProxyConfig {
	if ca.config.Proxy != nil {
		return ca.config.Proxy
	}

	// 返回默认代理配置
	return &ProxyConfig{
		Enabled:      true,
		Strategy:     "random",
		MaxRetries:   3,
		PoolSize:     10,
	}
}

// ValidateProxyConfiguration 验证代理配置
func (ca *ConfigApplier) ValidateProxyConfiguration() error {
	if !ca.checker.IsProxyEnabled() {
		ca.logger.Warn("代理功能已禁用 - 这可能影响核心功能")
		return fmt.Errorf("代理功能已禁用")
	}

	ca.logger.Info("代理配置验证通过")
	return nil
}

// GetEnabledFeaturesSummary 获取启用功能的摘要
func (ca *ConfigApplier) GetEnabledFeaturesSummary() map[string]bool {
	return map[string]bool{
		"cache":       ca.checker.IsCacheEnabled(),
		"logging":     ca.checker.IsLoggingEnabled(),
		"security":    ca.checker.IsSecurityEnabled(),
		"proxy":       ca.checker.IsProxyEnabled(),
		"advanced":    ca.checker.IsAdvancedEnabled(),
		"development": ca.checker.IsDevelopmentEnabled(),
		"monitoring":  ca.checker.IsMonitoringEnabled(),
	}
}
