package errors

import (
	"context"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common/constants"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts int           `json:"max_attempts"`
	InitialDelay time.Duration `json:"initial_delay"`
	MaxDelay     time.Duration `json:"max_delay"`
	Multiplier   float64       `json:"multiplier"`
	Jitter       bool          `json:"jitter"`
}

// DefaultRetryConfig 默认重试配置
var DefaultRetryConfig = RetryConfig{
	MaxAttempts:  constants.DefaultMaxRetryAttempts,
	InitialDelay: constants.DefaultInitialRetryDelay,
	MaxDelay:     constants.DefaultMaxRetryDelay,
	Multiplier:   constants.DefaultRetryMultiplier,
	Jitter:       true,
}

// RetryableFunc 可重试的函数类型
type RetryableFunc func() error

// IsRetryable 判断错误是否可重试
type IsRetryable func(error) bool

// DefaultIsRetryable 默认重试判断逻辑
func DefaultIsRetryable(err error) bool {
	if flexErr, ok := err.(*FlexProxyError); ok {
		// 网络错误、超时错误、系统错误可重试
		return flexErr.Type == ErrTypeNetwork ||
			flexErr.Type == ErrTypeTimeout ||
			flexErr.Type == ErrTypeSystem
	}
	return false
}

// Retry 执行重试逻辑
func Retry(ctx context.Context, config RetryConfig, fn RetryableFunc, isRetryable IsRetryable) error {
	if isRetryable == nil {
		isRetryable = DefaultIsRetryable
	}
	
	var lastErr error
	delay := config.InitialDelay
	
	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return WrapError(ctx.Err(), ErrTypeSystem, ErrCodeSystemFailure, "context cancelled during retry")
		default:
		}
		
		err := fn()
		if err == nil {
			return nil // 成功
		}
		
		lastErr = err
		
		// 如果不可重试或已达到最大重试次数，直接返回
		if !isRetryable(err) || attempt == config.MaxAttempts {
			break
		}
		
		// 计算下次重试的延迟时间
		actualDelay := delay
		if config.Jitter {
			// 添加随机抖动，避免雷群效应
			actualDelay = time.Duration(float64(delay) * (0.5 + 0.5*float64(time.Now().UnixNano()%1000)/1000.0))
		}
		
		// 等待重试
		select {
		case <-ctx.Done():
			return WrapError(ctx.Err(), ErrTypeSystem, ErrCodeSystemFailure, "context cancelled during retry delay")
		case <-time.After(actualDelay):
		}
		
		// 计算下次延迟时间
		delay = time.Duration(float64(delay) * config.Multiplier)
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}
	}
	
	return WrapErrorWithDetails(lastErr, ErrTypeSystem, ErrCodeSystemFailure, 
		"retry failed after max attempts", 
		string(rune(config.MaxAttempts))+" attempts")
}

// CircuitBreakerState 熔断器状态
type CircuitBreakerState int

const (
	StateClosed CircuitBreakerState = iota // 关闭状态，正常工作
	StateOpen                              // 开启状态，拒绝请求
	StateHalfOpen                          // 半开状态，尝试恢复
)

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	FailureThreshold int           `json:"failure_threshold"`     // 失败阈值
	SuccessThreshold int           `json:"success_threshold"`     // 成功阈值（半开状态）
	Timeout          time.Duration `json:"timeout"`               // 超时时间
	ResetTimeout     time.Duration `json:"reset_timeout"`         // 重置超时时间
}

// DefaultCircuitBreakerConfig 默认熔断器配置
var DefaultCircuitBreakerConfig = CircuitBreakerConfig{
	FailureThreshold: constants.DefaultFailureThreshold,
	SuccessThreshold: constants.DefaultSuccessThreshold,
	Timeout:          constants.DefaultCircuitTimeout,
	ResetTimeout:     constants.DefaultCircuitResetTimeout,
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	config       CircuitBreakerConfig
	state        CircuitBreakerState
	failureCount int
	successCount int
	lastFailTime time.Time
	mu           sync.RWMutex
}

// NewCircuitBreaker 创建新的熔断器
func NewCircuitBreaker(config CircuitBreakerConfig) *CircuitBreaker {
	return &CircuitBreaker{
		config: config,
		state:  StateClosed,
	}
}

// Execute 执行函数，带熔断保护
func (cb *CircuitBreaker) Execute(fn RetryableFunc) error {
	cb.mu.Lock()
	defer cb.mu.Unlock()
	
	// 检查当前状态
	switch cb.state {
	case StateOpen:
		// 检查是否可以进入半开状态
		if time.Since(cb.lastFailTime) > cb.config.ResetTimeout {
			cb.state = StateHalfOpen
			cb.successCount = 0
		} else {
			return NewError(ErrTypeSystem, ErrCodeSystemFailure, "circuit breaker is open")
		}
	case StateHalfOpen:
		// 半开状态，允许少量请求通过
	}
	
	// 执行函数
	err := fn()
	
	if err != nil {
		// 执行失败
		cb.onFailure()
		return err
	}
	
	// 执行成功
	cb.onSuccess()
	return nil
}

// onFailure 处理失败情况
func (cb *CircuitBreaker) onFailure() {
	cb.failureCount++
	cb.lastFailTime = time.Now()
	
	if cb.state == StateHalfOpen {
		// 半开状态下失败，直接进入开启状态
		cb.state = StateOpen
		cb.successCount = 0
	} else if cb.failureCount >= cb.config.FailureThreshold {
		// 失败次数达到阈值，进入开启状态
		cb.state = StateOpen
	}
}

// onSuccess 处理成功情况
func (cb *CircuitBreaker) onSuccess() {
	cb.failureCount = 0
	
	if cb.state == StateHalfOpen {
		cb.successCount++
		if cb.successCount >= cb.config.SuccessThreshold {
			// 成功次数达到阈值，进入关闭状态
			cb.state = StateClosed
			cb.successCount = 0
		}
	}
}

// GetState 获取当前状态
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// GetStats 获取统计信息
func (cb *CircuitBreaker) GetStats() map[string]interface{} {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	
	return map[string]interface{}{
		"state":         cb.state,
		"failure_count": cb.failureCount,
		"success_count": cb.successCount,
		"last_fail_time": cb.lastFailTime,
	}
}