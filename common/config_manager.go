package common

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/mubeng/mubeng/common/errors"
	"github.com/mubeng/mubeng/common/logger"
	"gopkg.in/yaml.v3"
)

// ConfigVersion 配置版本信息
type ConfigVersion struct {
	Version     int       `json:"version"`
	Timestamp   time.Time `json:"timestamp"`
	Description string    `json:"description"`
	FilePath    string    `json:"file_path"`
	Checksum    string    `json:"checksum"`
}

// ConfigBackup 配置备份信息
type ConfigBackup struct {
	Version ConfigVersion `json:"version"`
	Config  *Config       `json:"config"`
	Data    []byte        `json:"data"` // 原始配置文件数据
}

// ConfigManager 配置管理器
type ConfigManager struct {
	currentConfig   *Config
	currentVersion  ConfigVersion
	backupHistory   []ConfigBackup
	maxBackups      int
	backupDir       string
	validator       *ConfigValidator
	mu              sync.RWMutex
	logger          *logger.LoggerAdapter
	reloadCallbacks []func(*Config) error
}

// ConfigManagerOptions 配置管理器选项
type ConfigManagerOptions struct {
	MaxBackups      int    `json:"max_backups"`
	BackupDir       string `json:"backup_dir"`
	EnableAutoSave  bool   `json:"enable_auto_save"`
	SaveInterval    int    `json:"save_interval_seconds"`
}

// NewConfigManager 创建新的配置管理器
func NewConfigManager(options ConfigManagerOptions) *ConfigManager {
	if options.MaxBackups <= 0 {
		options.MaxBackups = 10
	}
	if options.BackupDir == "" {
		options.BackupDir = "./config_backups"
	}

	// 确保备份目录存在
	os.MkdirAll(options.BackupDir, 0755)

	return &ConfigManager{
		maxBackups:      options.MaxBackups,
		backupDir:       options.BackupDir,
		validator:       NewConfigValidator(),
		backupHistory:   make([]ConfigBackup, 0),
		reloadCallbacks: make([]func(*Config) error, 0),
		logger:          logger.GetLoggerAdapter("CONFIG_MANAGER"),
	}
}

// LoadConfig 加载配置
func (cm *ConfigManager) LoadConfig(configPath string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cm.logger.Info("开始加载配置文件: %s", configPath)

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigFileNotFound,
			"无法读取配置文件",
			fmt.Sprintf("文件路径: %s, 错误: %v", configPath, err),
		)
	}

	// 解析配置
	config, err := ParseConfig(data)
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigParseError,
			"配置文件解析失败",
			fmt.Sprintf("文件路径: %s, 错误: %v", configPath, err),
		)
	}

	// 验证配置
	if err := cm.validator.ValidateConfig(config); err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeConfigValidationFailed,
			"配置验证失败",
			fmt.Sprintf("文件路径: %s, 错误: %v", configPath, err),
		)
	}

	// 计算校验和
	checksum := calculateChecksum(data)

	// 创建版本信息
	version := ConfigVersion{
		Version:     cm.getNextVersion(),
		Timestamp:   time.Now(),
		Description: "Initial load",
		FilePath:    configPath,
		Checksum:    checksum,
	}

	// 设置当前配置
	cm.currentConfig = config
	cm.currentVersion = version

	cm.logger.Info("配置加载成功，版本: %d", version.Version)
	return nil
}

// ReloadConfig 热重载配置（带回滚机制）
func (cm *ConfigManager) ReloadConfig(configPath string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cm.logger.Info("开始热重载配置文件: %s", configPath)

	// 备份当前配置
	if cm.currentConfig != nil {
		if err := cm.createBackup("Before reload"); err != nil {
			cm.logger.Warn("创建备份失败: %v", err)
		}
	}

	// 保存当前配置状态（用于回滚）
	previousConfig := cm.currentConfig
	previousVersion := cm.currentVersion

	// 读取新配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return cm.handleReloadError(
			errors.NewErrorWithDetails(
				errors.ErrTypeConfig,
				errors.ErrCodeConfigFileNotFound,
				"无法读取配置文件",
				fmt.Sprintf("文件路径: %s, 错误: %v", configPath, err),
			),
			previousConfig,
			previousVersion,
		)
	}

	// 解析新配置
	newConfig, err := ParseConfig(data)
	if err != nil {
		return cm.handleReloadError(
			errors.NewErrorWithDetails(
				errors.ErrTypeConfig,
				errors.ErrCodeConfigParseError,
				"配置文件解析失败",
				fmt.Sprintf("文件路径: %s, 错误: %v", configPath, err),
			),
			previousConfig,
			previousVersion,
		)
	}

	// 验证新配置
	if err := cm.validator.ValidateConfig(newConfig); err != nil {
		return cm.handleReloadError(
			errors.NewErrorWithDetails(
				errors.ErrTypeValidation,
				errors.ErrCodeConfigValidationFailed,
				"配置验证失败",
				fmt.Sprintf("文件路径: %s, 错误: %v", configPath, err),
			),
			previousConfig,
			previousVersion,
		)
	}

	// 计算校验和
	checksum := calculateChecksum(data)

	// 创建新版本信息
	newVersion := ConfigVersion{
		Version:     cm.getNextVersion(),
		Timestamp:   time.Now(),
		Description: "Hot reload",
		FilePath:    configPath,
		Checksum:    checksum,
	}

	// 原子性更新配置
	cm.currentConfig = newConfig
	cm.currentVersion = newVersion

	// 执行重载回调
	if err := cm.executeReloadCallbacks(newConfig); err != nil {
		cm.logger.Error("重载回调执行失败，开始回滚: %v", err)
		
		// 回滚到之前的配置
		cm.currentConfig = previousConfig
		cm.currentVersion = previousVersion
		
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigReloadFailed,
			"配置重载失败，已回滚到之前版本",
			fmt.Sprintf("回调错误: %v", err),
		)
	}

	cm.logger.Info("配置热重载成功，版本: %d -> %d", 
		previousVersion.Version, newVersion.Version)
	
	return nil
}

// RollbackToVersion 回滚到指定版本
func (cm *ConfigManager) RollbackToVersion(version int) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cm.logger.Info("开始回滚到版本: %d", version)

	// 查找指定版本的备份
	var targetBackup *ConfigBackup
	for i := range cm.backupHistory {
		if cm.backupHistory[i].Version.Version == version {
			targetBackup = &cm.backupHistory[i]
			break
		}
	}

	if targetBackup == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigVersionNotFound,
			"指定版本的配置备份不存在",
			fmt.Sprintf("版本: %d", version),
		)
	}

	// 验证备份配置
	if err := cm.validator.ValidateConfig(targetBackup.Config); err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeConfigValidationFailed,
			"备份配置验证失败",
			fmt.Sprintf("版本: %d, 错误: %v", version, err),
		)
	}

	// 备份当前配置
	if err := cm.createBackup("Before rollback"); err != nil {
		cm.logger.Warn("创建回滚前备份失败: %v", err)
	}

	// 原子性回滚
	cm.currentConfig = targetBackup.Config
	cm.currentVersion = targetBackup.Version
	cm.currentVersion.Description = fmt.Sprintf("Rollback to version %d", version)
	cm.currentVersion.Timestamp = time.Now()

	// 执行重载回调
	if err := cm.executeReloadCallbacks(cm.currentConfig); err != nil {
		cm.logger.Error("回滚后回调执行失败: %v", err)
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigReloadFailed,
			"回滚后回调执行失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	cm.logger.Info("配置回滚成功，当前版本: %d", cm.currentVersion.Version)
	return nil
}

// GetCurrentConfig 获取当前配置
func (cm *ConfigManager) GetCurrentConfig() *Config {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.currentConfig
}

// GetCurrentVersion 获取当前版本信息
func (cm *ConfigManager) GetCurrentVersion() ConfigVersion {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.currentVersion
}

// GetVersionHistory 获取版本历史
func (cm *ConfigManager) GetVersionHistory() []ConfigVersion {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	versions := make([]ConfigVersion, len(cm.backupHistory))
	for i, backup := range cm.backupHistory {
		versions[i] = backup.Version
	}
	return versions
}

// RegisterReloadCallback 注册重载回调
func (cm *ConfigManager) RegisterReloadCallback(callback func(*Config) error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.reloadCallbacks = append(cm.reloadCallbacks, callback)
}

// createBackup 创建配置备份
func (cm *ConfigManager) createBackup(description string) error {
	if cm.currentConfig == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigNotLoaded,
			"当前没有加载的配置",
			"无法创建备份",
		)
	}

	// 序列化当前配置
	data, err := json.MarshalIndent(cm.currentConfig, "", "  ")
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigSerializationFailed,
			"配置序列化失败",
			fmt.Sprintf("错误: %v", err),
		)
	}

	// 创建备份
	backup := ConfigBackup{
		Version: ConfigVersion{
			Version:     cm.currentVersion.Version,
			Timestamp:   time.Now(),
			Description: description,
			FilePath:    cm.currentVersion.FilePath,
			Checksum:    cm.currentVersion.Checksum,
		},
		Config: cm.currentConfig,
		Data:   data,
	}

	// 添加到历史记录
	cm.backupHistory = append(cm.backupHistory, backup)

	// 限制备份数量
	if len(cm.backupHistory) > cm.maxBackups {
		cm.backupHistory = cm.backupHistory[1:]
	}

	// 保存备份到文件
	backupPath := filepath.Join(cm.backupDir, fmt.Sprintf("config_v%d_%d.json",
		backup.Version.Version, backup.Version.Timestamp.Unix()))

	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		cm.logger.Warn("保存备份文件失败: %v", err)
	} else {
		cm.logger.Info("配置备份已保存: %s", backupPath)
	}

	return nil
}

// handleReloadError 处理重载错误并回滚
func (cm *ConfigManager) handleReloadError(err error, previousConfig *Config, previousVersion ConfigVersion) error {
	cm.logger.Error("配置重载失败，开始回滚: %v", err)

	// 回滚到之前的配置
	cm.currentConfig = previousConfig
	cm.currentVersion = previousVersion

	return err
}

// executeReloadCallbacks 执行重载回调
func (cm *ConfigManager) executeReloadCallbacks(config *Config) error {
	for i, callback := range cm.reloadCallbacks {
		if err := callback(config); err != nil {
			return errors.NewErrorWithDetails(
				errors.ErrTypeConfig,
				errors.ErrCodeConfigCallbackFailed,
				fmt.Sprintf("重载回调 %d 执行失败", i),
				fmt.Sprintf("错误: %v", err),
			)
		}
	}
	return nil
}

// getNextVersion 获取下一个版本号
func (cm *ConfigManager) getNextVersion() int {
	if cm.currentConfig == nil {
		return 1
	}
	return cm.currentVersion.Version + 1
}

// ExportBackup 导出备份到指定路径
func (cm *ConfigManager) ExportBackup(version int, exportPath string) error {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	// 查找指定版本的备份
	var targetBackup *ConfigBackup
	for i := range cm.backupHistory {
		if cm.backupHistory[i].Version.Version == version {
			targetBackup = &cm.backupHistory[i]
			break
		}
	}

	if targetBackup == nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigVersionNotFound,
			"指定版本的配置备份不存在",
			fmt.Sprintf("版本: %d", version),
		)
	}

	// 导出备份
	if err := os.WriteFile(exportPath, targetBackup.Data, 0644); err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigExportFailed,
			"配置导出失败",
			fmt.Sprintf("路径: %s, 错误: %v", exportPath, err),
		)
	}

	cm.logger.Info("配置备份已导出: %s", exportPath)
	return nil
}

// ImportBackup 从指定路径导入备份
func (cm *ConfigManager) ImportBackup(importPath string, description string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 读取备份文件
	data, err := os.ReadFile(importPath)
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigFileNotFound,
			"无法读取备份文件",
			fmt.Sprintf("路径: %s, 错误: %v", importPath, err),
		)
	}

	// 解析配置
	config, err := ParseConfig(data)
	if err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeConfig,
			errors.ErrCodeConfigParseError,
			"备份文件解析失败",
			fmt.Sprintf("路径: %s, 错误: %v", importPath, err),
		)
	}

	// 验证配置
	if err := cm.validator.ValidateConfig(config); err != nil {
		return errors.NewErrorWithDetails(
			errors.ErrTypeValidation,
			errors.ErrCodeConfigValidationFailed,
			"备份配置验证失败",
			fmt.Sprintf("路径: %s, 错误: %v", importPath, err),
		)
	}

	// 创建备份记录
	backup := ConfigBackup{
		Version: ConfigVersion{
			Version:     cm.getNextVersion(),
			Timestamp:   time.Now(),
			Description: description,
			FilePath:    importPath,
			Checksum:    calculateChecksum(data),
		},
		Config: config,
		Data:   data,
	}

	// 添加到历史记录
	cm.backupHistory = append(cm.backupHistory, backup)

	// 限制备份数量
	if len(cm.backupHistory) > cm.maxBackups {
		cm.backupHistory = cm.backupHistory[1:]
	}

	cm.logger.Info("配置备份已导入，版本: %d", backup.Version.Version)
	return nil
}

// CleanupOldBackups 清理旧备份
func (cm *ConfigManager) CleanupOldBackups(keepDays int) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cutoff := time.Now().AddDate(0, 0, -keepDays)
	var newHistory []ConfigBackup

	for _, backup := range cm.backupHistory {
		if backup.Version.Timestamp.After(cutoff) {
			newHistory = append(newHistory, backup)
		}
	}

	removed := len(cm.backupHistory) - len(newHistory)
	cm.backupHistory = newHistory

	cm.logger.Info("清理了 %d 个旧备份", removed)
	return nil
}

// calculateChecksum 计算数据的SHA256校验和
func calculateChecksum(data []byte) string {
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:])
}

// ParseConfig 解析配置数据
func ParseConfig(data []byte) (*Config, error) {
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("YAML解析失败: %v", err)
	}
	return &config, nil
}
